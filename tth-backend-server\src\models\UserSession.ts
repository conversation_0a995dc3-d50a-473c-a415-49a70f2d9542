"use strict";
import { Model, DataTypes } from "sequelize";
import { addActivity } from "../helper/queue.service";
import { sequelize } from "./index";


interface userSessionAttributes {
  id: number;
  user_id: number;
  device_type: string;
  token: number;
  created_by: number;
  updated_by: number;
}


export class UserSession
  extends Model<userSessionAttributes, never>
  implements userSessionAttributes {
  id!: number;
  user_id!: number;
  device_type!: string;
  token!: number;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }

}

UserSession.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
    },
    device_type: {
      type: DataTypes.STRING,
    },
    token: {
      type: DataTypes.TEXT('long'),
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_user_session",
    modelName: "UserSession",
    timestamps: true,
  },
);


UserSession.addHook("afterUpdate", async (userSession: any) => {
  await addActivity("UserSession", "updated", userSession);
});

UserSession.addHook("afterCreate", async (userSession: UserSession) => {
  await addActivity("UserSession", "created", userSession);
});

UserSession.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});