"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { HealthSafetyCategory } from "./HealthSafetyCategory";
import { Playlist } from "./Playlist";

interface healthSafetyPlaylistAttributes {
    health_safety_category_id: number;
    branch_id: number;
    playlist_id: number;
    status: string;
    created_by: number;
    updated_by: number;
}

export enum status {
    ACTIVE = "active",
    INACTIVE = "inactive",
}


export class HealthSafetyPlaylist
    extends Model<healthSafetyPlaylistAttributes, never>
    implements healthSafetyPlaylistAttributes {
    health_safety_category_id!: number;
    branch_id!: number;
    playlist_id!: number;
    status!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

HealthSafetyPlaylist.init(
    {
        health_safety_category_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        branch_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        playlist_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        status: {
            type: DataTypes.ENUM,
            values: Object.values(status),
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_health_safety_playlist",
        modelName: "healthSafetyPlaylist",
    },
);

HealthSafetyPlaylist.removeAttribute("id");
HealthSafetyCategory.hasMany(HealthSafetyPlaylist, {
    foreignKey: "health_safety_category_id",
    as: "health_safety_playlist_data",
});
HealthSafetyPlaylist.belongsTo(HealthSafetyCategory, {
    foreignKey: "health_safety_category_id",
    as: "health_safety_playlist_category",
});
Playlist.hasMany(HealthSafetyPlaylist, {
    foreignKey: "playlist_id",
    as: "health_playlist_data",
});
HealthSafetyPlaylist.belongsTo(Playlist, {
    foreignKey: "playlist_id",
    as: "health_safety_playlist",
});


// Define hooks for HealthSafety model
HealthSafetyPlaylist.addHook("afterUpdate", async (healthSafetyPlaylist: any) => {
    await addActivity("healthSafetyPlaylist", "updated", healthSafetyPlaylist);
});

HealthSafetyPlaylist.addHook("afterCreate", async (healthSafetyPlaylist: HealthSafetyPlaylist) => {
    await addActivity("healthSafetyPlaylist", "created", healthSafetyPlaylist);
});

HealthSafetyPlaylist.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});


