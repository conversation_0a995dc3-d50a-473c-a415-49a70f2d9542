export const thisIsAModule = true;

declare global {
  // eslint-disable-next-line no-var
  var config: any;
  // eslint-disable-next-line no-var
  var db: any;
  namespace Express {
    // eslint-disable-next-line @typescript-eslint/consistent-type-definitions
    interface Request {
      token: string;
      user: any;
      header: any;
    }
    interface Response {
      __: i18n;
    }
  }
}

export {};
