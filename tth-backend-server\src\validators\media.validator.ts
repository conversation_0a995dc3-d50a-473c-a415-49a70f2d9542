import { Segments, Joi, celebrate } from "celebrate";

export default {
  createCategory: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        category_name: Joi.string().required(),
        category_description: Joi.string().allow(null, ""),
        categoryStatus: Joi.string()
          .valid("active", "draft", "inactive")
          .required(),
        dashboard_view  : Joi.boolean()
      }),
    }),
  updateCategory: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        category_name: Joi.string().required(),
        category_description: Joi.string().allow(null, ""),
        categoryStatus: Joi.string().required(),
        dashboard_view  : Joi.boolean()
      }),
    }),
  changePlaylistMediaOrder: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        media_list: Joi.array()
          .required()
          .items(Joi.number().required())
          .min(1),
      }),
    }),
  updatePlaylistMediaTrack: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        media_list: Joi.array()
          .required()
          .items(Joi.number().required())
          .min(1),
        status: Joi.string()
          .valid("ongoing", "pending", "completed")
          .required(),
      }),
    }),
  createPlaylist: Joi.object().keys({
    playlist_name: Joi.string().required(),
    playlist_description: Joi.string().allow(null, ""),
    category_id: Joi.string().required(),
    department_id: Joi.string().required(),
    branch_id: Joi.string().required(),
    playlistStatus: Joi.string()
      .valid("active", "draft", "inactive")
      .required(),
  }),
  updatePlaylist: Joi.object().keys({
    playlist_name: Joi.string().required(),
    playlist_description: Joi.string().allow(null, ""),
    category_id: Joi.string().required(),
    department_id: Joi.string().required(),
    branch_id: Joi.string().required(),
    playlistStatus: Joi.string()
      .valid("active", "draft", "inactive")
      .required(),
    playlist_image: Joi.string().allow(null, ""),
  }),
  createMedia: Joi.object().keys({
    media_title: Joi.string().required(),
    media_description: Joi.string().allow(null, ""),
    // playlist_id: Joi.array().required().items(Joi.number().required()).min(1),
    playlist_id: Joi.string().required(),
    mediaStatus: Joi.string().valid("active", "draft", "inactive").required(),
    is_external_link: Joi.boolean().allow(null, ""),
    media_name: Joi.string().allow(null, ""),
    media_type: Joi.string().valid("image", "video", "audio", "pdf","docs" ,"youtube").allow(null, ""),
    media_upload: Joi.string().allow(null, ""),
    is_notify : Joi.string().allow(null,""),
    notification_content : Joi.string().allow(null,'')
  }),
  updateMedia: Joi.object().keys({
    media_title: Joi.string().required(),
    media_description: Joi.string().allow(null, ""),
    playlist_id: Joi.string().required(),
    mediaStatus: Joi.string().valid("active", "draft", "inactive").required(),
    is_external_link: Joi.boolean().allow(null, ""),
    media_name: Joi.string().allow(null, ""),
    media_type: Joi.string().valid("image", "video", "audio", "pdf","docs","youtube").allow(null, ""),
    media_upload: Joi.string().allow(null, ""),
  }),
  media_upload: Joi.any().allow(null, ""),
  playlist_image: Joi.any().allow(null, ""),
};
