"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface policySettingMetaAttributes {
    policy_setting_id: number;
    policy_setting_leval: number;
    user_id: number;

}



export class PolicySettingMeta
    extends Model<policySettingMetaAttributes, never>
    implements policySettingMetaAttributes {
    policy_setting_id!: number;
    policy_setting_leval!: number;
    user_id!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

PolicySettingMeta.init(
    {
        policy_setting_id: {
            type: DataTypes.INTEGER
        },
        policy_setting_leval: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        }
    },
    {
        sequelize: sequelize,
        tableName: "nv_policy_setting_meta",
        modelName: "PolicySettingMeta",
    },
);

PolicySettingMeta.removeAttribute("id");


PolicySettingMeta.addHook("afterUpdate", async (PolicySettingMeta: any) => {
    await addActivity("PolicySettingMeta", "updated", PolicySettingMeta);
});

PolicySettingMeta.addHook("afterCreate", async (policySettingMeta: PolicySettingMeta) => {
    await addActivity("PolicySettingMeta", "created", policySettingMeta);
});

PolicySettingMeta.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

