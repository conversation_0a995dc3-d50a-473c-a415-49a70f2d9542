"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { DocumentCategory } from "./DocumentCategory";

interface documentCategoryBranchAttributes {
  category_id: number;
  branch_id: number;
  document_category_branch_status: string;
  created_by: number;
  updated_by: number;
}

export enum document_category_branch_status {
  ACTIVE = "active",
  DRAFT = "draft",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export class DocumentCategoryBranch
  extends Model<documentCategoryBranchAttributes, never>
  implements documentCategoryBranchAttributes {
  category_id!: number;
  branch_id!: number;
  document_category_branch_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

DocumentCategoryBranch.init(
  {
    category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    branch_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    document_category_branch_status: {
      type: DataTypes.ENUM,
      values: Object.values(document_category_branch_status),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_document_category_branch",
    modelName: "DocumentCategoryBranch",
  },
);

DocumentCategoryBranch.removeAttribute("id");
// Branch.hasMany(DocumentCategoryBranch, { foreignKey: "branch_id", as: "branch_wise" });
// DocumentCategoryBranch.belongsTo(Branch, { foreignKey: "branch_id", as: "category_branches" });
DocumentCategory.hasMany(DocumentCategoryBranch, { foreignKey: "category_id" });
DocumentCategoryBranch.belongsTo(DocumentCategory, { foreignKey: "category_id" });

DocumentCategoryBranch.addHook("afterUpdate", async (documentCategoryBranch: any) => {
  await addActivity("DocumentCategoryBranch", "updated", documentCategoryBranch);
});

DocumentCategoryBranch.addHook(
  "afterCreate",
  async (documentCategoryBranch: DocumentCategoryBranch) => {
    await addActivity("DocumentCategoryBranch", "created", documentCategoryBranch);
  },
);

DocumentCategoryBranch.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
