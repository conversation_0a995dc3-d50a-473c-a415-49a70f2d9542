"use strict";

const { QueryTypes } = require("sequelize");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    const seederExists = await queryInterface.sequelize.query(
      `SELECT * FROM nv_users`,
      { type: QueryTypes.SELECT },
    );
    const userRoles = await queryInterface.sequelize.query(
      `SELECT * FROM nv_roles`,
      { type: QueryTypes.SELECT },
    );
    let superAdminRecord;

    if (seederExists.length == 0) {
      superAdminRecord = await queryInterface.bulkInsert("nv_users", [
        {
          user_first_name: "Super",
          user_last_name: "admin",
          user_email: "<EMAIL>",
          user_password:
            "sha1$100$16$YU5EcePGzFKdHbnwmXrlgQ==$4wr9t02F8E1ktPDmeA9EdA==",
          user_designation: "Super admin",
          user_status: "verified",
          user_active_role_id: 1,
          web_user_active_role_id: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
          employment_number: "000001"
        },
      ]);
      if (superAdminRecord && userRoles.length > 0) {
        await queryInterface.bulkInsert(
          "nv_user_roles",
          [
            {
              user_id: 1,
              role_id: 1,
            },
          ],
          {},
        );
      }
    }
  },
};
