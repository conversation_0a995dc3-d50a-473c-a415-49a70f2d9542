"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { CheckList } from "./CheckList";
import { addActivity } from "../helper/queue.service";

interface userCheckListAttributes {
  checklist_id: number;
  to_user_id: number;
  from_user_id: number;
  status: string;
  is_last_rejected: boolean;
  created_by: number;
  updated_by: number;
}

/** Role enum  for status*/
export enum check_list_status {
  COMPLETED = "completed",
  PENDING = "pending",
}

export class UserCheckList
  extends Model<userCheckListAttributes, never>
  implements userCheckListAttributes {
  checklist_id!: number;
  to_user_id!: number;
  from_user_id!: number;
  is_last_rejected!: boolean
  status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

UserCheckList.init(
  {
    checklist_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM,
      values: Object.values(check_list_status),
      defaultValue: check_list_status.PENDING,
    },
    to_user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    from_user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    is_last_rejected: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_user_checklist",
    modelName: "UserCheckList",
  },
);
UserCheckList.removeAttribute("id");
UserCheckList.belongsTo(CheckList, { foreignKey: "checklist_id", as: "user" });
CheckList.hasMany(UserCheckList, {
  foreignKey: "checklist_id",
  as: "userCheckList",
});
// UserCheckList.belongsTo(User, {
//   foreignKey: "from_user_id",
//   as: "from_user_list",
// });
// User.hasMany(UserCheckList, { foreignKey: "from_user_id", as: "from_user" });
// UserCheckList.belongsTo(User, { foreignKey: "to_user_id", as: "to_user_list" });
// User.hasMany(UserCheckList, { foreignKey: "to_user_id", as: "to_user_list" });

UserCheckList.addHook("afterUpdate", async (userCheckList: any) => {
  await addActivity("UserCheckList", "updated", userCheckList);
});

UserCheckList.addHook("afterCreate", async (userCheckList: UserCheckList) => {
  await addActivity("UserCheckList", "created", userCheckList);
});

UserCheckList.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
