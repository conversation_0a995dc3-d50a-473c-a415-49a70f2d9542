import { Segments, Jo<PERSON>, celebrate } from "celebrate";
export default {
  createBranch: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        branch_name: Joi.string().required(),
        branch_remark: Joi.string().allow(null, ""),
        branchStatus: Joi.string().required(),
        branch_color: Joi.string().allow(null)
      }),
    }),
  updateBranch: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        branch_name: Joi.string().required(),
        branch_remark: Joi.string().allow(null, ""),
        branchStatus: Joi.string().required(),
        branch_color: Joi.string().allow(null),
        branch_sign: Joi.string().allow(null),
        branch_employer_name: Joi.string().allow(null),
        branch_heading_employer_name: Joi.string().allow(null),
        branch_heading_name: Joi.string().allow(null),
        branch_work_place: Joi.string().allow(null),
        branch_heading_work_place: Joi.string().allow(null),
        registration_number: Joi.string().allow(null),
        text_color: Joi.string().allow(null),
      }),
    }),
};
