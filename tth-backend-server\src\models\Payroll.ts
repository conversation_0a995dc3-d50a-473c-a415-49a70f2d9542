"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface payrollAttributes {
    id: number;
    payroll_working_hour_epos: number;
    payroll_working_hour_non_epos: number;
    payroll_working_hour_training_non_epos: number;
    total_payroll_wage: number;
    payroll_status: string;
    created_by: number;
    updated_by: number;
}

export enum payroll_status {
    ACTIVE = "active",
    DRAFT = "draft",
    INACTIVE = "inactive",
    DELETED = "deleted",
}

export class Payroll
    extends Model<payrollAttributes, never>
    implements payrollAttributes {
    id!: number;
    payroll_working_hour_epos!: number;
    payroll_working_hour_non_epos!: number;
    payroll_working_hour_training_non_epos!: number;
    total_payroll_wage!: number;
    payroll_status!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

Payroll.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        payroll_working_hour_epos: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        payroll_working_hour_non_epos: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        payroll_working_hour_training_non_epos: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        total_payroll_wage: {
            type: DataTypes.FLOAT,
            allowNull: true,
        },
        payroll_status: {
            type: DataTypes.ENUM,
            values: Object.values(payroll_status),
            allowNull: true,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_payroll",
        modelName: "Payroll",
    },
);

Payroll.addHook("afterUpdate", async (payroll: any) => {
    await addActivity("Payroll", "updated", payroll);
});

Payroll.addHook("afterCreate", async (payroll: Payroll) => {
    await addActivity("Payroll", "created", payroll);
});

Payroll.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});
