"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { Forecast } from "./Forecast";

interface forecastMetaAttributes {
    id: number;
    forecast_category_type: string;
    forecast_type: string
    forecast_id: number;
    forecast_meta_category_status: string;
    payment_type_id: number;
    payment_type_category_id: string;
    created_by: number;
    updated_by: number;
}

export enum forecast_meta_category_status {
    ACTIVE = "active",
    INACTIVE = 'inactive'
}

export enum forecast_category_type {
    INCOME = "income",
    OTHER = "other",
    EXPENSE = "expense",
}



export enum forecast_type {
    BUGDET = "bugdet",
    COMPARISON = "comparison"
}


export class ForecastMeta
    extends Model<forecastMetaAttributes, never>
    implements forecastMetaAttributes {
    id!: number;
    forecast_category_type!: string;
    forecast_type!: string
    forecast_id!: number;
    forecast_meta_category_status!: string;
    payment_type_id!: number;
    payment_type_category_id!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

ForecastMeta.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        forecast_category_type: {
            type: DataTypes.ENUM,
            values: Object.values(forecast_category_type),
            allowNull: true
        },
        forecast_type: {
            type: DataTypes.ENUM,
            values: Object.values(forecast_type),
            allowNull: true
        },
        forecast_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        payment_type_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        payment_type_category_id: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        forecast_meta_category_status: {
            type: DataTypes.ENUM,
            values: Object.values(forecast_meta_category_status),
            allowNull: true
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_forecast_meta",
        modelName: "ForecastMeta",
    },
);


ForecastMeta.belongsTo(Forecast, { foreignKey: "forecast_id", as: "forecast" })
Forecast.hasMany(ForecastMeta, { foreignKey: "forecast_id", as: "forecast_meta" })


// Define hooks for Card model
ForecastMeta.addHook("afterUpdate", async (forecastMeta: any) => {
    await addActivity("ForecastMeta", "updated", forecastMeta);
});

ForecastMeta.addHook("afterCreate", async (forecastMeta: ForecastMeta) => {
    await addActivity("ForecastMeta", "created", forecastMeta);
});

ForecastMeta.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

