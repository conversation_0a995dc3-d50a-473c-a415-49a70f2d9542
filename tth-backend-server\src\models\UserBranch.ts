"use strict";
import { Model, Sequelize, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface userBranchAttributes {
  user_id: number;
  branch_id: number;
  created_by: number;
  createdAt: string;
}

export class UserBranch
  extends Model<userBranchAttributes>
  implements userBranchAttributes {
  user_id!: number;
  branch_id!: number;
  created_by!: number;
  createdAt!: string;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

UserBranch.init(
  {
    user_id: {
      type: DataTypes.INTEGER,
    },
    branch_id: {
      type: DataTypes.INTEGER,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_user_branches",
    modelName: "UserBranch",
    timestamps: false,
  },
);

UserBranch.removeAttribute("id");
// UserRole.belongsTo(User, { foreignKey: "user_id", as: "user" });
// User.hasMany(UserRole, { foreignKey: "user_id", as: "user_roles" });


UserBranch.addHook("afterUpdate", async (userBranch: any) => {
  await addActivity("UserBranch", "updated", userBranch);
});

UserBranch.addHook("afterCreate", async (userBranch: UserBranch) => {
  await addActivity("UserBranch", "created", userBranch);
});



UserBranch.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
