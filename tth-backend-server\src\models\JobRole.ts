"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface JobRoleAttributes {
    id: number;
    name: string;
    status: string;
    remark: string;
    created_by: number;
    updated_by: number;
}

export class JobRoleModel
    extends Model<JobRoleAttributes, never>
    implements JobRoleAttributes {
    public id!: number;
    public name!: string;
    public status!: string;
    public remark!: string;
    public created_by!: number;
    public updated_by!: number;

    public readonly createdAt!: Date;
    public readonly updatedAt!: Date;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

export enum status {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DRAFT = "draft",
    DELETED = "deleted"
}

JobRoleModel.init(
    {
        id: {
            type: DataTypes.INTEGER.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
        },
        name: {
            type: DataTypes.STRING(100),
            allowNull: false,
        },
        remark: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        status: {
            type: DataTypes.ENUM,
            values: Object.values(status),
            defaultValue: status.ACTIVE,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_job_role",
        modelName: "JobRoleModel",
    },
);


// Define hooks for contract type model
JobRoleModel.addHook("afterUpdate", async (JobRoleModel: any) => {
    await addActivity("JobRole", "updated", JobRoleModel);
});

JobRoleModel.addHook("afterCreate", async (JobRoleModel: JobRoleModel) => {
    await addActivity("JobRole", "created", JobRoleModel);
});

JobRoleModel.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});