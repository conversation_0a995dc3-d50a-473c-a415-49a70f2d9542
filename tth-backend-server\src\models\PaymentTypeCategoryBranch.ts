"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { PaymentTypeCategory } from "./PaymentTypeCategory";


interface PaymentTypeCategoryBranchAttributes {
  id: number;
  payment_type_category_id: number;
  branch_id: number;
  payment_type_category_branch_status: string;
  has_default_active: boolean;
  parent_id: number;
  category_branch_type: string;
  payment_type_category_branch_order: number;
  created_by: number;
  updated_by: number;
}

export enum payment_type_category_branch_status {
  ACTIVE = "active",
  INACTIVE = "inactive"
}

export enum category_branch_type {
  SEPARATE = "separate",
  COMMON = "common"
}


export class PaymentTypeCategoryBranch
  extends Model<PaymentTypeCategoryBranchAttributes, never>
  implements PaymentTypeCategoryBranchAttributes {
  id!: number;
  payment_type_category_id!: number;
  branch_id!: number;
  payment_type_category_branch_status!: string;
  has_default_active!: boolean;
  parent_id!: number;
  category_branch_type!: string;
  payment_type_category_branch_order!: number;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

PaymentTypeCategoryBranch.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    payment_type_category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    branch_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    payment_type_category_branch_status: {
      type: DataTypes.ENUM,
      values: Object.values(payment_type_category_branch_status),
      defaultValue: payment_type_category_branch_status.ACTIVE,
    },
    has_default_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    },
    parent_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    category_branch_type: {
      type: DataTypes.ENUM,
      values: Object.values(category_branch_type),
      allowNull: true,
    },
    payment_type_category_branch_order: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_payment_type_category_branch",
    modelName: "PaymentTypeCategoryBranch",
  },
);


PaymentTypeCategoryBranch.belongsTo(PaymentTypeCategory, { foreignKey: "payment_type_category_id", as: "payment_type_category_branch_list" });
PaymentTypeCategory.hasMany(PaymentTypeCategoryBranch, { foreignKey: "payment_type_category_id", as: "payment_type_branch_category" });



// Define hooks for Card model
PaymentTypeCategoryBranch.addHook("afterUpdate", async (paymentTypeCategoryBranch: any) => {
  await addActivity("PaymentTypeCategoryBranch", "updated", paymentTypeCategoryBranch);
});

PaymentTypeCategoryBranch.addHook("afterCreate", async (paymentTypeCategoryBranch: PaymentTypeCategoryBranch) => {
  await addActivity("PaymentTypeCategoryBranch", "created", paymentTypeCategoryBranch);
});

PaymentTypeCategoryBranch.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

