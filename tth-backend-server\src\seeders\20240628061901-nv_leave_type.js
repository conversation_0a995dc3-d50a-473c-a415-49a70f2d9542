"use strict";
const { QueryTypes } = require("sequelize");
module.exports = {
  async up(queryInterface, Sequelize) {
    const getReportFilterData = await queryInterface.sequelize.query(
      `SELECT * FROM nv_leave_type where has_annual_leave = true`,
      { type: QueryTypes.SELECT },
    );
    if (getReportFilterData.length === 0) {
      const insertData = [
        {
          name: "Annual leave",
          status: "active",
          has_annual_leave: true,
          leave_deduction_type: "paid",
          leave_type_color: "#ef1212",
          created_by: 1,
          updated_by: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];
      await queryInterface.bulkInsert("nv_leave_type", insertData, {});
    }
  },
};