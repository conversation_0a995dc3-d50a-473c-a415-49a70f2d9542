import { Joi } from "celebrate";
export default {
    sendChangeRequest: Joi.object().keys({
        change_request_subject: Joi.string().required(),
        old_data: Joi.string().required(),
        new_data: Joi.string().required(),
    }),
    updateChangeRequest: Joi.object().keys({
        change_request_subject: Joi.string().required(),
        old_data: Joi.string().required(),
        new_data: Joi.string().required(),
        change_request_remark : Joi.string().allow(null)
    }),
    approveRejectRequest: Joi.object().keys({
        change_request_remark : Joi.string().allow(null),
        change_request_status  :Joi.string().required()
    })
};
