"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

export enum restrict_by_gender_status {
    MALE = "male",
    FEMALE = "female",
    OTHER = "other",
}

export enum restrict_by_marital_status {
    MARRIED = "married",
    UNMARRIED = "unmarried",
}

export enum round_of_decimal_leave_balance {
    NO = "no",
    FULL_DAY = "full_day",
    HALF_DAY = "half_day",
}

interface leaveApplicationRulesPolicyAttributes {
    leave_accural_policy_id: number;
    allow_emp_for_half_day: boolean;
    allow_emp_for_short_leave: boolean;
    allow_emp_for_short_leave_count: number;
    has_document_required: boolean;
    limit_for_document_required: number;
    restrict_by_gender: boolean;
    restrict_by_gender_status: string;
    restrict_by_marital: boolean;
    restrict_by_marital_status: string;
    round_of_decimal_leave_balance: string;
    allow_leave_request_data: string;
}

export class LeaveApplicationRulesPolicy
    extends Model<leaveApplicationRulesPolicyAttributes, never>
    implements leaveApplicationRulesPolicyAttributes {
    leave_accural_policy_id!: number;
    allow_emp_for_half_day!: boolean;
    allow_emp_for_short_leave!: boolean;
    allow_emp_for_short_leave_count!: number;
    has_document_required!: boolean;
    limit_for_document_required!: number;
    restrict_by_gender!: boolean;
    restrict_by_gender_status!: string;
    restrict_by_marital!: boolean;
    restrict_by_marital_status!: string;
    round_of_decimal_leave_balance!: string;
    allow_leave_request_data!: string;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

LeaveApplicationRulesPolicy.init(
    {
        leave_accural_policy_id: {
            type: DataTypes.INTEGER
        },
        allow_emp_for_half_day: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        allow_emp_for_short_leave: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        allow_emp_for_short_leave_count: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        has_document_required: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        limit_for_document_required: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        restrict_by_gender: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        restrict_by_gender_status: {
            type: DataTypes.ENUM,
            values: Object.values(restrict_by_gender_status),
            allowNull: true,
        },
        restrict_by_marital: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        restrict_by_marital_status: {
            type: DataTypes.ENUM,
            values: Object.values(restrict_by_marital_status),
            allowNull: true,
        },
        round_of_decimal_leave_balance: {
            type: DataTypes.ENUM,
            values: Object.values(round_of_decimal_leave_balance),
            allowNull: true,
        },
        allow_leave_request_data: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_leave_application_rules_policy",
        modelName: "LeaveApplicationRulesPolicy",
    },
);

LeaveApplicationRulesPolicy.removeAttribute("id");


LeaveApplicationRulesPolicy.addHook("afterUpdate", async (LeaveApplicationRulesPolicy: any) => {
    await addActivity("LeaveApplicationRulesPolicy", "updated", LeaveApplicationRulesPolicy);
});

LeaveApplicationRulesPolicy.addHook("afterCreate", async (LeaveApplicationRulesPolicy: LeaveApplicationRulesPolicy) => {
    await addActivity("LeaveApplicationRulesPolicy", "created", LeaveApplicationRulesPolicy);
});

LeaveApplicationRulesPolicy.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

