"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface dashboardAttributes {
    id: number;
    dashboard_name: string;
    dashboard_status: string;
    has_dashboard_default: boolean;
    user_id: number;
    dashboard_filter: string;
    organization_id: string;
    created_by: number;
    updated_by: number;
}

export enum dashboard_status {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DELETED = 'deleted'
}

export class Dashboard
    extends Model<dashboardAttributes, never>
    implements dashboardAttributes {
    id!: number;
    dashboard_name!: string;
    dashboard_status!: string;
    has_dashboard_default!: boolean;
    user_id!: number;
    dashboard_filter!: string;
    organization_id!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

Dashboard.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        dashboard_name: {
            type: DataTypes.TEXT("long"),
            allowNull: false,
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        dashboard_status: {
            type: DataTypes.ENUM,
            values: Object.values(dashboard_status),
            defaultValue: dashboard_status.ACTIVE,
        },
        has_dashboard_default: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
        },
        dashboard_filter: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        organization_id: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_dashboards",
        modelName: "Dashboard",
    },
);
// Define hooks for Card model
Dashboard.addHook("afterUpdate", async (dashboard: any) => {
    await addActivity("Dashboard", "updated", dashboard);
});

Dashboard.addHook("afterCreate", async (dashboard: Dashboard) => {
    await addActivity("Dashboard", "created", dashboard);
});

Dashboard.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

