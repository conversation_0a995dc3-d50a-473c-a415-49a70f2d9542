"use strict";
const { QueryTypes } = require("sequelize");

module.exports = {
  async up(queryInterface, Sequelize) {
    const getRightWorkFormData = await queryInterface.sequelize.query(
      `SELECT * FROM nv_right_work_form_data`,
      { type: QueryTypes.SELECT },
    );

    if (getRightWorkFormData.length === 0) {
      const insertData = [{
        type: "step1_listA",
        field_type: "checkbox",
        title: "Step 1 - Obtain",
        sub_title: "List A",
        header: null,
        footer: null,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),

      },
      {
        type: "step1_listB_group1",
        field_type: "checkbox",
        title: "Step 1 - Obtain",
        sub_title: "List B Group 1",
        header: null,
        footer: null,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      }, {
        type: 'step1_listB_group2',
        field_type: "checkbox",
        title: "Step 1 - Obtain",
        sub_title: "List B Group 2",
        header: null,
        footer: null,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      }, {
        type: "step2_check",
        field_type: "option",
        title: "Step 2 - Check",
        sub_title: null,
        header: `You must <span class="fw600">check</span> that the documents are genuine and that the person presenting them is the prospective employee or employee, the rightful holder and allowed to do the type of work you are offering.`,
        footer: null,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      }, {
        type: "step3_copy",
        field_type: "checkbox",
        title: "Step 3 – Copy",
        sub_title: null,
        header: `You must make a <span class=\"fw600\">clear copy </span>of each document in a format which cannot manually be altered and retain the copy securely: electronically or in hardcopy. You must also retain a secure record of the date on which you made the check. Further information can be found under ‘Retaining Evidence’ in the employer’s guide at <a style=\"color: #0462c1;text-decoration: underline;\" href=\"https://assets.publishing.service.gov.uk/media/65cb49ec103de2000eb8f357/Final%2B-%2BGuidance%2BRight%2Bto%2Bwork%2Bchecks%2B-%2Ban%2Bemployer_s%2Bguide%2BClean%2BVersion__1_.pdf\" class=\"p14\" target=\"_blank\">Employer&#39;s guide to right to work checks.</a> <br>You must copy and retain copies of:`,
        footer: `All copies of documents taken should be kept securely for the duration of the worker’s employment and for two years afterwards. The copy must then be securely destroyed. We recommend you use our online interactive tool <a style="color: #0462c1;text-decoration: underline;" href="https://www.gov.uk/legal-right-work-uk" class="p14" target="_blank"> ‘Check if someone can work in the UK’,</a> which will take you through the process by asking you a series of questions`,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      }, {
        type: "know_type_of_excuse",
        field_type: "checkbox",
        title: "Know the type of excuse you have",
        sub_title: null,
        header: `If you have correctly carried out the above 3 steps you will have an excuse against liability for a civil penalty if the above named person is found working for you illegally. However, you need to be aware of the type of excuse you have as this determines how long it lasts for, and if, and when you are required to do a follow-up check.<br> The documents that you have checked and copied are from:`,
        footer: `<p class="p14" style="padding-left: 20px;">You will <span class="fw600">not</span> obtain a statutory excuse if: <br> </p> <ul id="l1" style="margin-bottom: 20px;" ><li data-list-text="" style="padding-top: 5px;"> <p class="p14" style="padding-left: 20px;"> it is reasonably apparent that the person presenting the document is not the person referred to in that document, even if the document itself is genuine</p> </li><li data-list-text=""><p class="p14" style="padding-left: 20px;">you know that the individual is not permitted to undertake the work in question</p></li><li data-list-text=""><p class="p14" style="padding-left: 20px;"> you know that the documents are false or do not rightfully belong to the holder.</p></li></ul>`,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },]


      await queryInterface.bulkInsert("nv_right_work_form_data", insertData, {});
    }
  },

  async down(queryInterface, Sequelize) {
    // Add commands to revert seed here.
    // Example: await queryInterface.bulkDelete('People', null, {});
  },
};


