import { Router } from "express";
import categoryController from "../../controller/documentCategory.controller";
import { multerS3 } from "../../helper/upload.service";
import { FILE_UPLOAD_CONSTANT } from "../../helper/constant";
const router: Router = Router();

const uploadService = multerS3(
  process.env.NODE_ENV!,
  FILE_UPLOAD_CONSTANT.DOCUMENT_CATEGORY_FILES.folder,
);

router.post(
  "/add-category",
  uploadService.fields([
    { name: "category_image", maxCount: 1 },
    { name: "item_list", maxCount: 10 },
  ]),
  categoryController.createCategory,
);

router.put(
  "/update-category/:category_id",
  uploadService.fields([
    { name: "category_image", maxCount: 1 },
    { name: "item_list", maxCount: 10 },
  ]),
  categoryController.updateCategory,
);

router.get("/get-category-list", categoryController.getCategoryList);

router.get("/get-all-category-list", categoryController.getAllCategoryList);

router.get("/get-own-category-list", categoryController.getOwnCategoryList);

router.get(
  "/get-all-own-category-list",
  categoryController.getAllOwnCategoryList,
);

router.get(
  "/get-category/:category_id",
  categoryController.getCategoryDetailsById,
);

router.post(
  "/update-category-item-order/",
  categoryController.updateCategoryItemOrder,
);

router.post("/update-item-order", categoryController.updateItemOrder);

router.post("/move-category", categoryController.moveCategory);

router.post("/delete-category", categoryController.deleteCategory);

router.post("/copy-category", categoryController.copyCategory);

router.get(
  "/get-user-statistics-list",
  categoryController.getUserStatisticsList,
);

router.post("/track-category", categoryController.trackCategory);

router.post("/restore-track-category", categoryController.restoreTrackCategory);

router.get(
  "/get-branch-category/:branch_id?",
  categoryController.getBranchCategoryList,
);

router.post(
  "/add-category-branch",
  categoryController.addCategoryIntoHealthCategory,
);

router.post(
  "/all-restore-track-category",
  categoryController.allRestoreTrackCategory,
);

router.get(
  "/get-category-track-activity/",
  categoryController.getUserCategoryTrackHistory,
);

router.post(
  "/add-multiple-category-branch",
  categoryController.addMultipleBranchIntoHealthCategory,
);

router.get(
  "/get-branch-list-category/:category_id",
  categoryController.getBranchDetailsByCategoryId,
);

export default router;
