"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { Resignation } from "./Resignation";

interface resignationRemarkAttributes {
  id: number;
  resignation_id: number;
  remarks: string;
  user_id: string;
  created_by: number;
  updated_by: number;
}

export class ResignationRemark
  extends Model<resignationRemarkAttributes, never>
  implements resignationRemarkAttributes {
  id!: number;
  resignation_id!: number;
  remarks!: string;
  user_id!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

ResignationRemark.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    resignation_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    remarks: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_resignation_remark",
    modelName: "ResignationRemark",
  },
);

// ResignationRemark.belongsTo(User, {
//   foreignKey: "user_id",
//   as: "resign_remark_user",
// });
// User.hasMany(ResignationRemark, {
//   foreignKey: "user_id",
//   as: "user_remark_detail",
// });

ResignationRemark.belongsTo(Resignation, {
  foreignKey: "resignation_id",
  as: "resignation_request",
});
Resignation.hasMany(ResignationRemark, {
  foreignKey: "resignation_id",
  as: "user_resignation_detail",
});


ResignationRemark.addHook("afterUpdate", async (resignationRemark: any) => {
  await addActivity("ResignationRemark", "updated", resignationRemark);
});

ResignationRemark.addHook("afterCreate", async (resignationRemark: ResignationRemark) => {
  await addActivity("ResignationRemark", "created", resignationRemark);
});

ResignationRemark.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
