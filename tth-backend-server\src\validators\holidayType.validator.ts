import { Segments, Joi, celebrate } from "celebrate";
export default {
  createHolidayType: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        holiday_type_name: Joi.string().required(),
        holiday_type_description: Joi.string().allow(null, ""),
        has_holiday_type_default: Joi.boolean().required()
      }),
    }),
  updateHolidayType: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        holiday_type_name: Joi.string().required(),
        holiday_type_description: Joi.string().allow(null, ""),
        has_holiday_type_default: Joi.boolean().required()
      }),
    }),
  ChangeHolidayTypeStatus: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        holiday_type_id: Joi.number().required(),
        holidayTypeStatus: Joi.string().required(),
      }),
    }),
  addHolidayPolicy: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        holiday_policy_name: Joi.string().required(),
        holiday_policy_colour: Joi.string().allow(null, ""),
        holiday_policy_description: Joi.string().allow(null, ""),
        holiday_policy_start_date: Joi.string().required(),
        holiday_policy_end_date: Joi.string().required(),
        has_leave_reprocess: Joi.boolean().allow(null, ""),
      }),
    }),
  updateHolidayPolicy: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        holiday_policy_name: Joi.string().required(),
        holiday_policy_colour: Joi.string().allow(null, ""),
        holiday_policy_description: Joi.string().allow(null, ""),
        holiday_policy_start_date: Joi.string().required(),
        holiday_policy_end_date: Joi.string().required(),
        has_leave_reprocess: Joi.boolean().allow(null, ""),
        holiday_type_id: Joi.number().required()
      }),
    }),
};
