"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface healthSafetyFormAttributes {
  id: number;
  checklist_id: number;
  user_id: number;
  status: string;
  created_by: number;
  updated_by: number;
}

export class HealthSafetyForm
  extends Model<healthSafetyFormAttributes, never>
  implements healthSafetyFormAttributes {
  id!: number;
  checklist_id!: number;
  user_id!: number;
  status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

export enum status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

HealthSafetyForm.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    checklist_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM,
      values: Object.values(status),
      defaultValue: status.ACTIVE,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_health_safety_form",
    modelName: "HealthSafetyForm",
  },
);

// Define hooks for HealthSafetyForm model
HealthSafetyForm.addHook("afterUpdate", async (healthSafetyForm: any) => {
  await addActivity("HealthSafetyForm", "updated", healthSafetyForm);
});

HealthSafetyForm.addHook(
  "afterCreate",
  async (healthSafetyForm: HealthSafetyForm) => {
    await addActivity("HealthSafetyForm", "created", healthSafetyForm);
  },
);

HealthSafetyForm.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

