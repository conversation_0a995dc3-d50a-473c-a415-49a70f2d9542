'use strict'
import { Model, DataTypes } from "sequelize"
import { sequelize } from "./index";


interface geoAttributes {
    place_code: string,
    geo_type: number,
    place_name: string,
    geo_order: number,
    parent_place: string,
    phone_code: string
}

export class Geo
    extends Model<geoAttributes, never>
    implements geoAttributes {
    place_code!: string
    geo_type!: number
    place_name!: string
    geo_order!: number
    parent_place!: string
    phone_code!: string
    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

Geo.init(
    {
        place_code: {
            type: DataTypes.STRING,
            primaryKey: true
        },
        geo_type: {
            type: DataTypes.INTEGER,
        },
        place_name: {
            type: DataTypes.STRING,
        },
        geo_order: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        parent_place: {
            type: DataTypes.STRING,
        },
        phone_code: {
            type: DataTypes.STRING,
            allowNull: true
        }
    },
    {
        sequelize: sequelize,
        tableName: "nv_geo",
        modelName: "Geo",
    },
);


