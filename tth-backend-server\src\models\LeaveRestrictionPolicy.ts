"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

export enum maximum_leave_allowed_for_period_type {
    DAYS = "days",
    WEEKLY = 'weekly',
    QUARTERLY = 'quarterly',
    MONTHLY = "monthly",
    YEARLY = "yearly",
}


export enum maximum_leave_balance_for_period_type {
    DAYS = "days",
    WEEKLY = 'weekly',
    QUARTERLY = 'quarterly',
    MONTHLY = "monthly",
    YEARLY = "yearly",
}

interface leaveRestrictionPolicyAttributes {
    leave_accural_policy_id: number;
    has_maximum_consecutive: boolean;
    maximum_consecutive_count: number;
    has_allow_gap_between_leave: boolean;
    gap_between_leave_application: number;
    emp_can_apply_in_notice_period: boolean;
    extend_notice_period: boolean;
    club_leave_with_other_leave_policy: boolean;
    club_leave_with_other_leave_policy_id: string;
    allow_view_for_same_leave_type: boolean;
    maximum_leave_allowed_for_period_count: number;
    maximum_leave_allowed_for_period_type: string;
    maximum_leave_balance_for_period_days: number;
    maximum_leave_balance_for_period_type: string;
    bind_leave_policy_id: string;
}

export class LeaveRestrictionPolicy
    extends Model<leaveRestrictionPolicyAttributes, never>
    implements leaveRestrictionPolicyAttributes {
    leave_accural_policy_id!: number;
    has_maximum_consecutive!: boolean;
    maximum_consecutive_count!: number;
    has_allow_gap_between_leave!: boolean;
    gap_between_leave_application!: number;
    emp_can_apply_in_notice_period!: boolean;
    extend_notice_period!: boolean;
    club_leave_with_other_leave_policy!: boolean;
    club_leave_with_other_leave_policy_id!: string;
    allow_view_for_same_leave_type!: boolean;
    maximum_leave_allowed_for_period_count!: number;
    maximum_leave_allowed_for_period_type!: string;
    maximum_leave_balance_for_period_days!: number;
    maximum_leave_balance_for_period_type!: string;
    bind_leave_policy_id!: string;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

LeaveRestrictionPolicy.init(
    {
        leave_accural_policy_id: {
            type: DataTypes.INTEGER
        },
        has_maximum_consecutive: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        maximum_consecutive_count: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        has_allow_gap_between_leave: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        gap_between_leave_application: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        emp_can_apply_in_notice_period: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        extend_notice_period: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        club_leave_with_other_leave_policy: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        club_leave_with_other_leave_policy_id: {
            type: DataTypes.STRING,
            allowNull: true
        },
        allow_view_for_same_leave_type: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        maximum_leave_allowed_for_period_count: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        maximum_leave_allowed_for_period_type: {
            type: DataTypes.ENUM,
            values: Object.values(maximum_leave_allowed_for_period_type),
            allowNull: true,
        },
        maximum_leave_balance_for_period_days: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        maximum_leave_balance_for_period_type: {
            type: DataTypes.ENUM,
            values: Object.values(maximum_leave_balance_for_period_type),
            allowNull: true,
        },
        bind_leave_policy_id: {
            type: DataTypes.STRING,
            allowNull: true
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_leave_restriction_policy",
        modelName: "LeaveRestrictionPolicy",
    },
);

LeaveRestrictionPolicy.removeAttribute("id");


LeaveRestrictionPolicy.addHook("afterUpdate", async (LeaveRestrictionPolicy: any) => {
    await addActivity("LeaveRestrictionPolicy", "updated", LeaveRestrictionPolicy);
});

LeaveRestrictionPolicy.addHook("afterCreate", async (LeaveRestrictionPolicy: LeaveRestrictionPolicy) => {
    await addActivity("LeaveRestrictionPolicy", "created", LeaveRestrictionPolicy);
});

LeaveRestrictionPolicy.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

