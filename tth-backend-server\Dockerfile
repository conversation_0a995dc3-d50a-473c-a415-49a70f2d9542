FROM node:20 AS build_image

# Set the working directory
WORKDIR /usr/src/backend-ms
RUN apt-get update && apt-get install -y libx11-xcb1 libxcomposite1 libxdamage1 libxrandr2 libgbm1  libasound2 libatk1.0-0 libatk-bridge2.0-0 libcups2 libdbus-1-3 libdrm2  libfontconfig1 libgtk-3-0 libnspr4 libnss3 libxshmfence1 xdg-utils
# Copy only package.json and package-lock.json to install dependencies
COPY package.json ./

RUN npm install pnpm -g

# Install dependencies
RUN npm install -f --legacy-peer-deps
RUN npm install -f axios
RUN npm install -D @types/body-parser


# Copy the rest of the application files
COPY . .

# Build the Node.js app
RUN yarn build
# Optional: install Chrome for Puppeteer                                                                                                                                                                                                     
RUN npx puppeteer browsers install chrome
# Copy the email templates into the build directory after building the app
# COPY ./src/email_templates /usr/src/backend-ms/build/email_templates

# remove dev dependencies
# RUN npm prune --production

# Stage 2: Production Image
FROM node:20

# Set the working directory
WORKDIR /usr/src/backend-ms
RUN apt-get update && apt-get install -y libx11-xcb1 libxcomposite1 libxdamage1 libxrandr2 libgbm1  libasound2 libatk1.0-0 libatk-bridge2.0-0 libcups2 libdbus-1-3 libdrm2  libfontconfig1 libgtk-3-0 libnspr4 libnss3 libxshmfence1 xdg-utils
# Install production dependencies only
COPY --from=build_image /usr/src/backend-ms/package.json ./package.json
COPY --from=build_image /usr/src/backend-ms/node_modules ./node_modules
COPY --from=build_image /usr/src/backend-ms/build ./build
COPY --from=build_image /usr/src/backend-ms/src ./src
COPY --from=build_image /root/.cache/puppeteer /root/.cache/puppeteer

# Expose the port that the Node.js app runs on
EXPOSE 8029

# Start the Node.js app
CMD ["node", "./build/index.js"]]
