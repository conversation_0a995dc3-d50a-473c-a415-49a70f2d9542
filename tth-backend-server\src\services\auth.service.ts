import { formatUserAgentData, generateEmploymentNumber, getOrganizationLogo, getOrgName, sendEmailNotification, sendInvitation } from "../helper/common";
import { encrypt } from "../helper/utils";
import { Role } from "../models/MORole";
import { Role as uRole } from "../models/Role";
import { MOPermission } from "../models/MOPermission";
import { MOModule } from "../models/MOModule";
import { User, user_status } from "../models/User";
import { invitation_status, UserInvite } from "../models/UserInvite";
import { UserRole } from "../models/UserRole";


import { UserSession } from "../models/UserSession";
import { Activity } from "../models/Activity";
import { BannerNotification } from "../models/BannerNotification";
import { BannerConfig } from "../models/BannerConfig";
import { ADMIN_SIDE_USER, EMAIL_ADDRESS, NORMAL_USER, OLD_ROLE_PERMISSION, ROLE_PERMISSIONS, ROLE_CONSTANT } from "../helper/constant";
import { Permission } from "../models/Permission";
import { sequelize } from "../models";
import { ChangeRequestSettings } from "../models/ChangeRequestSettings";
import { Setting, setting_status } from "../models/Setting";
import { Op, QueryTypes } from "sequelize";

/** Store staff details */
const staffUpdateDetails = async (staffData: any) => {
    try {
        staffData = staffData.staffResponse;

        /** check if staff action is reset password */
        if (staffData.type == 'reset_password') {
            const getUserData: any = await User.findOne({ where: { keycloak_auth_id: staffData.userId }, attributes: ['id', 'user_status'], raw: true })
            if (getUserData) {
                /** check user status, if it's pending then update with active status */
                await User.update({
                    user_status:
                        getUserData.user_status == user_status.PENDING
                            ? user_status.ACTIVE
                            : getUserData.user_status,
                }, { where: { id: getUserData.id } })
                const findUserInvitation = await UserInvite.findOne({ where: { user_id: getUserData.id, invitation_status: { [Op.not]: invitation_status.ACCEPTED } } })
                if (findUserInvitation) {
                    await UserInvite.update({ invitation_status: invitation_status.ACCEPTED, action_by: getUserData.id, updated_by: getUserData.id }, { where: { id: findUserInvitation.id } })
                }
            }
        } else {
            /** Update keycloak auth id after creation staff user. */
            if (staffData) {
                const username = staffData.username ? staffData.username : null
                await User.update({ keycloak_auth_id: staffData.keycloak_auth_id, username: username }, {
                    where: {
                        id: staffData.userId
                    }
                })
            }
        }
    } catch (e: any) {
        console.error('Error in staffUpdateDetails:', e);
        return { status: false, message: e }
    }
}
/** Store org master details */
const orgMasterDetails = async (masterData: any) => {
    try {
        masterData = masterData.orgMasterData;
        const employmentNumber = await generateEmploymentNumber(masterData.organization_id)

        if (masterData) {

            const superAdminRole = await createDefaultRolesAndPermissions(masterData.organization_id, 1);

            /** Prepare User object and store data */
            const createObj: any = {
                user_first_name: masterData.firstName,
                user_last_name: masterData.lastName,
                user_email: masterData.email,
                user_phone_number: masterData.phoneNumber,
                user_status: 'pending',
                keycloak_auth_id: masterData.keycloak_auth_id,
                organization_id: masterData.organization_id,
                user_password: await encrypt(masterData.userPassword),
                employment_number: employmentNumber,
                user_active_role_id: 1,
                web_user_active_role_id: 1,
                user_role_id: superAdminRole.id,
                username: masterData.username,
                user_designation: masterData.user_designation
            }
            const createUser = await User.create(createObj)

            /** Get Super admin role */
            const getSuperAdminRole: any = await uRole.findOne({ where: { parent_role_id: null as unknown as number } });

            /** Not exist then throw error */
            if (!getSuperAdminRole) {
                return { status: false, message: 'Super Admin role not found' }
            }
            /** Prepare User role object and store data */
            // const createUserRole: any = {
            //     user_id: createUser.id,
            //     role_id: getSuperAdminRole.id
            // }

            // await UserRole.create(createUserRole);
            /** Create Default change request setting for organization */
            const createChangeRequestSetting: any = {
                organization_id: masterData.organization_id,
                key: JSON.stringify(["user_first_name", "user_last_name", "user_email"]),
                created_by: createUser.id,
                updated_by: createUser.id
            }
            await ChangeRequestSettings.create(createChangeRequestSetting);

            /** create default settings for organization */
            const defaultSettings: any = [
                { key: "base_leave", value: "5.6" },
                { key: "working_hours_per_day", value: "7" },
                { key: "max_limit_per_week", value: "35" },
                { key: "financial_month", value: "april - march" },
                { key: "currency", value: JSON.stringify({ currency: "GBP", name: "British pound", symbol: "£" }) },
                { key: "leave_calculation_type", value: "manual" },
                { key: "leave_period_type", value: "day" },
            ];

            const settingsToInsert: any = defaultSettings.map((setting: any) => ({
                ...setting,
                organization_id: masterData.organization_id,
                setting_status: setting_status.ACTIVE,
                created_by: createUser.id,
                updated_by: createUser.id
            }));

            await Setting.bulkCreate(settingsToInsert);
        }

    } catch (e: any) {
        console.error('Error in orgMasterDetails:', e);
        return { status: false, message: e }
    }
}
/** Update org master status from pending to verified  */
const updateOrgMasterStatus = async (masterData: any) => {
    try {
        masterData = masterData.orgMaster;

        /** check masterData exist then update status from pending to verified */
        if (masterData.type == 'update_super_admin') {
            const updateObj: any = {
                username: masterData.username,
                user_email: masterData.user_email,
                user_first_name: masterData.user_first_name,
                user_last_name: masterData.user_last_name,
                user_phone_number: masterData.user_phone_number,
            }
            const getUserData: any = await User.update(updateObj, { where: { keycloak_auth_id: masterData.keycloak_auth_id } })
            if (getUserData) {
                return { status: true, message: 'Super admin updated successfully' }
            }
            return { status: false, message: 'Super admin not found' }
        } else {
            await User.update({ user_status: user_status.VERIFIED }, {
                where: {
                    keycloak_auth_id: masterData.keycloak_auth_id
                }
            })
        }
    } catch (e: any) {
        console.error('Error in updateOrgMasterStatus:', e);
        return { status: false, message: e }
    }
}
/** staff creation mail */
const staffCreationMail = async (mailData: any) => {
    try {
        mailData = mailData.mailResponse;
        if (mailData) {
            /** check if consumer queue id for staff reinvitation then execute below code */
            if (mailData && mailData.staffData) {
                const staffData: any = mailData.staffData;
                if (staffData.email) {
                    const templateData = {
                        name: staffData.name,
                        email: staffData.email,
                        password: staffData.user_password,
                        role: staffData.role,
                        username: mailData.username,
                        mail_type: 'send_invitation',
                        ORGANIZATION_LOGO: await getOrganizationLogo(mailData.organization_id),
                        LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
                        ADDRESS: EMAIL_ADDRESS.ADDRESS,
                        PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
                        EMAIL: EMAIL_ADDRESS.EMAIL,
                        ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
                        smtpConfig: 'INFO',
                        organization: await getOrgName(mailData.organization_id)
                    };
                    await sendEmailNotification(templateData)
                }
            } else {
                /** below code is first time invitation of staff */
                const addUserId = mailData.staffUserId
                const adminId = mailData.adminId
                const password = mailData.password
                const username = mailData.username
                /** Send mail */
                await sendInvitation([addUserId], adminId, username, password)
            }
        }
    } catch (e: any) {
        console.error('Error in staffCreationMail:', e);
        return { status: false, message: e }
    }
}
/** session stored data */
const sessionStorage = async (loginData: any) => {
    try {
        const sessionData = loginData.sessionData
        const fetchUser: any = await User.findOne({ where: { keycloak_auth_id: sessionData.user_id }, attributes: ['id'], raw: true })
        if (fetchUser) {
            const userRoles: any = await UserRole.findAll({
                where: { user_id: fetchUser.id },
                include: [
                    {
                        model: uRole,
                        as: "role",
                        attributes: ["id", "role_name"],
                    },
                ],
                nest: true,
                raw: true,
            });
            const roleData = userRoles[0]

            if (process.env.NEXT_NODE_ENV !== "staging" && roleData.role.name !== 'Super Admin') {
                await UserSession.destroy({ where: { user_id: fetchUser.id, device_type: sessionData.device_type } });
                // Store the new session in the database
                await UserSession.create({ user_id: fetchUser.id, device_type: sessionData.device_type, token: sessionData.token } as any);
            }
        }
    } catch (e: any) {
        console.error('Error in sessionStorage:', e);
        return { status: false, message: e }
    }
}

/** store activity log */
const storeActivityLog = async (task: any) => {
    try {
        const data = task.logData.message
        const headers = task.logData.header
        const { activity_table, activity_action, message, platformType, reference_id, organization_id, keycloak_userId, activity_type } = data;
        /** let Get userData */
        const userData: any = await User.findOne({ where: { keycloak_auth_id: keycloak_userId ? keycloak_userId : message.keycloak_userId }, attributes: ['id'], raw: true })

        await Activity.create({
            activity_table,
            activity_action,
            reference_id: reference_id ? reference_id : userData?.id,
            ip_address: headers?.["ip-address"],
            address: headers?.["address"],
            userAgent: `${message?.platformType}` ? `${formatUserAgentData(headers?.["user-agent"], message?.platformType)}` : platformType,
            location: headers?.["location"],
            previous_data: JSON.stringify(data?._previousDataValues),
            new_data: JSON.stringify(message),
            organization_id: organization_id ? organization_id : message.organization_id,
            activity_type: activity_type,
            created_by: userData?.id || null,
            updated_by: userData?.id || null,
        } as any)
    } catch (e: any) {
        console.error('Error in storeActivityLog:', e);
        return { status: false, message: e }
    }
}

/** store Banner Notifications */
const storeBannerNotification = async (notificationData: any) => {
    try {
        const whereObj: any = {};
        /** check if plan status is trial_plan or renew_plan then set key as subscription */
        if (notificationData.plan_status == 'trial_plan' || notificationData.plan_status == 'renew_plan') {
            whereObj.key = 'subscription'
        }

        if (notificationData.plan_status == 'cancel_plan') {
            whereObj.key = 'subscription_cancel'
        }

        const bannerConfig: any = await BannerConfig.findOne({ where: whereObj, raw: true })
        let createObj: any = {}
        if (bannerConfig) {
            createObj.banner_config_id = bannerConfig.id
        }
        createObj = { ...createObj, ...notificationData }
        await BannerNotification.create(createObj as any)
    } catch (e: any) {
        console.error('Error in storeBannerNotification:', e);
        return { status: false, message: e }
    }
}


const getRolesToCreate = async () => {
    return await Role.findAll({
        where: { organization_id: null },
        raw: true,
    } as any);
    // return [
    //     { id: 1, role_name: 'Super Admin', parent_role_id: null },
    //     { id: 2, role_name: 'Admin', parent_role_id: 1 },
    //     { id: 3, role_name: 'Director', parent_role_id: 2 },
    //     { id: 4, role_name: 'HR', parent_role_id: 2 },
    //     { id: 5, role_name: 'Area Manager', parent_role_id: 4 },
    //     { id: 6, role_name: 'Accountant', parent_role_id: 5 },
    //     { id: 7, role_name: 'Branch Manager', parent_role_id: 6 },
    //     { id: 8, role_name: 'Assist. Branch Manager', parent_role_id: 7 },
    //     { id: 9, role_name: 'Head Chef', parent_role_id: 7 },
    //     { id: 10, role_name: 'Bar Manager', parent_role_id: 7 },
    //     { id: 11, role_name: 'FOH', parent_role_id: 7 },
    //     { id: 12, role_name: 'Bar', parent_role_id: 7 },
    //     { id: 13, role_name: 'Kitchen', parent_role_id: 7 },
    //     { id: 14, role_name: 'Hotel Manager', parent_role_id: 6 },
    //     { id: 15, role_name: 'Assist. Hotel Manager', parent_role_id: 14 },
    //     { id: 16, role_name: 'Receptionist', parent_role_id: 15 },
    //     { id: 17, role_name: 'Head Housekeeper', parent_role_id: 15 },
    //     { id: 18, role_name: 'House Keeper', parent_role_id: 17 },
    //     { id: 19, role_name: 'Signature', parent_role_id: 2 },
    // ];
}

// Ensure view permission is added if create/edit/delete is present
export const setViewPermission = (permission: number) => {
    if (permission & (ROLE_PERMISSIONS.CREATE | ROLE_PERMISSIONS.EDIT | ROLE_PERMISSIONS.DELETE)) {
        permission |= ROLE_PERMISSIONS.VIEW;  // Add view permission if create/edit/delete is present
    }
    return permission;
};

/**
 * Update existing users with their highest role from UserRole table to user_role_id field
 * @param organization_id - Organization ID to filter users
 * @param roleIdMap - Map of old role IDs to new MORole IDs
 */
const updateUsersWithMORole = async (organization_id: string, roleIdMap: Map<number, number>) => {
    try {
        console.log(`Starting user_role_id migration for organization: ${organization_id}`);

        // Get all users in the organization who don't have user_role_id set
        const users = await User.findAll({
            where: {
                organization_id: organization_id,
                user_role_id: { [Op.is]: null },
                user_status: {
                    [Op.notIn]: [user_status.DELETED, user_status.CANCELLED]
                }
            },
            attributes: ['id', 'web_user_active_role_id', 'user_active_role_id'],
            raw: true
        } as any);

        console.log(`Found ${users.length} users to migrate in organization ${organization_id}`);

        for (const user of users) {
            try {
                // Get all roles for this user from UserRole table
                const userRoles: any[] = await UserRole.findAll({
                    where: { user_id: user.id },
                    include: [{
                        model: uRole,
                        as: 'role',
                        attributes: ['id', 'role_name', 'parent_role_id'],
                        where: { role_status: 'active' }
                    }],
                    raw: true,
                    nest: true
                });

                if (userRoles.length === 0) {
                    console.log(`No active roles found for user ${user.id}, skipping`);
                    continue;
                }

                // Find the highest priority role (lowest parent_role_id or null for Super Admin)
                let highestRole = userRoles[0];
                for (const userRole of userRoles) {
                    const currentRole = (userRole as any).role;
                    const highestRoleData = (highestRole as any).role;

                    // Super Admin has parent_role_id = null, which is highest priority
                    if (currentRole.parent_role_id === null) {
                        highestRole = userRole;
                        break;
                    }

                    // Lower parent_role_id means higher in hierarchy
                    if (highestRoleData.parent_role_id !== null &&
                        (currentRole.parent_role_id === null ||
                         currentRole.parent_role_id < highestRoleData.parent_role_id)) {
                        highestRole = userRole;
                    }
                }

                // Map old role ID to new MORole ID
                const oldRoleId = (highestRole as any).role.id;
                const newMORoleId = roleIdMap.get(oldRoleId);

                if (newMORoleId) {
                    // Update user with the new MORole ID
                    await User.update(
                        { user_role_id: newMORoleId },
                        { where: { id: user.id } }
                    );

                    console.log(`Updated user ${user.id}: ${(highestRole as any).role.role_name} (old ID: ${oldRoleId}) -> MORole ID: ${newMORoleId}`);
                } else {
                    console.log(`Warning: Could not find MORole mapping for old role ID ${oldRoleId} (${(highestRole as any).role.role_name}) for user ${user.id}`);
                }

            } catch (userError) {
                console.error(`Error updating user ${user.id}:`, userError);
                // Continue with next user instead of failing the entire migration
            }
        }

        console.log(`Completed user_role_id migration for organization: ${organization_id}`);

    } catch (error) {
        console.error(`Error in updateUsersWithMORole for organization ${organization_id}:`, error);
        throw error;
    }
};

/**
 * Standalone function to migrate all existing users to MORole system
 * This can be called independently to populate user_role_id for existing users
 */
const migrateAllUsersToMORole = async () => {
    try {
        console.log('Starting migration of all users to MORole system...');

        // Get all organizations that have users
        const organizations = await User.findAll({
            attributes: ['organization_id'],
            group: ['organization_id'],
            where: {
                organization_id: { [Op.ne]: null },
                user_status: {
                    [Op.notIn]: [user_status.DELETED, user_status.CANCELLED]
                }
            },
            raw: true,
        } as any);

        console.log(`Found ${organizations.length} organizations to process`);

        for (const org of organizations) {
            if (org.organization_id) {
                try {
                    console.log(`Processing organization: ${org.organization_id}`);

                    // Check if MORole system exists for this organization
                    const existingMORoles = await Role.findAll({
                        where: { organization_id: org.organization_id },
                        attributes: ['id', 'role_name'],
                        raw: true
                    });

                    if (existingMORoles.length === 0) {
                        console.log(`No MORole system found for organization ${org.organization_id}, skipping user migration`);
                        continue;
                    }

                    // Create role mapping from old role names to new MORole IDs
                    const roleIdMap = new Map<number, number>();
                    const rolesToCreate = await getRolesToCreate();

                    for (const oldRole of rolesToCreate) {
                        const moRole = existingMORoles.find(mr => mr.role_name === oldRole.role_name);
                        if (moRole) {
                            roleIdMap.set(oldRole.id, moRole.id);
                        }
                    }

                    if (roleIdMap.size === 0) {
                        console.log(`No role mappings found for organization ${org.organization_id}, skipping user migration`);
                        continue;
                    }

                    // Migrate users for this organization
                    await updateUsersWithMORole(org.organization_id, roleIdMap);

                } catch (orgError) {
                    console.error(`Error processing organization ${org.organization_id}:`, orgError);
                    // Continue with next organization
                }
            }
        }

        console.log('Completed migration of all users to MORole system');

    } catch (error) {
        console.error('Error in migrateAllUsersToMORole:', error);
        throw error;
    }
};

const createDefaultRolesAndPermissions = async (organization_id: string, user_id: number) => {
    try {
        const modulesToCreate = await MOModule.findAll({
            where: { organization_id: null },
            raw: true,
        } as any);

        const moduleIdMap = new Map<number, number>();
        for (const moduleData of modulesToCreate) {
            let module = await MOModule.findOne({ where: { module: moduleData.module, organization_id: organization_id } });
            if (!module) {
                module = await MOModule.create({ ...moduleData, id: undefined, organization_id: organization_id, created_by: user_id, updated_by: user_id } as any);
            }
            moduleIdMap.set(moduleData.id, module.id);
        }

        const rolesToCreate = await getRolesToCreate();
        const roleIdMap = new Map<number, number>();
        let superAdminRole: any;

        for (const roleData of rolesToCreate) {
            const parentRoleId = roleData.parent_role_id ? roleIdMap.get(roleData.parent_role_id) : null;

            let role = await Role.findOne({ where: { role_name: roleData.role_name, organization_id: organization_id } });
            if (!role) {
                role = await Role.create({
                    role_name: roleData.role_name,
                    role_status: 'active',
                    parent_role_id: parentRoleId,
                    platform: NORMAL_USER.includes(roleData.role_name as any) && ADMIN_SIDE_USER.includes(roleData.role_name as any) ? 3 : ADMIN_SIDE_USER.includes(roleData.role_name as any) ? 1 : 2,
                    additional_permissions: null, // Initialize as null, can be set later via role APIs
                    organization_id: organization_id,
                    created_by: user_id,
                    updated_by: user_id,
                } as any);
            }

            roleIdMap.set(roleData.id, role.id);

            if (roleData.role_name === 'Super Admin') {
                superAdminRole = role;
            }

            const permissions: any = await MOPermission.findAll({
                where: { role_id: roleData.id, organization_id: null },
                raw: true,
            } as any);

            for (const permission of permissions) {
                const newModuleId = moduleIdMap.get(permission.module_id);
                if (newModuleId) {
                    const existingPermission = await MOPermission.findOne({ where: { role_id: role.id, module_id: newModuleId, organization_id: organization_id } });
                    if (!existingPermission) {
                        const permissionValue = Number(permission.permission)
                        await MOPermission.create({
                            ...permission,
                            id: undefined,
                            role_id: role.id,
                            module_id: newModuleId,
                            organization_id: organization_id,
                            created_by: user_id,
                            updated_by: user_id,
                            permission: roleData.role_name === 'Super Admin' ? 15 : setViewPermission(permissionValue || 0)
                        } as any);
                    }
                }
            }
        }

        // Update existing users with their highest role in the new MORole system
        await updateUsersWithMORole(organization_id, roleIdMap);

        return superAdminRole;
    } catch (e: any) {
        console.error('Error in createDefaultRolesAndPermissions:', e);
        throw e;
    }
}



const backfillDefaultData = async () => {
    try {
        const organizations = await User.findAll({
            attributes: ['organization_id'],
            group: ['organization_id'],
            where: { organization_id: { [Op.ne]: null } },
            raw: true,
        } as any);

        for (const org of organizations) {
            if (org.organization_id) {
                const user = await User.findOne({ 
                    where: { organization_id: org.organization_id }, 
                    include: { 
                        model: UserRole, 
                        as: 'user_roles',
                        include: [{
                            model: uRole, 
                            as: 'role', 
                            where: { role_name: 'Super Admin' } 
                        }]
                    }, raw: true 
                });
                if (user) {
                    await createDefaultRolesAndPermissions(org.organization_id, 1);
                    // await User.update({ user_active_role_id: newRole.id, web_user_active_role_id: newRole.id }, { where: { id: user.id } })
                }
            }
        }
    } catch (e: any) {
        console.error('Error in backfillDefaultData:', e);
        throw e;
    }
}

setTimeout(() => {
    backfillDefaultData().then().catch((e: any) => {
        console.error('Error in backfillDefaultData:', e);
    })
}, 200000)

/**
 * Migration script to update mo_permissions table based on nv_permissions data
 * This script:
 * 1. Maps old permission values to new permission values using OLD_ROLE_PERMISSION constant
 * 2. Updates mo_permissions where organization_id is NULL (template permissions)
 * 3. Copies template permissions to organization-specific permissions
 * 4. Handles missing modules by setting appropriate default permissions
 */

interface ModuleMapping {
  old_module: string;
  new_module_id: number;
  new_module_slug: string;
}



/**
 * Get module mapping between old nv_permissions modules and new mo_modules
 */
async function getModuleMapping(): Promise<ModuleMapping[]> {
  const moduleMapping: ModuleMapping[] = [
    // Direct mappings based on actual old and new module data from nv_permissions_.sql and mo_modules.sql
    { old_module: 'dashboard', new_module_id: 1, new_module_slug: 'dashboard' },
    { old_module: 'forecast', new_module_id: 2, new_module_slug: 'budget_forecast' },
    { old_module: 'forecast_budget', new_module_id: 2, new_module_slug: 'budget_forecast' },
    { old_module: 'setting', new_module_id: 3, new_module_slug: 'company_setting' },
    { old_module: 'setup', new_module_id: 3, new_module_slug: 'company_setting' },
    { old_module: 'branch', new_module_id: 4, new_module_slug: 'branch' },
    { old_module: 'playlist', new_module_id: 5, new_module_slug: 'training' },
    { old_module: 'media', new_module_id: 5, new_module_slug: 'training' },
    { old_module: 'category', new_module_id: 5, new_module_slug: 'training' }, // category maps to training
    { old_module: 'dsr', new_module_id: 6, new_module_slug: 'dsr_setting' },
    { old_module: 'department', new_module_id: 7, new_module_slug: 'department' },
    { old_module: 'notification', new_module_id: 8, new_module_slug: 'holiday_management' },
    { old_module: 'leave_setting', new_module_id: 9, new_module_slug: 'leave_setting' },
    { old_module: 'user', new_module_id: 10, new_module_slug: 'administrator_account' },
    { old_module: 'user_invitation', new_module_id: 11, new_module_slug: 'export_setting' },
    { old_module: 'change_request', new_module_id: 12, new_module_slug: 'change_request_setting' },
    { old_module: 'staff', new_module_id: 13, new_module_slug: 'staff' },
    { old_module: 'user_verification', new_module_id: 14, new_module_slug: 'staff_invitation' },
    { old_module: 'employee_contract', new_module_id: 15, new_module_slug: 'employee_contract' },
    { old_module: 'resignation', new_module_id: 17, new_module_slug: 'resignation' },
    { old_module: 'dsr_report', new_module_id: 24, new_module_slug: 'dsr_report' },
    { old_module: 'leave_center', new_module_id: 25, new_module_slug: 'team_leave' },
    { old_module: 'leave_report', new_module_id: 27, new_module_slug: 'leave_report' },
    { old_module: 'rota', new_module_id: 29, new_module_slug: 'rotas' }, // rota maps to rotas
    { old_module: 'activity_log', new_module_id: 35, new_module_slug: 'activity_log' },
    { old_module: 'recipe', new_module_id: 39, new_module_slug: 'recipe' }, // recipe maps to recipe
    // Old modules that don't have direct new equivalents - map to closest match
    { old_module: 'branch_card', new_module_id: 4, new_module_slug: 'branch' }, // branch_card maps to branch
    { old_module: 'branch_bank', new_module_id: 4, new_module_slug: 'branch' }, // branch_bank maps to branch
    { old_module: 'side_letter', new_module_id: 15, new_module_slug: 'employee_contract' }, // side_letter maps to employee_contract
  ];

  return moduleMapping;
}

/**
 * Convert old permission value to new permission value using OLD_ROLE_PERMISSION mapping
 */
function convertPermissionValue(oldPermission: number): number {
  return OLD_ROLE_PERMISSION[oldPermission] || 0;
}

/**
 * Get all mo_modules that exist in the system
 */
async function getAllMOModules(): Promise<any[]> {
  return await MOModule.findAll({
    attributes: ['id', 'module', 'module_name'],
    raw: true
  });
}

/**
 * Get all mo_roles that exist in the system
 */
async function getAllMORoles(): Promise<any[]> {
  return await Role.findAll({
    attributes: ['id', 'role_name', 'organization_id'],
    raw: true
  });
}

/**
 * Get all old permissions from nv_permissions
 */
async function getOldPermissions(): Promise<any[]> {
  return await Permission.findAll({
    include: [
      {
        model: uRole,
        as: 'role',
        attributes: ['id', 'role_name']
      }
    ],
    raw: true,
    nest: true
  });
}

/**
 * Update template permissions (organization_id = NULL) based on old permissions
 */
async function updateTemplatePermissions(): Promise<void> {
  console.log('🔄 Starting template permissions update...');

  const moduleMapping = await getModuleMapping();
  const oldPermissions = await getOldPermissions();
  const moRoles = await getAllMORoles();

  // Group old permissions by role_id and module
  const permissionMap = new Map<string, any>();

  for (const oldPerm of oldPermissions) {
    const key = `${oldPerm.role_id}_${oldPerm.module}`;
    permissionMap.set(key, oldPerm);
  }

  // Process each role and module combination
  for (const moRole of moRoles.filter(r => r.organization_id === null)) {
    for (const mapping of moduleMapping) {
      const key = `${moRole.id}_${mapping.old_module}`;
      const oldPerm = permissionMap.get(key);

      if (oldPerm) {
        // Update existing mo_permission with converted value
        const newPermission = convertPermissionValue(oldPerm.permission);

        await MOPermission.update(
          {
            permission: newPermission,
            partial: oldPerm.partial || false,
            updated_by: 1
          },
          {
            where: {
              role_id: moRole.id,
              module_id: mapping.new_module_id,
              organization_id: null as any
            }
          }
        );

        console.log(`✅ Updated template permission: Role ${moRole.role_name} -> Module ${mapping.new_module_slug} -> Permission ${newPermission}`);
      }
    }
  }
}

/**
 * Handle missing modules by setting default permissions
 */
async function handleMissingModules(): Promise<void> {
  console.log('🔄 Handling missing modules...');

  const allModules = await getAllMOModules();
  const templateRoles = await getAllMORoles();
  const moduleMapping = await getModuleMapping();

  // Get mapped module IDs
  const mappedModuleIds = new Set(moduleMapping.map(m => m.new_module_id));

  // Find modules that don't have mappings from old system
  const unmappedModules = allModules.filter(module => !mappedModuleIds.has(module.id));

  for (const module of unmappedModules) {
    for (const role of templateRoles.filter(r => r.organization_id === null)) {
      // Set permission based on role type
      let defaultPermission = 1; // VIEW only for most roles

      if (role.role_name === ROLE_CONSTANT.SUPER_ADMIN) {
        defaultPermission = 15; // Full permissions for Super Admin
      } else if (role.role_name === ROLE_CONSTANT.ADMIN) {
        defaultPermission = 7; // VIEW + CREATE + EDIT for Admin
      }

      // Check if permission already exists
      const existingPermission = await MOPermission.findOne({
        where: {
          role_id: role.id,
          module_id: module.id,
          organization_id: null as any
        }
      });

      if (existingPermission) {
        // Update existing permission
        await MOPermission.update(
          {
            permission: defaultPermission,
            updated_by: 1
          },
          {
            where: {
              role_id: role.id,
              module_id: module.id,
              organization_id: null as any
            }
          }
        );
      } else {
        // Create new permission
        await MOPermission.create({
          role_id: role.id,
          module_id: module.id,
          permission: defaultPermission,
          partial: false,
          status: 'active',
          created_by: 1,
          updated_by: 1,
          organization_id: null as any
        } as any);
      }

      console.log(`✅ Set default permission for unmapped module: Role ${role.role_name} -> Module ${module.module} -> Permission ${defaultPermission}`);
    }
  }
}

/**
 * Copy template permissions to organization-specific permissions
 */
async function copyTemplateToOrganizations(): Promise<void> {
  console.log('🔄 Copying template permissions to organizations...');

  // Get all organizations that have roles
  const organizations = await sequelize.query(
    `SELECT DISTINCT organization_id FROM mo_roles WHERE organization_id IS NOT NULL`,
    { type: QueryTypes.SELECT }
  ) as { organization_id: string }[];

  // Get all template permissions
  const templatePermissions = await MOPermission.findAll({
    where: { organization_id: null as any },
    raw: true
  });

  for (const org of organizations) {
    console.log(`📋 Processing organization: ${org.organization_id}`);

    // Get organization roles
    const orgRoles = await Role.findAll({
      where: { organization_id: org.organization_id },
      raw: true
    });

    for (const templatePerm of templatePermissions) {
      // Find corresponding organization role
      const templateRole = await Role.findOne({
        where: { id: templatePerm.role_id, organization_id: null as any },
        raw: true
      });

      if (templateRole) {
        const orgRole = orgRoles.find(r => r.role_name === templateRole.role_name);

        if (orgRole) {
          // Check if organization permission already exists
          const existingOrgPerm = await MOPermission.findOne({
            where: {
              role_id: orgRole.id,
              module_id: templatePerm.module_id,
              organization_id: org.organization_id
            }
          });

          if (existingOrgPerm) {
            // Update existing permission with template values
            await MOPermission.update(
              {
                permission: templatePerm.permission,
                partial: templatePerm.partial,
                updated_by: 1
              },
              {
                where: {
                  role_id: orgRole.id,
                  module_id: templatePerm.module_id,
                  organization_id: org.organization_id
                }
              }
            );
          } else {
            // Create new organization permission based on template
            await MOPermission.create({
              role_id: orgRole.id,
              module_id: templatePerm.module_id,
              permission: templatePerm.permission,
              partial: templatePerm.partial,
              status: 'active',
              created_by: 1,
              updated_by: 1,
              organization_id: org.organization_id
            } as any);
          }

          console.log(`✅ Copied permission: Org ${org.organization_id} -> Role ${orgRole.role_name} -> Module ${templatePerm.module_id} -> Permission ${templatePerm.permission}`);
        }
      }
    }
  }
}

/**
 * Main migration function
 */
async function migratePermissions(): Promise<void> {
  try {
    console.log('🚀 Starting MOPermissions migration...');
    console.log('📊 OLD_ROLE_PERMISSION mapping:', OLD_ROLE_PERMISSION);

    // Step 1: Update template permissions based on old permissions
    await updateTemplatePermissions();

    // Step 2: Handle missing modules with default permissions
    await handleMissingModules();

    // Step 3: Copy template permissions to organization-specific permissions
    await copyTemplateToOrganizations();

    console.log('✅ MOPermissions migration completed successfully!');

  } catch (error) {
    console.error('❌ Error during MOPermissions migration:', error);
    throw error;
  }
}

/**
 * Utility function to run migration from command line or API
 */
async function runPermissionMigration(): Promise<{ success: boolean; message: string }> {
  try {
    await migratePermissions();
    return {
      success: true,
      message: 'MOPermissions migration completed successfully'
    };
  } catch (error) {
    return {
      success: false,
      message: `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

export { staffUpdateDetails, orgMasterDetails, updateOrgMasterStatus, staffCreationMail, sessionStorage, storeActivityLog, storeBannerNotification, createDefaultRolesAndPermissions, backfillDefaultData, migrateAllUsersToMORole, updateUsersWithMORole, migratePermissions, runPermissionMigration }