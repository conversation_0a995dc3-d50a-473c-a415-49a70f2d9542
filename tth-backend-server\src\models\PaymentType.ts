"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";


interface PaymentTypeAttributes {
  id: number;
  payment_type_title: string;
  payment_type_status: string;
  payment_type_usage: string;
  has_weekly_use: boolean;
  payment_type_order: number;
  has_include_amount: boolean;
  has_field_currency: boolean;
  organization_id: string;
  created_by: number;
  updated_by: number;
}

export enum payment_type_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = 'deleted'
}

export enum payment_type_usage {
  COLLECTION = "income",
  EXPENSE = "expense",
  OTHER = "other"
}


export class PaymentType
  extends Model<PaymentTypeAttributes, never>
  implements PaymentTypeAttributes {
  id!: number;
  payment_type_title!: string;
  payment_type_status!: string;
  payment_type_usage!: string;
  has_weekly_use!: boolean;
  payment_type_order!: number;
  has_include_amount!: boolean;
  has_field_currency!: boolean;
  organization_id!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

PaymentType.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    payment_type_title: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    payment_type_status: {
      type: DataTypes.ENUM,
      values: Object.values(payment_type_status),
      defaultValue: payment_type_status.ACTIVE,
    },
    payment_type_usage: {
      type: DataTypes.ENUM,
      values: Object.values(payment_type_usage),
      allowNull: true,
    },
    has_weekly_use: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    payment_type_order: {
      type: DataTypes.INTEGER,
    },
    has_include_amount: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    has_field_currency: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_payment_type",
    modelName: "PaymentType",
  },
);



// Define hooks for Card model
PaymentType.addHook("afterUpdate", async (paymentType: any) => {
  await addActivity("PaymentType", "updated", paymentType);
});

PaymentType.addHook("afterCreate", async (paymentType: PaymentType) => {
  await addActivity("PaymentType", "created", paymentType);
});

PaymentType.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

