"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { ChangeRequest } from "./ChangeRequest";

interface changeRequestHistoryAttributes {
  id: number;
  change_request_id: number;
  user_id: number;
  change_request_subject: string;
  change_request_status: string;
  old_data: string;
  new_data: string;
  change_request_files: string;
  change_request_history_status: string;
  change_request_remark: string;
  created_by: number;
  updated_by: number;
}

export enum change_request_status {
  PENDING = 'pending',
  REOPENED = 'reopened',
  REJECTED = 'rejected',
  APPROVED = 'approved',
  CANCELLED = "cancelled",
  DELETED = 'deleted',
  CLOSED = 'closed'
}


export enum change_request_history_status {
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

export class ChangeRequestHistory
  extends Model<changeRequestHistoryAttributes, never>
  implements changeRequestHistoryAttributes {
  id!: number;
  change_request_id!: number;
  user_id!: number;
  change_request_subject!: string;
  change_request_status!: string;
  old_data!: string;
  new_data!: string;
  change_request_files!: string;
  change_request_history_status!: string;
  change_request_remark!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

ChangeRequestHistory.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    change_request_subject: {
      type: DataTypes.TEXT("long"),
      allowNull: true,
    },
    change_request_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    old_data: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    new_data: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    change_request_files: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    change_request_history_status: {
      type: DataTypes.ENUM,
      values: Object.values(change_request_history_status),
      defaultValue: change_request_history_status.ACTIVE,
    },
    change_request_status: {
      type: DataTypes.ENUM,
      values: Object.values(change_request_status),
      defaultValue: change_request_status.PENDING,
    },
    change_request_remark: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_change_request_history",
    modelName: "ChangeRequestHistory",
  },
);

// ChangeRequestHistory.belongsTo(User, {
//   foreignKey: "user_id",
//   as: "change_request_history_user",
// });
// User.hasMany(ChangeRequestHistory, {
//   foreignKey: "user_id",
//   as: "change_request_user_history_detail",
// });

ChangeRequestHistory.belongsTo(ChangeRequest, {
  foreignKey: "change_request_id",
  as: "change_request",
});
ChangeRequest.hasMany(ChangeRequestHistory, {
  foreignKey: "change_request_id",
  as: "change_request_history",
});



ChangeRequestHistory.addHook("afterUpdate", async (changeRequestHistory: any) => {
  await addActivity("Change Request History", "updated", changeRequestHistory);
});

ChangeRequestHistory.addHook("afterCreate", async (changeRequestHistory: ChangeRequestHistory) => {
  await addActivity("Change Request History", "created", changeRequestHistory);
});

ChangeRequestHistory.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
