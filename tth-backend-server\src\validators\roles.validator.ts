import { Segments, Jo<PERSON>, celebrate } from "celebrate";
export default {
    createRole: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                role_name: Joi.string().required(),
                platform: Joi.number().valid(1, 2, 3).required(), // 1=web, 2=mobile, 3=both
                parent_role_id: Joi.number().required(),
                additional_permissions: Joi.object().allow(null), // Role-level additional permissions
            }),
        }),
    updateRole: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                role_name: Joi.string().allow(null, ""),
                platform: Joi.number().valid(1, 2, 3).allow(null), // 1=web, 2=mobile, 3=both
                parent_role_id: Joi.number().allow(null),
                additional_permissions: Joi.object().allow(null), // Role-level additional permissions
            }),
        }),
    createPermission: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                role_id: Joi.number().required(),
                module_ids: Joi.array().items(Joi.number()).required(),
                partial: Joi.boolean().required(),
                permission: Joi.number().required(),
                platform: Joi.number().valid(1, 2, 3).required(), // 1=web, 2=mobile, 3=both
                additional_permissions: Joi.object().allow(null), // Module-level additional permissions
            }),
        }),
    updatePermission: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                roles: Joi.array().items( // Array of roles with their permissions
                    Joi.object().keys({
                        role_id: Joi.number().required(),
                        modules: Joi.array().items( // Array of modules with permissions
                            Joi.object().keys({
                                module_id: Joi.number().required(),
                                permission: Joi.number().required(), // Permission value (0-15)
                                partial: Joi.boolean().allow(null)
                            })
                        ).required()
                    })
                ).required(),
            }),
        }),
    copyPermission: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                from_role: Joi.number().required(),
                to_role: Joi.array().items(Joi.number()).min(1).required(), // Array of role IDs
            }),
        }),
};