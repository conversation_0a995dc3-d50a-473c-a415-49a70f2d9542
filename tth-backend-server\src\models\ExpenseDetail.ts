"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface expenseDetailAttributes {
  id: number;
  user_id: number;
  branch_id: number
  expense_month: number;
  expense_year: number;
  expense_detail_status: string;
  created_by: number;
  updated_by: number;
}

export enum expense_detail_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = 'deleted'
}

export class ExpenseDetail
  extends Model<expenseDetailAttributes, never>
  implements expenseDetailAttributes {
  id!: number;
  user_id!: number;
  branch_id!: number;
  expense_month!: number;
  expense_year!: number;
  expense_detail_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

ExpenseDetail.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    branch_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    expense_month: {
      type: DataTypes.TINYINT,
      allowNull: false,
    },
    expense_year: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    expense_detail_status: {
      type: DataTypes.ENUM,
      values: Object.values(expense_detail_status),
      defaultValue: expense_detail_status.ACTIVE,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_expense_details",
    modelName: "ExpenseDetail",
  },
);


// Define hooks for Card model
ExpenseDetail.addHook("afterUpdate", async (expenseDetail: any) => {
  await addActivity("ExpenseDetail", "updated", expenseDetail);
});

ExpenseDetail.addHook("afterCreate", async (expenseDetail: ExpenseDetail) => {
  await addActivity("ExpenseDetail", "created", expenseDetail);
});

ExpenseDetail.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
