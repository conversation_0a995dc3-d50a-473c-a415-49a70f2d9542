# DSR/WSR/Expense Dashboard Widget Usage Examples

## Updated Function Call Examples

### 1. Get Current Month Weekly Data (DSR, WSR, Expense)
```javascript
const widgets = await getDsrDashboardWidgets(
    "org123",
    456,
    {
        time_period: "this_month",
        branch_id: 1  // optional
    }
);

// Response:
{
  "dashboard_data": {
    "series": [
      {
        "type": "line",
        "xKey": "col1",
        "yKey": "col2",
        "yName": "DSR",
        "fills": []
      },
      {
        "type": "line",
        "xKey": "col1",
        "yKey": "col3",
        "yName": "WSR",
        "fills": []
      },
      {
        "type": "line",
        "xKey": "col1",
        "yKey": "col4",
        "yName": "Expense",
        "fills": []
      }
    ],
    "data": [
      { "col1": "Week 1", "col2": 1500.50, "col3": 800.25, "col4": 300.00 },
      { "col1": "Week 2", "col2": 2300.75, "col3": 1200.50, "col4": 450.25 },
      { "col1": "Week 3", "col2": 1800.25, "col3": 950.75, "col4": 275.50 },
      { "col1": "Week 4", "col2": 2100.00, "col3": 1100.00, "col4": 380.75 }
    ]
  }
}
```

### 2. Get Current Week Daily Data (DSR, WSR, Expense)
```javascript
const widgets = await getDsrDashboardWidgets(
    "org123",
    456,
    {
        time_period: "this_week",
        branch_id: 1  // optional
    }
);

// Response:
{
  "dashboard_data": {
    "series": [
      {
        "type": "line",
        "xKey": "col1",
        "yKey": "col2",
        "yName": "DSR",
        "fills": []
      },
      {
        "type": "line",
        "xKey": "col1",
        "yKey": "col3",
        "yName": "WSR",
        "fills": []
      },
      {
        "type": "line",
        "xKey": "col1",
        "yKey": "col4",
        "yName": "Expense",
        "fills": []
      }
    ],
    "data": [
      { "col1": "Sunday", "col2": 500.00, "col3": 200.00, "col4": 50.00 },
      { "col1": "Monday", "col2": 750.25, "col3": 350.50, "col4": 75.25 },
      { "col1": "Tuesday", "col2": 680.50, "col3": 300.75, "col4": 60.50 },
      { "col1": "Wednesday", "col2": 920.75, "col3": 450.25, "col4": 85.75 },
      { "col1": "Thursday", "col2": 1100.00, "col3": 500.00, "col4": 100.00 },
      { "col1": "Friday", "col2": 1250.25, "col3": 600.50, "col4": 120.25 },
      { "col1": "Saturday", "col2": 800.00, "col3": 400.00, "col4": 80.00 }
    ]
  }
}
```

### 3. Get Current Year Monthly Data (DSR, WSR, Expense)
```javascript
const widgets = await getDsrDashboardWidgets(
    "org123",
    456,
    {
        time_period: "this_year",
        branch_id: 1  // optional
    }
);

// Response shows monthly breakdown for all 12 months with DSR, WSR, and Expense data
```

## Filter Parameters

- `time_period` or `filter_time_period`:
  - `"this_month"` (default) - Shows weekly data for current month
  - `"this_week"` - Shows daily data for current week
  - `"this_year"` - Shows monthly data for current year
  - `"last_week"` - Shows daily data for previous week
  - `"last_month"` - Shows weekly data for previous month

- `branch_id` (optional): Filter data for specific branch

## Data Structure

The response now includes three data series:
- **col1**: Time period label (Week 1-4, Sunday-Saturday, January-December)
- **col2**: DSR (Daily Sales Report) total income
- **col3**: WSR (Weekly Sales Report) total income
- **col4**: Expense total amount

## Data Source

The function uses a comprehensive SQL query that fetches data from:
- **DSR Tables**: `nv_dsr_details`, `nv_dsr_items`
- **WSR Tables**: `nv_wsr_details`, `nv_wsr_items`
- **Expense Tables**: `nv_expense_details`, `nv_expense_items`
- **Supporting Tables**: `nv_payment_type_category`, `nv_branches`

Only includes:
- Active records (`status = 'active'`)
- Total income amounts (uses `TotalIncome` from JSON fields)
- Data within the specified time period
- Optional branch filtering

## Time Period Grouping

- **Monthly views** (`this_month`, `last_month`): Groups data by weeks
- **Weekly views** (`this_week`, `last_week`): Groups data by days
- **Yearly views** (`this_year`): Groups data by months
- **Other periods**: Returns 0 values

## Error Handling

If an error occurs, the function returns an empty data structure with the same three-series format to maintain consistency.
