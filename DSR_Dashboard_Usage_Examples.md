# DSR Dashboard Widget Usage Examples

## Function Call Examples

### 1. Get Current Month Weekly Data
```javascript
const widgets = await getDsrDashboardWidgets(
    "org123", 
    456, 
    { 
        time_period: "this_month",
        branch_id: 1  // optional
    }
);

// Response:
{
  "dashboard_data": {
    "series": [
      {
        "type": "line",
        "xKey": "col1",
        "yKey": "col2",
        "yName": "Total of All income",
        "fills": []
      }
    ],
    "data": [
      { "col1": "Week 1", "col2": 1500.50 },
      { "col1": "Week 2", "col2": 2300.75 },
      { "col1": "Week 3", "col2": 1800.25 },
      { "col1": "Week 4", "col2": 2100.00 }
    ]
  }
}
```

### 2. Get Current Week Daily Data
```javascript
const widgets = await getDsrDashboardWidgets(
    "org123", 
    456, 
    { 
        time_period: "this_week",
        branch_id: 1  // optional
    }
);

// Response:
{
  "dashboard_data": {
    "series": [
      {
        "type": "line",
        "xKey": "col1",
        "yKey": "col2",
        "yName": "Total of All income",
        "fills": []
      }
    ],
    "data": [
      { "col1": "Sunday", "col2": 500.00 },
      { "col1": "Monday", "col2": 750.25 },
      { "col1": "Tuesday", "col2": 680.50 },
      { "col1": "Wednesday", "col2": 920.75 },
      { "col1": "Thursday", "col2": 1100.00 },
      { "col1": "Friday", "col2": 1250.25 },
      { "col1": "Saturday", "col2": 800.00 }
    ]
  }
}
```

### 3. Get Today's Hourly Data
```javascript
const widgets = await getDsrDashboardWidgets(
    "org123", 
    456, 
    { 
        time_period: "today",
        branch_id: 1  // optional
    }
);

// Response shows hourly breakdown from 0:00 to 23:00
```

## Filter Parameters

- `time_period` or `filter_time_period`: 
  - `"this_month"` (default)
  - `"this_week"`
  - `"today"`
  - `"last_week"`
  - `"last_month"`

- `branch_id` (optional): Filter data for specific branch

## Data Source

The function fetches data from:
- `DsrDetail` table: Contains daily sales records
- `DsrItem` table: Contains individual sales items
- Only includes active records and income/collection type payments
- Aggregates amounts by the specified time period

## Error Handling

If an error occurs, the function returns an empty data structure with the same format to maintain consistency.
