"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { DsrDetail } from "./DsrDetail";
import { PaymentTypeCategory } from "./PaymentTypeCategory";

interface dsrItemAttributes {
  id: number;
  dsr_detail_id: number;
  payment_type_category_id: number
  dsr_amount: number;
  reference_id: number;
  dsr_item_status: string;
  created_by: number;
  updated_by: number;
}

export enum dsr_item_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = 'deleted'
}

export class DsrItem
  extends Model<dsrItemAttributes, never>
  implements dsrItemAttributes {
  id!: number;
  dsr_detail_id!: number;
  payment_type_category_id!: number
  dsr_amount!: number;
  reference_id!: number;
  dsr_item_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

DsrItem.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    dsr_detail_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    payment_type_category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    dsr_amount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
    },
    reference_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    dsr_item_status: {
      type: DataTypes.ENUM,
      values: Object.values(dsr_item_status),
      defaultValue: dsr_item_status.ACTIVE,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_dsr_items",
    modelName: "DsrItem",
  },
);

DsrItem.belongsTo(DsrDetail, { foreignKey: "dsr_detail_id", as: "dsr_item" });
DsrDetail.hasMany(DsrItem, { foreignKey: "dsr_detail_id", as: "dsr_detail" });

DsrItem.belongsTo(PaymentTypeCategory, { foreignKey: "payment_type_category_id", as: "dsr_item_type" });
PaymentTypeCategory.hasMany(DsrItem, { foreignKey: "payment_type_category_id", as: "dsr_detail_type" });


// Define hooks for Card model
DsrItem.addHook("afterUpdate", async (dsrItem: any) => {
  await addActivity("DsrItem", "updated", dsrItem);
});

DsrItem.addHook("afterCreate", async (dsrItem: DsrItem) => {
  await addActivity("DsrItem", "created", dsrItem);
});

DsrItem.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

