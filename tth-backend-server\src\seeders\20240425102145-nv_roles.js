const { QueryTypes } = require("sequelize");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    const userRoles = await queryInterface.sequelize.query(
      `SELECT * FROM nv_roles`,
      { type: QueryTypes.SELECT },
    );
    if (userRoles.length == 0) {
      await queryInterface.bulkInsert("nv_roles", [
        {
          role_name: "Super Admin",
          role_status: "active",
          parent_role_id: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Admin",
          role_status: "active",
          parent_role_id: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Director",
          role_status: "active",
          parent_role_id: 2,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "HR",
          role_status: "active",
          parent_role_id: 2,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Area Manager",
          role_status: "active",
          parent_role_id: 4,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Accountant",
          role_status: "active",
          parent_role_id: 5,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Branch Manager",
          role_status: "active",
          parent_role_id: 6,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Assist. Branch Manager",
          role_status: "active",
          parent_role_id: 7,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Head Chef",
          role_status: "active",
          parent_role_id: 7,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Bar Manager",
          role_status: "active",
          parent_role_id: 7,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "FOH",
          role_status: "active",
          parent_role_id: 7,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Bar",
          role_status: "active",
          parent_role_id: 7,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Kitchen",
          role_status: "active",
          parent_role_id: 7,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Hotel Manager",
          role_status: "active",
          parent_role_id: 6,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Assist. Hotel Manager",
          role_status: "active",
          parent_role_id: 14,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Receptionist",
          role_status: "active",
          parent_role_id: 15,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Head Housekeeper",
          role_status: "active",
          parent_role_id: 15,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "House Keeper",
          role_status: "active",
          parent_role_id: 17,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          role_name: "Signature",
          role_status: "active",
          parent_role_id: 2,
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      ]);
    }
  },
};
