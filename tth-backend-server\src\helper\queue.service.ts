"use strict";
import { Activity } from "../models/Activity";
import Queue from "better-queue";
import { formatUserAgentData } from "./common";

const activityQueue = new Queue((task: any, cb: any) => {
    const { activity_table, activity_action, data } = task;
    Activity.create({
        activity_table,
        activity_action,
        reference_id: data?.id,
        ip_address: data?.headers?.headers?.["ip-address"],
        address: data?.headers?.headers?.["address"],
        userAgent: `${data?.headers?.headers?.["platform-type"]} : ${formatUserAgentData(
            data?.headers?.headers?.["user-agent"],
            data?.headers?.headers?.["platform-type"]
        )}`,
        location: data?.headers?.headers?.["location"],
        previous_data: JSON.stringify(data?._previousDataValues),
        new_data: JSON.stringify(data?.dataValues),
        organization_id: data.headers?.user?.organization_id,
        created_by: data.headers?.user?.id,
        updated_by: data.headers?.user?.id,
    } as any)
        .then(() => cb())
        .catch((error: any) => {
            console.log("Error processing activity queue item", error);
            cb(error);
        });
});

export const addActivity = (activity_table: string, activity_action: string, data: any) => {
    activityQueue.push({ activity_table, activity_action, data });
};
