"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { Media } from "./Media";
import { Playlist } from "./Playlist";
import { addActivity } from "../helper/queue.service";

interface playlistMediaTrackAttributes {
  playlist_id: number;
  media_id: number;
  playlist_media_track_status: string;
  user_id: number;
  created_by: number;
  updated_by: number;
}

/** playlist media track enum  for status*/
export enum playlist_media_track_status {
  ONGOING = "ongoing",
  PENDING = "pending",
  COMPLETED = "completed",
}

export class PlaylistMediaTrack
  extends Model<playlistMediaTrackAttributes, never>
  implements playlistMediaTrackAttributes {
  playlist_id!: number;
  media_id!: number;
  playlist_media_track_status!: string;
  user_id!: number;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

PlaylistMediaTrack.init(
  {
    playlist_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    media_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    playlist_media_track_status: {
      type: DataTypes.ENUM,
      values: Object.values(playlist_media_track_status),
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_playlist_media_track",
    modelName: "PlaylistMediaTrack",
  },
);

PlaylistMediaTrack.removeAttribute("id");
Media.hasMany(PlaylistMediaTrack, { foreignKey: "media_id" });
PlaylistMediaTrack.belongsTo(Media, { foreignKey: "media_id" });
Playlist.hasMany(PlaylistMediaTrack, { foreignKey: "playlist_id" });
PlaylistMediaTrack.belongsTo(Playlist, { foreignKey: "playlist_id" });
// User.hasMany(PlaylistMediaTrack, { foreignKey: "user_id" });
// PlaylistMediaTrack.belongsTo(User, { foreignKey: "user_id" });
// User.hasMany(PlaylistMediaTrack, { foreignKey: "created_by" });
// PlaylistMediaTrack.belongsTo(User, { foreignKey: "created_by" });
// User.hasMany(PlaylistMediaTrack, { foreignKey: "updated_by" });
// PlaylistMediaTrack.belongsTo(User, { foreignKey: "updated_by" });

PlaylistMediaTrack.addHook("afterUpdate", async (playlistMediaTrack: any) => {
  await addActivity("PlaylistMediaTrack", "updated", playlistMediaTrack);
});

PlaylistMediaTrack.addHook(
  "afterCreate",
  async (playlistMediaTrack: PlaylistMediaTrack) => {
    await addActivity("PlaylistMediaTrack", "created", playlistMediaTrack);
  },
);

PlaylistMediaTrack.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

