import { Router } from "express";
import dashboardController from "../../controller/dashboard.controller";
import dashboardValidator from "../../validators/dashboard.validator";
const router: Router = Router();

// Get App Dashboard Detail
router.get("/app-dashboard", dashboardController.getAppDashboardDetail);

// Get Admin Dashboard Detail
router.get("/admin-dashboard", dashboardController.getAdminDashboardDetail);

// Get Dashboard Model
router.get("/get-dashboard-model", dashboardController.getModelType);

// Get Dashboard Model tab
router.get("/get-dashboard-model-tab", dashboardController.getModelTypeTab);

// Create Dashboard List
router.post("/create-dashboard", dashboardValidator.addDashboard(), dashboardController.createDashboard);

// Update Dashboard List
router.put("/update-dashboard/:id", dashboardValidator.updateDashboard(), dashboardController.updateDashboard);

// Get Dashboard List
router.get("/get-dashboard-list", dashboardController.getDashboardList);

// Mark Dashboard Default
router.put("/mark-dashboard-default/:id", dashboardController.markDashboardDefault);

// Remove Dashboard from list
router.delete("/delete-dashboard/:id", dashboardController.deleteDashboard);

// get Dashboard by id
router.get("/get-dashboard-by-id/:id", dashboardController.getDashboardById);

// Get Dashboard Widgets by Type
router.post("/get-dashboard-widgets", dashboardValidator.getDashboardWidgets(), dashboardController.getDashboardWidgets);


export default router;
