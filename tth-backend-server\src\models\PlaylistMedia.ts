"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { Media } from "./Media";
import { Playlist } from "./Playlist";
import { addActivity } from "../helper/queue.service";

interface playlistMediaAttributes {
  playlist_id: number;
  media_id: number;
  playlist_media_status: string;
  order: number;
  created_by: number;
  updated_by: number;
}

/** Role enum  for status*/
export enum playlist_media_status {
  ACTIVE = "active",
  DRAFT = "draft",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export class PlaylistMedia
  extends Model<playlistMediaAttributes, never>
  implements playlistMediaAttributes {
  playlist_id!: number;
  media_id!: number;
  playlist_media_status!: string;
  order!: number;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

PlaylistMedia.init(
  {
    playlist_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    media_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    playlist_media_status: {
      type: DataTypes.ENUM,
      values: Object.values(playlist_media_status),
    },
    order: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_playlist_media",
    modelName: "PlaylistMedia",
  },
);

PlaylistMedia.removeAttribute("id");
Media.hasMany(PlaylistMedia, { foreignKey: "media_id", as: "media" });
PlaylistMedia.belongsTo(Media, { foreignKey: "media_id", as: "media" });
Playlist.hasMany(PlaylistMedia, { foreignKey: "playlist_id" });
PlaylistMedia.belongsTo(Playlist, { foreignKey: "playlist_id" });

PlaylistMedia.addHook("afterUpdate", async (playlistMedia: any) => {
  await addActivity("PlaylistMedia", "updated", playlistMedia);
});

PlaylistMedia.addHook("afterCreate", async (playlistMedia: PlaylistMedia) => {
  await addActivity("PlaylistMedia", "created", playlistMedia);
});

PlaylistMedia.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

