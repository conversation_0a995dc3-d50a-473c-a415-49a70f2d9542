"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { WsrDetail } from "./WsrDetail";
import { PaymentTypeCategory } from "./PaymentTypeCategory";

interface wsrItemAttributes {
  id: number;
  wsr_detail_id: number;
  payment_type_category_id: number
  wsr_amount: number;
  reference_id: number;
  wsr_item_status: string;
  created_by: number;
  updated_by: number;
}

export enum wsr_item_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = 'deleted'
}

export class WsrItem
  extends Model<wsrItemAttributes, never>
  implements wsrItemAttributes {
  id!: number;
  wsr_detail_id!: number;
  payment_type_category_id!: number
  wsr_amount!: number;
  reference_id!: number;
  wsr_item_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

WsrItem.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    wsr_detail_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    payment_type_category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    wsr_amount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
    },
    reference_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    wsr_item_status: {
      type: DataTypes.ENUM,
      values: Object.values(wsr_item_status),
      defaultValue: wsr_item_status.ACTIVE,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_wsr_items",
    modelName: "WsrItem",
  },
);

WsrItem.belongsTo(WsrDetail, { foreignKey: "wsr_detail_id", as: "wsr_item" });
WsrDetail.hasMany(WsrItem, { foreignKey: "wsr_detail_id", as: "wsr_detail" });

WsrItem.belongsTo(PaymentTypeCategory, { foreignKey: "payment_type_category_id", as: "wsr_item_type" });
PaymentTypeCategory.hasMany(WsrItem, { foreignKey: "payment_type_category_id", as: "wsr_detail_type" });


// Define hooks for wst item model
WsrItem.addHook("afterUpdate", async (wsrItem: any) => {
  await addActivity("WsrItem", "updated", wsrItem);
});

WsrItem.addHook("afterCreate", async (wsrItem: WsrItem) => {
  await addActivity("WsrItem", "created", wsrItem);
});

WsrItem.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

