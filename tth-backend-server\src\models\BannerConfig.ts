"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

interface bannerConfigAttributes {
    id: number;
    key: string;
    value: string;
    created_by: number | null;
    updated_by: number | null;
    createdAt: Date;
    updatedAt: Date;
}

export class BannerConfig
    extends Model<bannerConfigAttributes, never>
    implements bannerConfigAttributes {
    id!: number;
    key!: string;
    value!: string;
    created_by!: number | null;
    updated_by!: number | null;
    createdAt!: Date;
    updatedAt!: Date;
}

BannerConfig.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        key: {
            type: DataTypes.TEXT('long'),
            allowNull: false,
        },
        value: {
            type: DataTypes.TEXT('long'),
            allowNull: false,
        },
        created_by: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        updated_by: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_banner_configs",
        modelName: "BannerConfig",
    }
);
