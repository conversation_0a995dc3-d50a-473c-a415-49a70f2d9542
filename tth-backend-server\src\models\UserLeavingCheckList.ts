"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { CheckList } from "./CheckList";
import { addActivity } from "../helper/queue.service";

interface userCheckListAttributes {
  checklist_id: number;
  to_user_id: number;
  from_user_id: number;
  status: string;
  created_by: number;
  updated_by: number;
}

/** Role enum  for status*/
export enum user_leaving_checklist_status {
  COMPLETED = "completed",
  PENDING = "pending",
}

export class UserLeavingCheckList
  extends Model<userCheckListAttributes, never>
  implements userCheckListAttributes {
  checklist_id!: number;
  to_user_id!: number
  from_user_id!: number;
  status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

UserLeavingCheckList.init(
  {
    checklist_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM,
      values: Object.values(user_leaving_checklist_status),
      defaultValue: user_leaving_checklist_status.PENDING,
    },
    to_user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    from_user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_user_leaving_checklist",
    modelName: "UserLeavingCheckList",
  },
);
UserLeavingCheckList.removeAttribute("id");

UserLeavingCheckList.belongsTo(CheckList, { foreignKey: "checklist_id", as: "user" });
CheckList.hasMany(UserLeavingCheckList, {
  foreignKey: "checklist_id",
  as: "userLeavingCheckList",
});

// UserLeavingCheckList.belongsTo(User, {
//   foreignKey: "to_user_id",
//   as: "leaving_to_user_list",
// });
// User.hasMany(UserLeavingCheckList, { foreignKey: "to_user_id", as: "leaving_to_user" });

// UserLeavingCheckList.belongsTo(User, {
//   foreignKey: "from_user_id",
//   as: "leaving_from_user_list",
// });
// User.hasMany(UserLeavingCheckList, { foreignKey: "from_user_id", as: "leaving_from_user" });


UserLeavingCheckList.addHook("afterUpdate", async (UserLeavingCheckList: any) => {
  await addActivity("UserLeavingCheckList", "updated", UserLeavingCheckList);
});

UserLeavingCheckList.addHook("afterCreate", async (UserLeavingCheckList: UserLeavingCheckList) => {
  await addActivity("UserLeavingCheckList", "created", UserLeavingCheckList);
});

UserLeavingCheckList.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

