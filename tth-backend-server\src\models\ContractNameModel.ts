"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface ContractNameTypeAttributes {
    id: number;
    contract_name: string;
    organization_id: string;
    is_default: boolean;
    created_by: number;
    updated_by: number;
}

export class ContractNameModel
    extends Model<ContractNameTypeAttributes, never>
    implements ContractNameTypeAttributes {
    public id!: number;
    public contract_name!: string;
    public organization_id!: string;
    public is_default!: boolean;
    public created_by!: number;
    public updated_by!: number;

    public readonly createdAt!: Date;
    public readonly updatedAt!: Date;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}


ContractNameModel.init(
    {
        id: {
            type: DataTypes.INTEGER.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
        },
        contract_name: {
            type: DataTypes.TEXT('long'),
            allowNull: false,
        },
        organization_id: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        is_default: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_contract_name",
        modelName: "ContractNameModel",
    },
);


// Define hooks for contract type model
ContractNameModel.addHook("afterUpdate", async (ContractNameModel: any) => {
    await addActivity("ContractNameModel", "updated", ContractNameModel);
});

ContractNameModel.addHook("afterCreate", async (contractNameModel: ContractNameModel) => {
    await addActivity("ContractNameModel", "created", contractNameModel);
});

ContractNameModel.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});