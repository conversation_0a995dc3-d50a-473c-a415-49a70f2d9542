"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { UserLeavePolicy } from "./UserLeavePolicy";

interface userLeavePolicyHistoryAttributes {
  leave_user_policy_id: number;
  user_leave_policy_history_status: string;
  leave_total_hour_balance: number;
  leave_current_hour_balance: number;
  leave_total_day_balance: number;
  leave_current_day_balance: number;
  leave_accural_date: Date;
  leave_balance_start_date: Date;
  leave_balance_end_date: Date;
  carry_forward_hour_balance: number;
  carry_forward_day_balance: number;
  created_by: number;
  updated_by: number;
}

export class UserLeavePolicyHistory
  extends Model<userLeavePolicyHistoryAttributes>
  implements userLeavePolicyHistoryAttributes {
  leave_user_policy_id!: number;
  user_leave_policy_history_status!: string;
  leave_total_hour_balance!: number;
  leave_current_hour_balance!: number;
  leave_total_day_balance!: number;
  leave_current_day_balance!: number;
  carry_forward_balance!: number;
  leave_accural_date!: Date;
  leave_balance_start_date!: Date;
  leave_balance_end_date!: Date;
  carry_forward_hour_balance!: number;
  carry_forward_day_balance!: number;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

export enum user_leave_policy_history_status {
  ACTIVE = "active",
  INACTIVE = "inactive"
}

UserLeavePolicyHistory.init(
  {
    leave_user_policy_id: {
      type: DataTypes.INTEGER,
    },
    user_leave_policy_history_status: {
      type: DataTypes.ENUM,
      values: Object.values(user_leave_policy_history_status),
      defaultValue: user_leave_policy_history_status.ACTIVE,
    },
    leave_total_hour_balance: {
      type: DataTypes.FLOAT,
      defaultValue: 0
    },
    leave_current_hour_balance: {
      type: DataTypes.FLOAT,
      defaultValue: 0
    },
    leave_total_day_balance: {
      type: DataTypes.FLOAT,
      defaultValue: 0
    },
    leave_current_day_balance: {
      type: DataTypes.FLOAT,
      defaultValue: 0
    },
    leave_accural_date: {
      type: DataTypes.DATE,
      allowNull: true
    },
    leave_balance_start_date: {
      type: DataTypes.DATE,
      allowNull: true
    },
    leave_balance_end_date: {
      type: DataTypes.DATE,
      allowNull: true
    },
    carry_forward_hour_balance: {
      type: DataTypes.FLOAT,
      defaultValue: 0
    },
    carry_forward_day_balance: {
      type: DataTypes.FLOAT,
      defaultValue: 0
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER
    }
  },
  {
    sequelize: sequelize,
    tableName: "nv_user_leave_policy_history",
    modelName: "UserLeavePolicyHistory",
    timestamps: true,
  },
);

UserLeavePolicyHistory.removeAttribute("id");
UserLeavePolicyHistory.belongsTo(UserLeavePolicy, { foreignKey: "leave_user_policy_id", as: "user_leave_policy_history" });
UserLeavePolicy.hasMany(UserLeavePolicyHistory, { foreignKey: "leave_user_policy_id", as: "user_leave_policy_history_list" });


UserLeavePolicyHistory.addHook("afterUpdate", async (userLeavePolicyHistory: any) => {
  await addActivity("UserLeavePolicy", "updated", userLeavePolicyHistory);
});

UserLeavePolicyHistory.addHook("afterCreate", async (userLeavePolicyHistory: UserLeavePolicyHistory) => {
  await addActivity("UserLeavePolicy", "created", userLeavePolicyHistory);
});

UserLeavePolicyHistory.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
