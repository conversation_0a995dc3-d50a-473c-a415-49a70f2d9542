"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface leaveApprovalMetaPolicyAttributes {
    leave_accural_policy_id: number;
    leave_policy_leval: number;
    user_id: number;
    auto_approve_or_skip: boolean;
    auto_approve_or_skip_days: number;
    approval_type: string;
}

export enum approval_type {
    LEAVE = "leave",
    ENCASEMENT = "encashment",
}



export class LeaveApprovalMetaPolicy
    extends Model<leaveApprovalMetaPolicyAttributes, never>
    implements leaveApprovalMetaPolicyAttributes {
    leave_accural_policy_id!: number;
    leave_policy_leval!: number;
    user_id!: number;
    auto_approve_or_skip!: boolean;
    auto_approve_or_skip_days!: number;
    approval_type!: string

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

LeaveApprovalMetaPolicy.init(
    {
        leave_accural_policy_id: {
            type: DataTypes.INTEGER
        },
        leave_policy_leval: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        auto_approve_or_skip: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        auto_approve_or_skip_days: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        approval_type: {
            type: DataTypes.ENUM,
            values: Object.values(approval_type),
            allowNull: true,
        }
    },
    {
        sequelize: sequelize,
        tableName: "nv_leave_approval_meta_policy",
        modelName: "LeaveApprovalMetaPolicy",
    },
);

LeaveApprovalMetaPolicy.removeAttribute("id");


LeaveApprovalMetaPolicy.addHook("afterUpdate", async (LeaveApprovalMetaPolicy: any) => {
    await addActivity("LeaveApprovalMetaPolicy", "updated", LeaveApprovalMetaPolicy);
});

LeaveApprovalMetaPolicy.addHook("afterCreate", async (LeaveApprovalMetaPolicy: LeaveApprovalMetaPolicy) => {
    await addActivity("LeaveApprovalMetaPolicy", "created", LeaveApprovalMetaPolicy);
});

LeaveApprovalMetaPolicy.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});

