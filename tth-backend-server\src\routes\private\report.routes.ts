import { Router } from "express";
import reportController from "../../controller/report.controller";
import reportValidator from "../../validators/report.validator";

const router: Router = Router();


router.get("/filter-list", reportController.getFilterList);

router.get("/filter-category-list", reportController.getFilterCategoryList);

router.post("/save-filter", reportValidator.saveReportFilter(), reportController.saveReportFilter)

router.put("/update-filter/:filter_id", reportValidator.updateReportFilter(), reportController.updateReportFilter)

router.get("/get-user-filter-by-id/:filter_id", reportController.getUserReportFilter);

router.get("/get-user-filter-list/:user_id", reportController.getUserReportFilterList);

router.delete("/remove-user-filter/:filter_id", reportController.deleteUserReportFilter);

router.post("/get-filter-column", reportController.getFilterColumnList);

export default router;
