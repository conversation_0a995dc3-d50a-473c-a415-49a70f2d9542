import { Router } from "express";
import forecastController from "../../controller/forecast.controller";
const router: Router = Router();

router.post("/add", forecastController.createForecast);

router.get("/get-single-forecast/:forecast_id", forecastController.getForecastById);

router.get("/get-forecast-list", forecastController.getForecastList);

router.get("/get-forecast-category", forecastController.getForecastCategoryByBranch);

router.put("/update-forecast/:forecast_id", forecastController.updateForecast)

router.put("/lock-forecast/:forecast_id", forecastController.makeForecastLocked)

router.get("/get-forecast-budget-details/:forecast_id", forecastController.getForecastBudgetDetails);

router.get("/get-forecast-chart-list", forecastController.getForecastChartList);

router.get("/download-forecast-report", forecastController.downloadPdfExcel);

router.post("/add-forecast-bugdet", forecastController.saveForecastBudgetData);

router.get("/get-forecast-history-list", forecastController.getForecastHistoryList);

router.get("/get-forecast-history-details/:forecast_history_id", forecastController.getForecastHistoryDetails);


/** assign budget API */
router.post("/assign-budget", forecastController.assignBudget);

/** assign budget API */
router.delete("/delete-revoke-budget/:id", forecastController.deleteBudget);

/** delete budget history */
router.get("/delete-budget-history", forecastController.deleteBudgetHistory);

export default router;
