"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { PaymentType } from "./PaymentType";


interface PaymentTypeCategoryAttributes {
  id: number;
  payment_type_id: number;
  payment_type_category_title: string;
  payment_type_category_status: string;
  payment_type_category_pattern: string;
  payment_type_category_data_type: string;
  payment_type_category_order: number;
  payment_type_category_remarks: string;
  created_by: number;
  updated_by: number;
}

export enum payment_type_category_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = 'deleted'
}

export enum payment_type_category_data_type {
  STRING = "string",
  INTEGER = "integer"
}

export enum payment_type_category_pattern {
  SINGLE = "single",
  MULTIPLE = "multiple"
}

export class PaymentTypeCategory
  extends Model<PaymentTypeCategoryAttributes, never>
  implements PaymentTypeCategoryAttributes {
  id!: number;
  payment_type_id!: number;
  payment_type_category_title!: string;
  payment_type_category_status!: string;
  payment_type_category_pattern!: string;
  payment_type_category_data_type!: string;
  payment_type_category_order!: number;
  payment_type_category_remarks!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

PaymentTypeCategory.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    payment_type_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    payment_type_category_title: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    payment_type_category_status: {
      type: DataTypes.ENUM,
      values: Object.values(payment_type_category_status),
      defaultValue: payment_type_category_status.ACTIVE,
    },
    payment_type_category_data_type: {
      type: DataTypes.ENUM,
      values: Object.values(payment_type_category_data_type),
      allowNull: true,
    },
    payment_type_category_pattern: {
      type: DataTypes.ENUM,
      values: Object.values(payment_type_category_pattern),
      allowNull: true,
    },
    payment_type_category_order: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    payment_type_category_remarks: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_payment_type_category",
    modelName: "PaymentTypeCategory",
  },
);

PaymentTypeCategory.belongsTo(PaymentType, { foreignKey: "payment_type_id", as: "payment_type_list" });
PaymentType.hasMany(PaymentTypeCategory, { foreignKey: "payment_type_id", as: "payment_type_category" });

// Define hooks for Card model
PaymentTypeCategory.addHook("afterUpdate", async (paymentTypeCategory: any) => {
  await addActivity("PaymentTypeCategory", "updated", paymentTypeCategory);
});

PaymentTypeCategory.addHook("afterCreate", async (paymentTypeCategory: PaymentTypeCategory) => {
  await addActivity("PaymentTypeCategory", "created", paymentTypeCategory);
});

PaymentTypeCategory.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

