"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

interface roleAttributes {
  id: number;
  role_name: string;
  role_status: string;
  parent_role_id: number;
  created_by: number;
  updated_by: number;
}

/** Role enum  for status*/
export enum role_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export class Role
  extends Model<roleAttributes, never>
  implements roleAttributes
{
  id!: number;
  role_name!: string;
  role_status!: string;
  parent_role_id!: number;
  created_by!: number;
  updated_by!: number;
}

Role.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    role_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    role_status: {
      type: DataTypes.ENUM,
      values: Object.values(role_status),
    },
    parent_role_id: {
      type: DataTypes.INTEGER,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_roles",
    modelName: "Role",
    timestamps: true,
  },
);
