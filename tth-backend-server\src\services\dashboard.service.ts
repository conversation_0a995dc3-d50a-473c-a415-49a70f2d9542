import { User, user_status } from "../models/User";
import { UserEmploymentContract, contract_status } from "../models/UserEmployementContract";
import { UserRequest, request_status } from "../models/UserRequest";
import { UserMeta } from "../models/UserMeta";
import { Branch, branch_status } from "../models/Branch";
import { DsrDetail, dsr_detail_status } from "../models/DsrDetail";
import { DsrItem, dsr_item_status } from "../models/DsrItem";
import { PaymentType, payment_type_usage } from "../models/PaymentType";
import { PaymentTypeCategory } from "../models/PaymentTypeCategory";
import { Op, QueryTypes } from "sequelize";
import { sequelize } from "../models";
import { generateDashboardWhereClause, getTimePeriodConfig } from "../helper/common";
import moment from "moment";

// Configure moment.js for consistent behavior
moment.locale('en'); // Set default locale

/**
 * Dashboard Service
 * Contains data functions for retrieving dashboard widgets
 */

// Enums for chart identification
enum ChartType {
    ONBOARDING_PIE = 'onboarding_pie',
    ONBOARDING_BAR = 'onboarding_bar',
    CONTRACT_GAUGE = 'contract_gauge',
    LEAVE_COMPARISON = 'leave_comparison',
    SALES_COMPARISON = 'sales_comparison'
}

// Types for Recharts compatibility
interface RechartsDataPoint {
    name: string;
    value: number;
    [key: string]: any; // Allow additional properties for multi-series data
}

interface RechartsLineBarData {
    data: RechartsDataPoint[];
    xAxisKey: string;
    yAxisKey: string;
    series: {
        dataKey: string;
        name: string;
        color: string;
        type?: 'line' | 'bar';
    }[];
}

interface RechartsPieData {
    data: RechartsDataPoint[];
    nameKey: string;
    valueKey: string;
    colors: string[];
}

interface CommonChartResponse {
    success: boolean;
    chartType: 'line' | 'bar' | 'pie' | 'gauge' | 'multi_line' | 'meter';
    title: string;
    data: RechartsLineBarData | RechartsPieData | any; // Added 'any' for gauge data
    metadata?: {
        totalRecords: number;
        dateRange?: string;
        timelinePeriod?: string;
        branchCount?: number;
        filters?: any;
        lastUpdated: string;
        [key: string]: any; // Allow additional metadata for gauge charts
    };
}

// Chart configuration interface
interface ChartConfig {
    type: ChartType;
    widgetType: string;
    title: string;
    chartType: 'line' | 'bar' | 'pie' | 'gauge' | 'multi_line' | 'meter';
    category: 'chart';
    description: string;
    hasFilters: boolean;
    dataFetcher: (organizationId: string, filters?: FilterOptions) => Promise<CommonChartResponse>;
}

interface FilterOptions {
    branchId?: number;
    widgetType?: "counts" | "charts" | "all";
    chartType?: "line" | "bar" | "pie" | "gauge" | "multi_line" | "meter";
}

// Cache for frequently accessed data (in production, use Redis or similar)
const dataCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get cached data or fetch if expired
 */
const getCachedData = async <T>(
    key: string,
    fetchFn: () => Promise<T>,
    ttl: number = CACHE_TTL
): Promise<T> => {
    const cached = dataCache.get(key);
    const now = Date.now();

    if (cached && (now - cached.timestamp) < ttl) {
        return cached.data as T;
    }

    const data = await fetchFn();
    dataCache.set(key, { data, timestamp: now });
    return data;
};

/**
 * Validate required parameters
 */
const validateParameters = (organizationId: string, userId: number): void => {
    if (!organizationId || typeof organizationId !== 'string') {
        throw new Error('Invalid organizationId: must be a non-empty string');
    }
    if (!userId || typeof userId !== 'number' || userId <= 0) {
        throw new Error('Invalid userId: must be a positive number');
    }
};

/**
 * Generate date range for the last N months using Moment.js
 */
const generateMonthRange = (months: number): { start: Date; monthKeys: string[]; monthLabels: string[] } => {
    const start = moment().subtract(months, 'months').startOf('month').toDate();

    const monthKeys: string[] = [];
    const monthLabels: string[] = [];

    for (let i = months - 1; i >= 0; i--) {
        const monthMoment = moment().subtract(i, 'months');
        monthKeys.push(monthMoment.format('YYYY-MM'));
        monthLabels.push(monthMoment.format('MMM YYYY'));
    }

    return { start, monthKeys, monthLabels };
};

/**
 * Utility functions for consistent date handling with Moment.js
 */
const DateUtils = {
    /**
     * Validate and format date to YYYY-MM format for month grouping
     */
    toMonthKey: (date: string | Date | moment.Moment): string => {
        const momentDate = moment(date);
        if (!momentDate.isValid()) {
            console.warn(`Invalid date provided to toMonthKey: ${date}`);
            return moment().format('YYYY-MM'); // Return current month as fallback
        }
        return momentDate.format('YYYY-MM');
    },

    /**
     * Format date to human-readable month label
     */
    toMonthLabel: (date: string | Date | moment.Moment): string => {
        const momentDate = moment(date);
        if (!momentDate.isValid()) {
            console.warn(`Invalid date provided to toMonthLabel: ${date}`);
            return moment().format('MMM YYYY'); // Return current month as fallback
        }
        return momentDate.format('MMM YYYY');
    },

    /**
     * Check if date is within the last N months
     */
    isWithinLastMonths: (date: string | Date | moment.Moment, months: number): boolean => {
        const momentDate = moment(date);
        if (!momentDate.isValid()) {
            console.warn(`Invalid date provided to isWithinLastMonths: ${date}`);
            return false;
        }
        const cutoffDate = moment().subtract(months, 'months').startOf('month');
        return momentDate.isAfter(cutoffDate);
    },

    /**
     * Get start of month for a given date
     */
    startOfMonth: (date: string | Date | moment.Moment): Date => {
        const momentDate = moment(date);
        if (!momentDate.isValid()) {
            console.warn(`Invalid date provided to startOfMonth: ${date}`);
            return moment().startOf('month').toDate(); // Return current month start as fallback
        }
        return momentDate.startOf('month').toDate();
    },

    /**
     * Format date for display with validation
     */
    formatForDisplay: (date: string | Date | moment.Moment): string => {
        const momentDate = moment(date);
        if (!momentDate.isValid()) {
            console.warn(`Invalid date provided to formatForDisplay: ${date}`);
            return moment().format('YYYY-MM-DD'); // Return current date as fallback
        }
        return momentDate.format('YYYY-MM-DD');
    },

    /**
     * Get relative time from now (e.g., "2 days ago")
     */
    fromNow: (date: string | Date | moment.Moment): string => {
        const momentDate = moment(date);
        if (!momentDate.isValid()) {
            console.warn(`Invalid date provided to fromNow: ${date}`);
            return 'Invalid date';
        }
        return momentDate.fromNow();
    },

    /**
     * Check if date is valid
     */
    isValid: (date: string | Date | moment.Moment): boolean => {
        return moment(date).isValid();
    },

    /**
     * Get current timestamp in ISO format
     */
    now: (): string => {
        return moment().toISOString();
    }
};

/**
 * Get onboarding pipeline status data for pie chart (Recharts format)
 * Based on getOnboardingPipelineBarData but converted to pie chart format
 * Shows distribution of Completed and Verified users across all branches
 * Supports branch filtering to show data for specific branch only
 */
const getOnboardingPipelinePieData = async (organizationId: string, filters?: FilterOptions): Promise<CommonChartResponse> => {
    if (!organizationId) {
        throw new Error('organizationId is required');
    }

    const cacheKey = `onboarding_pie_${organizationId}_${filters?.branchId || 'all'}`;

    return getCachedData(cacheKey, async () => {
        try {
            // Build where clauses for branches and users
            const branchWhere: any = {
                organization_id: organizationId,
                branch_status: branch_status.ACTIVE
            };

            const userWhere: any = {
                organization_id: organizationId
            };

            // Apply branch filtering if specified
            if (filters?.branchId) {
                branchWhere.id = filters.branchId;
                userWhere.branch_id = filters.branchId;
            }

            // Execute queries in parallel for better performance
            const [branches, users] = await Promise.all([
                Branch.findAll({
                    where: branchWhere,
                    attributes: ['id', 'branch_name'],
                    raw: true
                }),
                User.findAll({
                    where: userWhere,
                    attributes: ['id', 'branch_id', 'user_status'],
                    raw: true
                })
            ]);

            if (branches.length === 0) {
                return {
                    success: true,
                    chartType: 'pie',
                    title: 'Onboarding Pipeline Distribution',
                    data: {
                        data: [],
                        nameKey: 'name',
                        valueKey: 'value',
                        colors: ['#10B981', '#8B5CF6']
                    } as RechartsPieData,
                    metadata: {
                        totalRecords: 0,
                        lastUpdated: DateUtils.now()
                    }
                };
            }

            // Initialize status counters (only Completed and Verified as per line chart logic)
            const statusCounts = {
                'Completed': 0,
                'Verified': 0
            };

            // Aggregate user counts by status (only Completed and Verified)
            users.forEach((user: any) => {
                switch (user.user_status) {
                    case user_status.COMPLETED:
                        statusCounts.Completed++;
                        break;
                    case user_status.VERIFIED:
                        statusCounts.Verified++;
                        break;
                    // Skip pending, active, and ongoing statuses as per line chart logic
                }
            });

            // Define colors for each status
            const statusColors = {
                'Completed': '#10B981',  // Green
                'Verified': '#8B5CF6'    // Purple
            };

            // Format data for Recharts pie chart
            const chartData: RechartsDataPoint[] = Object.entries(statusCounts)
                .filter(([, count]) => count > 0) // Only include statuses with data
                .map(([status, count]) => ({
                    name: status,
                    value: count,
                    status: status,
                    count: count,
                    color: statusColors[status as keyof typeof statusColors]
                }));

            const totalRecords = chartData.reduce((sum, item) => sum + item.value, 0);

            // Convert statusColors object to array for RechartsPieData compatibility
            const colorsArray = Object.values(statusColors);

            return {
                success: true,
                chartType: 'pie',
                title: 'Onboarding Pipeline Distribution',
                data: {
                    data: chartData,
                    nameKey: 'name',
                    valueKey: 'value',
                    colors: colorsArray
                } as RechartsPieData,
                metadata: {
                    totalRecords,
                    filters: filters,
                    lastUpdated: DateUtils.now()
                }
            };
        } catch (error) {
            console.error('Error in getOnboardingPipelinePieData:', error);
            return {
                success: false,
                chartType: 'pie',
                title: 'Onboarding Pipeline Distribution',
                data: {
                    data: [],
                    nameKey: 'name',
                    valueKey: 'value',
                    colors: ['#10B981', '#8B5CF6']
                } as RechartsPieData,
                metadata: {
                    totalRecords: 0,
                    lastUpdated: DateUtils.now()
                }
            };
        }
    });
};

/**
 * Get onboarding pipeline status data for bar chart (Recharts format)
 * Based on getOnboardingPipelineLineData but converted to bar chart format
 * X-axis: Branches, Y-axis: User counts
 * 2 data series: Completed, Verified (matching line chart logic)
 */
const getOnboardingPipelineBarData = async (organizationId: string, filters?: FilterOptions): Promise<CommonChartResponse> => {
    if (!organizationId) {
        throw new Error('organizationId is required');
    }

    const cacheKey = `onboarding_bar_${organizationId}_${filters?.branchId || 'all'}`;

    return getCachedData(cacheKey, async () => {
        try {
            // Build where clauses for branches and users
            const branchWhere: any = {
                organization_id: organizationId,
                branch_status: branch_status.ACTIVE
            };

            const userWhere: any = {
                organization_id: organizationId
            };

            // Apply branch filtering if specified
            if (filters?.branchId) {
                branchWhere.id = filters.branchId;
                userWhere.branch_id = filters.branchId;
            }

            // Execute queries in parallel for better performance
            const [branches, users] = await Promise.all([
                Branch.findAll({
                    where: branchWhere,
                    attributes: ['id', 'branch_name'],
                    raw: true
                }),
                User.findAll({
                    where: userWhere,
                    attributes: ['id', 'branch_id', 'user_status'],
                    raw: true
                })
            ]);

            if (branches.length === 0) {
                return {
                    success: true,
                    chartType: 'bar',
                    title: 'Onboarding Pipeline Status by Branch',
                    data: {
                        data: [],
                        xAxisKey: 'name',
                        yAxisKey: 'value',
                        series: [
                            {
                                dataKey: 'completed',
                                name: 'Completed',
                                color: '#10B981',
                                type: 'bar'
                            },
                            {
                                dataKey: 'verified',
                                name: 'Verified',
                                color: '#8B5CF6',
                                type: 'bar'
                            }
                        ]
                    } as RechartsLineBarData,
                    metadata: {
                        totalRecords: 0,
                        lastUpdated: DateUtils.now()
                    }
                };
            }

            // Initialize branch data structure (only completed and verified as per line chart)
            const branchData = new Map<number, {
                name: string;
                completed: number;
                verified: number;
            }>();

            // Initialize all branches with zero counts
            branches.forEach((branch: any) => {
                branchData.set(branch.id, {
                    name: branch.branch_name,
                    completed: 0,
                    verified: 0
                });
            });

            // Aggregate user counts by branch and status (only completed and verified)
            users.forEach((user: any) => {
                const branchInfo = branchData.get(user.branch_id);
                if (branchInfo) {
                    switch (user.user_status) {
                        case user_status.COMPLETED:
                            branchInfo.completed++;
                            break;
                        case user_status.VERIFIED:
                            branchInfo.verified++;
                            break;
                        // Skip pending, active, and ongoing statuses as per line chart logic
                    }
                }
            });

            // Transform to Recharts format
            const chartData: RechartsDataPoint[] = Array.from(branchData.values()).map(branch => ({
                name: branch.name,
                value: branch.completed + branch.verified, // Total for reference
                completed: branch.completed,
                verified: branch.verified
            }));

            const totalRecords = users.length;

            return {
                success: true,
                chartType: 'bar',
                title: 'Onboarding Pipeline Status by Branch',
                data: {
                    data: chartData,
                    xAxisKey: 'name',
                    yAxisKey: 'value',
                    series: [
                        {
                            dataKey: 'completed',
                            name: 'Completed',
                            color: '#10B981',
                            type: 'bar'
                        },
                        {
                            dataKey: 'verified',
                            name: 'Verified',
                            color: '#8B5CF6',
                            type: 'bar'
                        }
                    ]
                } as RechartsLineBarData,
                metadata: {
                    totalRecords,
                    filters: filters,
                    lastUpdated: DateUtils.now()
                }
            };
        } catch (error) {
            console.error('Error in getOnboardingPipelineBarData:', error);
            return {
                success: false,
                chartType: 'bar',
                title: 'Onboarding Pipeline Status by Branch',
                data: {
                    data: [],
                    xAxisKey: 'name',
                    yAxisKey: 'value',
                    series: [
                        {
                            dataKey: 'completed',
                            name: 'Completed',
                            color: '#10B981',
                            type: 'bar'
                        },
                        {
                            dataKey: 'verified',
                            name: 'Verified',
                            color: '#8B5CF6',
                            type: 'bar'
                        }
                    ]
                } as RechartsLineBarData,
                metadata: {
                    totalRecords: 0,
                    lastUpdated: DateUtils.now()
                }
            };
        }
    });
};

/**
 * Get user contract status data for gauge chart (Recharts format)
 * Based on getUserContractPieData but converted to gauge/meter chart format
 * Shows contract health metrics: percentage of healthy vs problematic contracts
 * Healthy: Confirmed + Probation
 * Problematic: Expired + Expiry Soon + Pending + Awaiting Signature
 */
const getUserContractGaugeData = async (organizationId: string, filters?: FilterOptions): Promise<CommonChartResponse> => {
    if (!organizationId) {
        throw new Error('organizationId is required');
    }

    const cacheKey = `contract_gauge_${organizationId}_${filters?.branchId || 'all'}`;

    return getCachedData(cacheKey, async () => {
        try {
            // Build where clause for User table
            const userWhere: any = {
                organization_id: organizationId
            };

            if (filters?.branchId) {
                userWhere.branch_id = filters.branchId;
            }

            // Get all users with their contracts and meta data for probation calculation
            const usersWithContracts = await User.findAll({
                include: [
                    {
                        model: UserEmploymentContract,
                        as: 'user_contract',
                        attributes: ['contract_status', 'expire_date', 'is_confirm_sign'],
                        where: {
                            contract_status: contract_status.ACTIVE
                        },
                        required: true
                    },
                    {
                        model: UserMeta,
                        as: 'user_meta',
                        attributes: ['probation_length'],
                        required: false
                    }
                ],
                attributes: ['id', 'user_joining_date'],
                where: userWhere,
                raw: true,
                nest: true
            });

            // Initialize status counters
            const statusCounts = {
                'Expired': 0,
                'Expiry Soon': 0,
                'Probation': 0,
                'Confirmed': 0,
                'Pending': 0,
                'Awaiting Signature': 0
            };

            // Process each user and categorize based on frontend logic
            usersWithContracts.forEach((user: any) => {
                const currentDate = moment().startOf('day');
                const expireDate = moment(user.user_contract.expire_date).startOf('day');
                const daysDifference = expireDate.diff(currentDate, 'days');

                // Determine probation status
                const joiningDate = moment(user.user_joining_date);
                const probationLength = user.user_meta?.probation_length || 0;
                const probationEndDate = joiningDate.clone().add(probationLength, 'days');
                const isProbation = probationLength > 0 && moment().isBefore(probationEndDate) ? 1 : 0;

                // Apply the same logic as frontend
                let status: string;

                if (daysDifference <= 0) {
                    status = 'Expired';
                } else if (daysDifference > 0 && daysDifference <= 15) {
                    status = 'Expiry Soon';
                } else if (user.user_contract.is_confirm_sign === true || user.user_contract.is_confirm_sign === 1) {
                    status = isProbation === 1 ? 'Probation' : 'Confirmed';
                } else if (user.user_contract.is_confirm_sign === null) {
                    status = 'Pending';
                } else {
                    status = 'Awaiting Signature';
                }

                statusCounts[status as keyof typeof statusCounts]++;
            });

            // Calculate health metrics
            const healthyCount = statusCounts.Confirmed + statusCounts.Probation;
            const problematicCount = statusCounts.Expired + statusCounts['Expiry Soon'] +
                statusCounts.Pending + statusCounts['Awaiting Signature'];
            const totalCount = healthyCount + problematicCount;

            const healthPercentage = totalCount > 0 ? Math.round((healthyCount / totalCount) * 100) : 0;

            // Format data for gauge chart
            const gaugeData = [
                {
                    name: 'Contract Health',
                    value: healthPercentage,
                    healthy: healthyCount,
                    problematic: problematicCount,
                    total: totalCount,
                    color: healthPercentage >= 80 ? '#10B981' : healthPercentage >= 60 ? '#F59E0B' : '#EF4444'
                }
            ];

            return {
                success: true,
                chartType: 'gauge',
                title: 'Contract Health Metrics',
                data: {
                    data: gaugeData,
                    nameKey: 'name',
                    valueKey: 'value',
                    maxValue: 100,
                    unit: '%',
                    colors: ['#EF4444', '#F59E0B', '#10B981'], // Red, Orange, Green
                    thresholds: [
                        { value: 60, color: '#EF4444', label: 'Poor' },
                        { value: 80, color: '#F59E0B', label: 'Fair' },
                        { value: 100, color: '#10B981', label: 'Good' }
                    ]
                },
                metadata: {
                    totalRecords: totalCount,
                    healthyContracts: healthyCount,
                    problematicContracts: problematicCount,
                    healthPercentage: healthPercentage,
                    breakdown: statusCounts,
                    filters: filters,
                    lastUpdated: DateUtils.now()
                }
            };
        } catch (error) {
            console.error('Error in getUserContractGaugeData:', error);
            return {
                success: false,
                chartType: 'gauge',
                title: 'Contract Health Metrics',
                data: {
                    data: [],
                    nameKey: 'name',
                    valueKey: 'value',
                    maxValue: 100,
                    unit: '%',
                    colors: ['#EF4444', '#F59E0B', '#10B981'],
                    thresholds: []
                },
                metadata: {
                    totalRecords: 0,
                    healthyContracts: 0,
                    problematicContracts: 0,
                    healthPercentage: 0,
                    breakdown: {
                        'Expired': 0,
                        'Expiry Soon': 0,
                        'Probation': 0,
                        'Confirmed': 0,
                        'Pending': 0,
                        'Awaiting Signature': 0
                    },
                    lastUpdated: DateUtils.now()
                }
            };
        }
    });
};

/**
 * Get leave comparison data by branch for line chart (Recharts format)
 * Shows 12-month timeline with each branch as a separate line
 * X-axis: 12-month timeline from current month back one year
 * Y-axis: Leave count values
 * Data series: One line per branch with branch colors from database
 */
const getLeaveComparisonByBranchData = async (organizationId: string, filters?: FilterOptions): Promise<CommonChartResponse> => {
    if (!organizationId) {
        throw new Error('organizationId is required');
    }

    const cacheKey = `leave_comparison_${organizationId}_${filters?.branchId || 'all'}`;

    return getCachedData(cacheKey, async () => {
        try {
            // Generate 12-month timeline starting from current month going back one year
            const { monthKeys, monthLabels } = generateMonthRange(12);

            // Build where clauses for branch filtering
            const branchWhere: any = {
                organization_id: organizationId,
                branch_status: branch_status.ACTIVE
            };

            // Build where clauses for user filtering
            const userWhere: any = {
                organization_id: organizationId
            };

            // Apply branch filtering if specified
            if (filters?.branchId) {
                branchWhere.id = filters.branchId;
                userWhere.branch_id = filters.branchId;
            }


            // Execute queries in parallel for better performance
            // Query ALL branches first (regardless of leave data), then query leave data separately
            const [branches, leaveData] = await Promise.all([
                // Query ALL active branches in organization (regardless of leave data)
                Branch.findAll({
                    where: branchWhere,
                    attributes: ['id', 'branch_name', 'branch_color'],
                    raw: true
                }),


                // Query leave requests with user branch information
                UserRequest.findAll({
                    where: {
                        request_type: 'casual',
                        request_status: request_status.APPROVED
                    },
                    include: [{
                        model: User,
                        as: 'request_from_users',
                        where: userWhere,
                        attributes: ['branch_id'],
                        required: true
                    }],
                    attributes: ['leave_days', 'createdAt'],
                    raw: true
                })
            ]);


            if (branches.length === 0) {
                return {
                    success: true,
                    chartType: 'line',
                    title: 'Leave Comparison by Branch (12-Month Timeline)',
                    data: {
                        data: [],
                        xAxisKey: 'name',
                        yAxisKey: 'value',
                        series: []
                    } as RechartsLineBarData,
                    metadata: {
                        totalRecords: 0,
                        timelinePeriod: '12 months',
                        branchCount: 0,
                        lastUpdated: DateUtils.now()
                    }
                };
            }

            // Create efficient lookup structures for 12-month data
            const leaveByBranchAndMonth = new Map<string, Map<string, number>>();

            // Initialize data structure - ensure all branches have data for all 12 months
            branches.forEach((branch: any) => {
                const monthMap = new Map<string, number>();
                monthKeys.forEach(key => monthMap.set(key, 0));
                leaveByBranchAndMonth.set(branch.id.toString(), monthMap);
            });

            // Single pass through leave data to aggregate using Moment.js for 12-month period
            leaveData.forEach((leave: any) => {
                if (leave.createdAt && leave['request_from_users.branch_id']) {
                    // Filter by 12-month date range using DateUtils
                    if (DateUtils.isWithinLastMonths(leave.createdAt, 12)) {
                        const monthKey = DateUtils.toMonthKey(leave.createdAt);
                        const branchId = leave['request_from_users.branch_id'].toString();
                        const branchData = leaveByBranchAndMonth.get(branchId);

                        if (branchData && branchData.has(monthKey)) {
                            const currentTotal = branchData.get(monthKey) || 0;
                            branchData.set(monthKey, currentTotal + (leave.leave_days || 0));
                        }
                    }
                }
            });

            // Format data for Recharts line chart - 12-month timeline with branch lines
            const chartData: RechartsDataPoint[] = monthLabels.map((label, index) => {
                const dataPoint: RechartsDataPoint = {
                    name: label,
                    value: 0, // Will be calculated as sum of all branches for total reference
                    month: label
                };

                // Add each branch's data as a separate property for line chart
                branches.forEach((branch: any) => {
                    const branchData = leaveByBranchAndMonth.get(branch.id.toString());
                    const branchValue = branchData?.get(monthKeys[index]) || 0;
                    // Only use branch name as dataKey (remove redundant branch_id property)
                    dataPoint[branch.branch_name] = branchValue;
                    dataPoint.value += branchValue;
                });

                return dataPoint;
            });

            // Generate series for each branch using database colors
            const defaultColors = ['#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];
            const series = branches.map((branch: any, index: number) => ({
                dataKey: branch.branch_name,
                name: branch.branch_name,
                color: branch.branch_color || defaultColors[index % defaultColors.length],
                type: 'line' as const
            }));

            const totalRecords = leaveData.length;

            return {
                success: true,
                chartType: 'line',
                title: 'Leave Comparison by Branch (12-Month Timeline)',
                data: {
                    data: chartData,
                    xAxisKey: 'name',
                    yAxisKey: 'value',
                    series
                } as RechartsLineBarData,
                metadata: {
                    totalRecords,
                    dateRange: `${monthLabels[0]} - ${monthLabels[monthLabels.length - 1]}`,
                    timelinePeriod: '12 months',
                    branchCount: branches.length,
                    filters: filters,
                    lastUpdated: DateUtils.now()
                }
            };
        } catch (error) {
            console.error('Error in getLeaveComparisonByBranchData:', error);
            return {
                success: false,
                chartType: 'line',
                title: 'Leave Comparison by Branch (12-Month Timeline)',
                data: {
                    data: [],
                    xAxisKey: 'name',
                    yAxisKey: 'value',
                    series: []
                } as RechartsLineBarData,
                metadata: {
                    totalRecords: 0,
                    timelinePeriod: '12 months',
                    branchCount: 0,
                    lastUpdated: DateUtils.now()
                }
            };
        }
    });
};

/**
 * Get sales comparison data by branch for line chart (Recharts format)
 * Shows 12-month timeline with each branch as a separate line
 * X-axis: 12-month timeline from current month back one year
 * Y-axis: Sales amount values (from DSR data)
 * Data series: One line per branch with branch colors from database
 */
const getSalesComparisonByBranchData = async (organizationId: string, filters?: any): Promise<CommonChartResponse> => {
    if (!organizationId) {
        throw new Error('organizationId is required');
    }

    const cacheKey = `sales_comparison_${organizationId}_${filters?.branchId || 'all'}`;

    return getCachedData(cacheKey, async () => {
        try {
            // Generate 12-month timeline (current month back to 12 months ago)
            const monthKeys: string[] = [];
            const monthLabels: string[] = [];

            for (let i = 11; i >= 0; i--) {
                const monthMoment = moment().subtract(i, 'months');
                monthKeys.push(monthMoment.format('YYYY-MM'));
                monthLabels.push(monthMoment.format('MMM YYYY'));
            }

            // Build where clauses for branches
            const branchWhere: any = {
                organization_id: organizationId,
                branch_status: branch_status.ACTIVE
            };

            // Apply branch filtering if specified
            if (filters?.branchId) {
                branchWhere.id = filters.branchId;
            }

            // Get branches and DSR data in parallel
            const [branches, dsrData] = await Promise.all([
                Branch.findAll({
                    where: branchWhere,
                    attributes: ['id', 'branch_name', 'branch_color'],
                    raw: true
                }),
                DsrDetail.findAll({
                    where: {
                        dsr_detail_status: dsr_detail_status.ACTIVE,
                        dsr_date: {
                            [Op.gte]: moment().subtract(11, 'months').startOf('month').format('YYYY-MM-DD'),
                            [Op.lte]: moment().endOf('month').format('YYYY-MM-DD')
                        },
                        ...(filters?.branchId && { branch_id: filters.branchId })
                    },
                    include: [{
                        model: DsrItem,
                        as: 'dsr_detail',
                        where: {
                            dsr_item_status: dsr_item_status.ACTIVE
                        },
                        include: [{
                            model: PaymentTypeCategory,
                            as: 'dsr_item_type',
                            include: [{
                                model: PaymentType,
                                as: 'payment_type_list',
                                where: {
                                    payment_type_usage: payment_type_usage.COLLECTION // Only income/sales
                                }
                            }]
                        }],
                        required: false
                    }],
                    raw: false
                })
            ]);

            if (branches.length === 0) {
                return {
                    success: true,
                    chartType: 'line',
                    title: 'Sales Comparison by Branch (12-Month Timeline)',
                    data: {
                        data: [],
                        xAxisKey: 'name',
                        yAxisKey: 'value',
                        series: []
                    } as RechartsLineBarData,
                    metadata: {
                        totalRecords: 0,
                        timelinePeriod: '12 months',
                        branchCount: 0,
                        lastUpdated: DateUtils.now()
                    }
                };
            }

            // Create efficient lookup structures for 12-month data
            const salesByBranchAndMonth = new Map<string, Map<string, number>>();

            // Initialize data structure - ensure all branches have data for all 12 months
            branches.forEach((branch: any) => {
                const monthMap = new Map<string, number>();
                monthKeys.forEach(key => monthMap.set(key, 0));
                salesByBranchAndMonth.set(branch.id.toString(), monthMap);
            });

            // Process DSR data and aggregate sales by branch and month
            dsrData.forEach((dsr: any) => {
                const branchId = dsr.branch_id.toString();
                const monthKey = moment(dsr.dsr_date).format('YYYY-MM');

                if (salesByBranchAndMonth.has(branchId) && monthKeys.includes(monthKey)) {
                    let totalSales = 0;

                    // Sum up all sales items for this DSR entry
                    if (dsr.dsr_detail && dsr.dsr_detail.length > 0) {
                        totalSales = dsr.dsr_detail.reduce((sum: number, item: any) => {
                            return sum + (parseFloat(item.dsr_amount) || 0);
                        }, 0);
                    }

                    const currentAmount = salesByBranchAndMonth.get(branchId)!.get(monthKey) || 0;
                    salesByBranchAndMonth.get(branchId)!.set(monthKey, currentAmount + totalSales);
                }
            });

            // Transform to Recharts format
            const chartData: RechartsDataPoint[] = monthLabels.map((label, index) => {
                const monthKey = monthKeys[index];
                const dataPoint: RechartsDataPoint = {
                    name: label,
                    value: 0 // This will be the sum of all branches for reference
                };

                // Add each branch's data for this month
                branches.forEach((branch: any) => {
                    const branchSales = salesByBranchAndMonth.get(branch.id.toString())?.get(monthKey) || 0;
                    dataPoint[`branch_${branch.id}`] = branchSales;
                    dataPoint.value += branchSales;
                });

                return dataPoint;
            });

            // Create series configuration for each branch
            const series = branches.map((branch: any, index: number) => ({
                dataKey: `branch_${branch.id}`,
                name: branch.branch_name,
                color: branch.branch_color || `hsl(${(index * 137.5) % 360}, 70%, 50%)`, // Fallback colors
                type: 'line' as const
            }));

            const totalRecords = dsrData.length;

            return {
                success: true,
                chartType: 'line',
                title: 'Sales Comparison by Branch (12-Month Timeline)',
                data: {
                    data: chartData,
                    xAxisKey: 'name',
                    yAxisKey: 'value',
                    series
                } as RechartsLineBarData,
                metadata: {
                    totalRecords,
                    dateRange: `${monthLabels[0]} - ${monthLabels[monthLabels.length - 1]}`,
                    timelinePeriod: '12 months',
                    branchCount: branches.length,
                    filters: filters,
                    lastUpdated: DateUtils.now()
                }
            };
        } catch (error) {
            console.error('Error in getSalesComparisonByBranchData:', error);
            return {
                success: false,
                chartType: 'line',
                title: 'Sales Comparison by Branch (12-Month Timeline)',
                data: {
                    data: [],
                    xAxisKey: 'name',
                    yAxisKey: 'value',
                    series: []
                } as RechartsLineBarData,
                metadata: {
                    totalRecords: 0,
                    timelinePeriod: '12 months',
                    branchCount: 0,
                    lastUpdated: DateUtils.now()
                }
            };
        }
    });
};

export interface DashboardWidget {
    id?: number;
    title: string;
    type: string;
    data: any;
    category: "count" | "chart";
    chartType?: "line" | "bar" | "pie" | "gauge" | "multi_line" | "meter";
    order?: number;
}

/**
 * Chart configuration mapping system
 * This eliminates hardcoded array indices and makes the system more maintainable
 */
const CHART_CONFIGURATIONS: Record<ChartType, ChartConfig> = {
    [ChartType.ONBOARDING_PIE]: {
        type: ChartType.ONBOARDING_PIE,
        widgetType: 'pie_chart',
        title: 'Onboarding Pipeline Distribution',
        chartType: 'pie',
        category: 'chart',
        description: 'Distribution of onboarding status: Completed and Verified users across branches',
        hasFilters: true,
        dataFetcher: getOnboardingPipelinePieData
    },
    [ChartType.ONBOARDING_BAR]: {
        type: ChartType.ONBOARDING_BAR,
        widgetType: 'bar_chart',
        title: 'Onboarding Pipeline Status by Branch',
        chartType: 'bar',
        category: 'chart',
        description: 'Compare onboarding status across branches with 2 series: Completed and Verified users',
        hasFilters: true,
        dataFetcher: getOnboardingPipelineBarData
    },

    [ChartType.CONTRACT_GAUGE]: {
        type: ChartType.CONTRACT_GAUGE,
        widgetType: 'gauge_chart',
        title: 'Contract Health Metrics',
        chartType: 'gauge',
        category: 'chart',
        description: 'Contract health gauge showing percentage of healthy vs problematic contracts',
        hasFilters: true,
        dataFetcher: getUserContractGaugeData
    },
    [ChartType.LEAVE_COMPARISON]: {
        type: ChartType.LEAVE_COMPARISON,
        widgetType: 'line_chart',
        title: 'Leave Comparison by Branch (12-Month Timeline)',
        chartType: 'line',
        category: 'chart',
        description: 'Monthly leave data comparison across branches',
        hasFilters: true,
        dataFetcher: getLeaveComparisonByBranchData
    },
    [ChartType.SALES_COMPARISON]: {
        type: ChartType.SALES_COMPARISON,
        widgetType: 'multi_line_chart',
        title: 'Income Category Comparison',
        chartType: 'multi_line',
        category: 'chart',
        description: 'Yearly income category comparison',
        hasFilters: true,
        dataFetcher: getSalesComparisonByBranchData
    }
};

/**
 * Get chart configurations based on filters
 */
const getFilteredChartConfigs = (filters?: FilterOptions): ChartConfig[] => {
    let configs = Object.values(CHART_CONFIGURATIONS);    // Filter by chart type if specified
    if (filters?.chartType) {
        configs = configs.filter(config => config.chartType === filters.chartType);
    }

    return configs;
};

/**
 * Create chart widget from configuration and data
 */
const createChartWidget = (config: ChartConfig, chartData: CommonChartResponse, index: number): DashboardWidget => {
    // Generate dynamic ID based on chart type and index
    const baseId = 5; // Start chart IDs after the 4 count widgets
    const widgetId = baseId + index;

    return {
        id: widgetId,
        title: chartData.title || config.title,
        type: config.widgetType,
        data: {
            // Use the data from chartData.data directly to avoid duplication
            ...chartData.data,
            ...(config.hasFilters && { filters: { branchFilter: true } }),
            description: config.description
        },
        category: config.category,
        chartType: config.chartType
    };
};

/**
 * Get user-specific dashboard widgets
 * Returns widgets relevant to regular users
 */
export const getUserDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: FilterOptions
): Promise<DashboardWidget[]> => {
    // Validate input parameters
    validateParameters(organizationId, userId);

    try {
        // Static count widgets (no database calls needed)
        const countWidgets: DashboardWidget[] = [
            {
                id: 1,
                title: "My Tasks",
                type: "task_summary",
                data: {
                    pending: 5,
                    completed: 12,
                    overdue: 2
                },
                order: 1,
                category: "count"
            },
            {
                id: 2,
                title: "My Leave Balance",
                type: "leave_balance",
                data: {
                    available: 15,
                    used: 10,
                    pending: 2
                },
                order: 2,
                category: "count"
            },
            {
                id: 3,
                title: "My Recent Activities",
                type: "activity_feed",
                data: {
                    activities: [
                        { action: "DSR Submitted", date: DateUtils.formatForDisplay(moment().subtract(1, 'day')), status: "completed" },
                        { action: "Leave Request", date: DateUtils.formatForDisplay(moment().subtract(2, 'days')), status: "pending" }
                    ]
                },
                order: 3,
                category: "count"
            },
            {
                id: 4,
                title: "My Performance",
                type: "performance_chart",
                data: {
                    currentMonth: 85,
                    lastMonth: 78,
                    trend: "up"
                },
                order: 4,
                category: "count"
            }
        ];

        // Early return if only count widgets are requested
        if (filters?.widgetType === "counts") {
            return countWidgets;
        }

        // Get filtered chart configurations based on user filters
        const chartConfigs = getFilteredChartConfigs(filters);

        // Create data fetching promises dynamically based on configurations
        const chartDataPromises = chartConfigs.map(config => ({
            type: config.type,
            promise: config.dataFetcher(organizationId, filters)
        }));

        // Use Promise.allSettled to handle partial failures gracefully
        const chartResults = await Promise.allSettled(
            chartDataPromises.map(item => item.promise)
        );

        // Create chart widgets dynamically using the mapping system
        const chartWidgets: DashboardWidget[] = [];

        chartResults.forEach((result, index) => {
            const config = chartConfigs[index];
            const chartType = chartDataPromises[index].type;

            if (result.status === 'fulfilled' && result.value.success) {
                // Create widget using the configuration and data
                const widget = createChartWidget(config, result.value, index);
                chartWidgets.push(widget);
            } else {
                // Log failed chart data fetches for monitoring
                const errorMessage = result.status === 'rejected'
                    ? result.reason
                    : 'Chart data fetch returned unsuccessful result';
                console.error(`Chart data fetch failed for ${chartType}:`, errorMessage);
            }
        });

        // Combine widgets based on filter requirements
        let allWidgets: DashboardWidget[] = [];

        if (filters?.widgetType === "charts") {
            allWidgets = chartWidgets;
        } else {
            // Default: return both count and chart widgets
            allWidgets = [...countWidgets, ...chartWidgets];
        }

        // Apply chart type filtering if specified
        if (filters?.widgetType === "charts" && filters?.chartType) {
            allWidgets = allWidgets.filter(widget => widget.chartType === filters.chartType);
        }

        return allWidgets;
    } catch (error) {
        console.error('Error in getUserDashboardWidgets:', error);
        // Return at least the count widgets on error to provide partial functionality
        if (filters?.widgetType !== "charts") {
            return [
                {
                    title: "My Tasks",
                    type: "task_summary",
                    data: { pending: 0, completed: 0, overdue: 0 },
                    category: "count"
                }
            ];
        }
        throw error;
    }
};

/**
 * Get admin-specific dashboard widgets
 * Returns widgets relevant to administrators
 */
/**
 * Get DSR, WSR, and Expense data based on time period filter
 * Supports: this_month (weeks), this_week (days), this_year (months), etc.
 */
const getDsrDataByTimePeriod = async (
    organizationId: string,
    userId: number,
    timePeriod: string,
    branchId?: string,
    chartType?: string
): Promise<any> => {
    if (!organizationId) {
        throw new Error('organizationId is required');
    }
    try {
        const timePeriodConfig = getTimePeriodConfig(timePeriod);
        const { startDate, endDate, labels, labelKeyMap, weekDateRanges } = timePeriodConfig;
        console.log("startDate", startDate, endDate);
        // let whereClause = await generateDashboardWhereClause(weekDateRanges);
        let whereClause = `((x.start_date >= '${startDate}' AND x.start_date <= '${endDate}') AND (x.end_date >= '${startDate}' AND x.end_date <= '${endDate}'))`
        if (branchId) {
            whereClause += ` AND x.branch_id IN(${branchId.split(",")})`;
        }

        // Determine the label calculation based on groupBy from timePeriodConfig
        let labelSelectClause = '';
        if (timePeriodConfig.groupBy === 'day') {
            labelSelectClause = `DATE_FORMAT(x.start_date, '%W')`;
        } else if (timePeriodConfig.groupBy === 'week') {
            // Accurate week calculation: Week 1 = days 1-7, Week 2 = days 8-14, etc.
            labelSelectClause = `CONCAT('Week ',
                CASE
                    WHEN DAY(x.start_date) BETWEEN 1 AND 7 THEN 1
                    WHEN DAY(x.start_date) BETWEEN 8 AND 14 THEN 2
                    WHEN DAY(x.start_date) BETWEEN 15 AND 21 THEN 3
                    WHEN DAY(x.start_date) BETWEEN 22 AND 28 THEN 4
                    ELSE 5
                END)`;
        } else if (timePeriodConfig.groupBy === 'month') {
            labelSelectClause = `DATE_FORMAT(x.start_date, '%M')`;
        } else {
            labelSelectClause = `DATE_FORMAT(x.start_date, '%Y-%m-%d')`; // fallback
        }

        const getReportQuery = `
        SELECT
            ${labelSelectClause} AS label,
            x.type,
            SUM(x.amount) AS total_amount,
            b.branch_name,
            GROUP_CONCAT(DISTINCT DATE_FORMAT(x.start_date, '%Y-%m-%d') ORDER BY x.start_date) AS dates_included
        FROM (
          SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id, di.dsr_amount AS amount, 'dsr' AS type
          FROM nv_dsr_items AS di
          JOIN nv_dsr_details AS dd ON dd.id = di.dsr_detail_id
          WHERE di.dsr_item_status = 'active' AND dd.dsr_detail_status = 'active'

          UNION ALL

          SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id, wi.wsr_amount AS amount, 'wsr' AS type
          FROM nv_wsr_items AS wi
          JOIN nv_wsr_details AS wd ON wd.id = wi.wsr_detail_id
          WHERE wi.wsr_item_status = 'active' AND wd.wsr_detail_status = 'active'

          UNION ALL

          SELECT DATE_FORMAT(concat(ed.expense_year, '-', ed.expense_month, '-01'), '%Y-%m-%d') AS start_date, LAST_DAY(concat(ed.expense_year, '-', ed.expense_month, '-01')) AS end_date, ed.branch_id AS branch_id, ei.expense_amount AS amount, 'expense' AS type
          FROM nv_expense_items AS ei
          JOIN nv_expense_details AS ed ON ed.id = ei.expense_detail_id
          WHERE ei.expense_item_status = 'active' AND ed.expense_detail_status = 'active'
        ) x
        JOIN nv_branches AS b ON b.id = x.branch_id AND b.organization_id = '${organizationId}'
        WHERE ${whereClause}
        GROUP BY ${labelSelectClause}, x.type, b.branch_name
        ORDER BY ${labelSelectClause}, x.type`;
        console.log('getReportQuery', getReportQuery);
        const reportData: any = await sequelize.query(getReportQuery, {
            type: QueryTypes.SELECT,
        });

        // Initialize maps
        const dsrMap = new Map<string, number>();
        const wsrMap = new Map<string, number>();
        const expenseMap = new Map<string, number>();

        labels.forEach(label => {
            dsrMap.set(label, 0);
            wsrMap.set(label, 0);
            expenseMap.set(label, 0);
        });

        // Process the aggregated data from SQL
        console.log('Raw reportData (aggregated):', reportData);
        console.log('Number of aggregated records:', reportData.length);

        reportData.forEach((record: any) => {
            console.log("Processing aggregated record:", record);
            let key = record.label;
            key = labelKeyMap[key] || key;

            const amount = parseFloat(record.total_amount) || 0;

            console.log(`Processing: Key=${key}, Type=${record.type}, Amount=${amount}, Dates=${record.dates_included}`);

            if (labels.includes(key)) {
                switch (record.type) {
                    case 'dsr':
                        dsrMap.set(key, (dsrMap.get(key) || 0) + amount);
                        console.log(`Updated DSR for ${key}: ${dsrMap.get(key)}`);
                        break;
                    case 'wsr':
                        wsrMap.set(key, (wsrMap.get(key) || 0) + amount);
                        console.log(`Updated WSR for ${key}: ${wsrMap.get(key)}`);
                        break;
                    case 'expense':
                        expenseMap.set(key, (expenseMap.get(key) || 0) + amount);
                        console.log(`Updated Expense for ${key}: ${expenseMap.get(key)}`);
                        break;
                }
            } else {
                console.log(`Key ${key} not found in labels: ${labels.join(', ')}`);
            }
        });

        // Debug: Show final maps
        console.log('Final DSR Map:', Array.from(dsrMap.entries()));
        console.log('Final WSR Map:', Array.from(wsrMap.entries()));
        console.log('Final Expense Map:', Array.from(expenseMap.entries()));

        // Prepare data based on chart type
        let data = [];
        let series = [];

        if (chartType === 'line') {
            // For single line chart, combine all values into a single series
            data = labels.map(label => ({
                col1: label,
                col2: (dsrMap.get(label) || 0) + (wsrMap.get(label) || 0) + (expenseMap.get(label) || 0)
            }));

            series = [
                { type: "line", xKey: "col1", yKey: "col2", yName: "Total of All income", fills: [] }
            ];
        } else {
            // For multi_line chart, keep separate series for DSR, WSR, and Expense
            data = labels.map(label => ({
                col1: label,
                col2: dsrMap.get(label) || 0,
                col3: wsrMap.get(label) || 0,
                col4: expenseMap.get(label) || 0
            }));

            series = [
                { type: "line", xKey: "col1", yKey: "col2", yName: "DSR", fills: [] },
                { type: "line", xKey: "col1", yKey: "col3", yName: "WSR", fills: [] },
                { type: "line", xKey: "col1", yKey: "col4", yName: "Expense", fills: [] }
            ];
        }

        return {
            success: true,
            dashboard_data: {
                series: series,
                data: data
            },
            metadata: {
                timePeriod: timePeriod,
                dateRange: `${startDate} to ${endDate}`,
                totalRecords: reportData.length,
                lastUpdated: DateUtils.now()
            }
        };

    } catch (error) {
        console.error('Error in getDsrDataByTimePeriod:', error);

        // Prepare empty response based on chart type
        let series = [];

        if (chartType === 'line') {
            series = [
                { type: "line", xKey: "col1", yKey: "col2", yName: "Total of All income", fills: [] }
            ];
        } else {
            series = [
                { type: "line", xKey: "col1", yKey: "col2", yName: "DSR", fills: [] },
                { type: "line", xKey: "col1", yKey: "col3", yName: "WSR", fills: [] },
                { type: "line", xKey: "col1", yKey: "col4", yName: "Expense", fills: [] }
            ];
        }

        return {
            success: false,
            dashboard_data: {
                series: series,
                data: []
            },
            metadata: {
                timePeriod: timePeriod,
                totalRecords: 0,
                lastUpdated: DateUtils.now()
            }
        };
    }
};


export const getDsrDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    validateParameters(organizationId, userId);

    try {
        // Extract time period filter (default to 'this_month')
        const timePeriod = filters?.filter_time_period;
        const branchId = filters?.branchId;
        const chartType = filters?.chartType || 'multi_line'; // Default to multi_line

        // Get DSR data based on time period
        const dsrResult = await getDsrDataByTimePeriod(organizationId, userId, timePeriod, branchId, chartType);

        const countWidgets: DashboardWidget[] = [
            {
                id: 1,
                title: chartType === 'line' ? "Income Summary" : "DSR/WSR/Expense Summary",
                type: "dsr_summary",
                data: dsrResult.dashboard_data,
                order: 1,
                category: "chart",
                chartType: chartType
            }
        ];
        return countWidgets;
    } catch (error) {
        console.error('Error in getDsrDashboardWidgets:', error);
        // Return empty DSR widget on error
        return [
            {
                id: 1,
                title: "DSR/WSR/Expense Summary",
                type: "dsr_summary",
                data: {
                    series: [
                        {
                            type: "line",
                            xKey: "col1",
                            yKey: "col2",
                            yName: "DSR",
                            fills: []
                        },
                        {
                            type: "line",
                            xKey: "col1",
                            yKey: "col3",
                            yName: "WSR",
                            fills: []
                        },
                        {
                            type: "line",
                            xKey: "col1",
                            yKey: "col4",
                            yName: "Expense",
                            fills: []
                        }
                    ],
                    data: []
                },
                order: 1,
                category: "chart",
                chartType: "line"
            }
        ];
    }

};

/**
 * Get setup-specific dashboard widgets
 * Returns widgets relevant to system setup
 */
export const getSetupDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    // Suppress unused parameter warnings - these parameters are kept for API consistency
    void organizationId;
    void userId;
    void filters;
    try {
        const widgets: DashboardWidget[] = [];

        // Setup-specific widgets
        widgets.push({
            id: 1,
            title: "System Configuration",
            type: "system_config",
            data: {
                completedSteps: 8,
                totalSteps: 12,
                progressPercentage: 67
            },
            order: 1,
            category: "count"
        });

        widgets.push({
            id: 2,
            title: "User Setup",
            type: "user_setup",
            data: {
                totalUsers: 25,
                activeUsers: 20,
                pendingInvitations: 5
            },
            order: 2,
            category: "count"
        });

        widgets.push({
            id: 3,
            title: "Module Configuration",
            type: "module_config",
            data: {
                enabledModules: 8,
                totalModules: 12,
                pendingConfiguration: 4
            },
            order: 3,
            category: "count"
        });

        widgets.push({
            id: 4,
            title: "Integration Status",
            type: "integration_status",
            data: {
                connectedServices: 3,
                totalServices: 6,
                failedConnections: 1
            },
            order: 4,
            category: "count"
        });

        return widgets;
    } catch (error) {
        console.error('Error in getSetupDashboardWidgets:', error);
        throw error;
    }
};

export default {
    getUserDashboardWidgets,
    getDsrDashboardWidgets,
    getSetupDashboardWidgets
};