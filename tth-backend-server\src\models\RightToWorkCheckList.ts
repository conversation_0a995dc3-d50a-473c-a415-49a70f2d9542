"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface rigthToWorkCheckListAttributes {
  id: number;
  checklist_id: number;
  user_id: number;
  status: string;
  passport_front: string;
  passport_back: string;
  cv: string;
  share_code: string;
  brp_front: string;
  brp_back: string;
  p45: string;
  ni_letter: string;
  student_letter: string;
  statements_dl_utility: string;
  photoID: string;
  is_confirm_upload: boolean;
  has_right_to_work_in_uk: boolean;
  is_uk_citizen: boolean;
  created_by: number;
  updated_by: number;
}

export class RightToWorkCheckList
  extends Model<rigthToWorkCheckListAttributes, never>
  implements rigthToWorkCheckListAttributes {
  id!: number;
  checklist_id!: number;
  user_id!: number;
  status!: string;
  passport_front!: string;
  passport_back!: string;
  cv!: string;
  share_code!: string;
  brp_front!: string;
  brp_back!: string;
  p45!: string;
  ni_letter!: string;
  student_letter!: string;
  statements_dl_utility!: string;
  photoID!: string;
  has_right_to_work_in_uk!: boolean;
  is_uk_citizen!: boolean;
  is_confirm_upload!: boolean;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}


export enum check_type {
  Initial = "initial",
  Follow = "follow",
}

export enum status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

RightToWorkCheckList.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    checklist_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM,
      values: Object.values(status),
      defaultValue: status.ACTIVE,
    },
    passport_front: {
      type: DataTypes.STRING,
    },
    passport_back: {
      type: DataTypes.STRING,
    },
    cv: {
      type: DataTypes.STRING,
    },
    share_code: {
      type: DataTypes.STRING,
    },
    brp_front: {
      type: DataTypes.STRING,
    },
    brp_back: {
      type: DataTypes.STRING,
    },
    p45: {
      type: DataTypes.STRING,
    },
    ni_letter: {
      type: DataTypes.STRING,
    },
    student_letter: {
      type: DataTypes.STRING,
    },
    statements_dl_utility: {
      type: DataTypes.STRING,
    },
    photoID: {
      type: DataTypes.STRING,
    },
    is_confirm_upload: {
      type: DataTypes.BOOLEAN,
    },
    has_right_to_work_in_uk: {
      type: DataTypes.BOOLEAN,
    },
    is_uk_citizen: {
      type: DataTypes.BOOLEAN,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_work_checklist_form",
    modelName: "RightToWorkCheckList",
  },
);

RightToWorkCheckList.addHook(
  "afterUpdate",
  async (rightToWorkCheckList: any) => {
    await addActivity(
      "RightToWorkCheckList",
      "updated",
      rightToWorkCheckList,
    );
  },
);

RightToWorkCheckList.addHook(
  "afterCreate",
  async (rightToWorkCheckList: RightToWorkCheckList) => {
    await addActivity(
      "RightToWorkCheckList",
      "created",
      rightToWorkCheckList,
    );
  },
);

RightToWorkCheckList.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

