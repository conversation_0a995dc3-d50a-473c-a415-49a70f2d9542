export const EMAILCONSTANT = Object.freeze({
  FORGOT_PASSWORD_OTP: {
    subject: "Forgot password",
  },
  FORGOT_PIN_OTP: {
    subject: "Forgot pin.",
  },
  RESEND_OTP: { template: "resend_otp", subject: "New OTP" }
});
export const FORMCONSTANT = Object.freeze({
  EMPLOYE_CONTRACT: { template: "employment_contract" },
});
export const DSRCONSTANT = Object.freeze({
  DSR_PDF: { template: "dsr_report" },
  USER_LEAVE_REPORT_PDF: { template: "user_leave_report" },
  USER_LEAVE_CONSUMPTION_REPORT_PDF: { template: "user_leave_consumption_report" },
  LEAVE_TYPE_STATICTICS: { template: "leave_type_statictics" },
  FOOTER_TEMPLATE: { template: "footer" }
});
export const NOTIFICATIONCONSTANT = Object.freeze({
  LEAVE_APPLY: {
    heading: "Leave request",
    content: (name: string) => `${name} has applied for leave`,
  },
  LEAVE_RESPONSE: {
    heading: "Action taken on your leave request",
    content: (name: string, status: string) =>
      `${name} has ${status} your leave request.`,
  },
  ONBOARDING_INCOMPLETE: {
    heading: "On boarding process",
    content: "Please complete your on-boarding.",
  },
  PROFILE_INCOMPLETE: {
    heading: "Profile incomplete",
    content: "Please complete your profile.",
  },
  MEDIA_ADDED: { heading: "New Media Added in Training Module" },
  CATEGORY_ADDED: { heading: "New Update Available for Document Center" },
  RESIGNATION_APPLY: {
    heading: "Resignation request",
    content: (name: string) => `${name} has applied for Resignation`,
  },
  RESIGNATION_RESPONSE: {
    heading: "Action taken on your resignation request",
    content: (name: string, status: string) =>
      `${name} has ${status} your resignation request.`,
  },
  DSR_UPDATE_REQUEST: {
    heading: "DSR update Request",
    content: (name: any, branch: any, dsr_date: any) =>
      `${name} from ${branch} branch has sent a DSR update request for ${dsr_date}.`,
  },
  DSR_RESPONSE: {
    heading: "Action taken on your DSR update request",
    content: (name: any, status: string, dsr_date: any) =>
      `Your DSR update request for ${dsr_date} has been ${status} by ${name}.`,
  },
  DSR_PENDING: {
    heading: "Reminder to Submit Daily Status Report",
    content: (branch_name: string, Date: any) =>
      `Please submit the DSR for ${branch_name} branch for the date(${Date}).`,
  },
  CHANGE_REQUEST: {
    heading: "Change request",
    content: (name: any) =>
      `${name} has submitted change request.Please review it. Thanks.`,
  },
  CHANGE_REQUEST_REJECTED: {
    heading: "Action taken on your Change request",
    content: (name: any) =>
      `${name} has Rejected your Change request.Please review it.`,
  },
  CHANGE_REQUEST_DELETED: {
    heading: "Action taken on your Change request",
    content: (name: any) => `${name} has Deleted your Change request.`,
  },
  ONBOARDING_FORM_REQUEST: {
    heading: "Action taken on your On-boarding form",
    content: (name: any, status: any) =>
      `${name} has ${status} your On-boarding form.`,
  },
  ONBOARDING_FORM_REMIND: {
    heading: "On boarding reminder",
    content: `Please complete your on-boarding.`,
  },
  ONBOARDING_FORM_EXPIRING_REMIND: {
    heading: (name: string) => `(${name}), Employeement contract expiring`,
    content: (day: any) =>
      `Your employeement contract is about to expire in ${day}.`,
  },
  PROBATION_OVER_REMIND: {
    heading: (name: string) => `(${name}), Probation period over`,
    content: (day: any) => `Your Probation period going to over on ${day}.`,
  },
  USER_CATEGORY_TRACK: {
    heading: "Item Viewed by User",
    content: (name: any, itemName: any) =>
      `${name} has viewed the item: ${itemName}.`,
  },
  USER_CATEGORY_RESTORE: {
    heading: "Category Reassignment Alert",
    content: (userName: string) =>
      `The category has been reassigned by ${userName}. Please review the updates accordingly.`,
  },
  EXPENSE_UPDATE_REQUEST: {
    heading: "Expense update Request",
    content: (name: any, branch: any, expense_month: any, expense_year: any) =>
      `${name} from ${branch} branch has sent a Expense update request for ${expense_month} - ${expense_year}.`,
  },
  EXPENSE_RESPONSE: {
    heading: "Action taken on your Expense update request",
    content: (
      name: any,
      status: string,
      expense_month: any,
      expense_year: any,
    ) =>
      `Your Expense update request for ${expense_month} - ${expense_year} has been ${status} by ${name}.`,
  },
  WSR_UPDATE_REQUEST: {
    heading: "WSR update Request",
    content: (name: any, branch: any, wsr_start_date: any, wsr_end_date: any) =>
      `${name} from ${branch} branch has sent a WSR update request for Week(${wsr_start_date} To ${wsr_end_date}).`,
  },
  WSR_RESPONSE: {
    heading: "Action taken on your WSR update request",
    content: (
      name: any,
      status: string,
      week_count: any,
      wsr_start_date: any,
      wsr_end_date: any,
    ) =>
      `Your WSR update request for Week ${week_count}(${wsr_start_date} To ${wsr_end_date}) has been ${status} by ${name}.`,
  },
  WSR_PENDING: {
    heading: "Reminder to Submit Weekly Status Report",
    content: (
      branch_name: string,
      week_count: any,
      wsr_start_date: any,
      wsr_end_date: any,
    ) =>
      `Please submit the WSR for ${branch_name} branch for Last Week (${wsr_start_date} To ${wsr_end_date}).`,
  },
  EXPENSE_PENDING: {
    heading: "Reminder to Submit Monthly Expense Report",
    content: (branch_name: string, expense_month: any, expense_year: any) =>
      `Please submit the Expense report for ${branch_name} branch for Last Month - ${expense_month}(${expense_year}).`,
  },
  FORECAST_DETAIL_UPDATED: {
    heading: "Forecast Detail Updated",
    content: () => "Your branch forecast has been updated. Please check it.",
  },
  FORECAST_LOCKED: {
    heading: "Forecast Locked",
    content: (forecast_lock: any) =>
      `Your branch forecast has been ${forecast_lock ? "locked" : "Unlocked"}locked by the admin. Please check it.`,
  },
  FORECAST_DETAIL_UPDATED_BM: {
    heading: "Forecast Detail Updated",
    content: (userName: string, branchName: any, budgetYear: any) =>
      `${userName} has submitted ${branchName} budget for ${budgetYear} for approval.`,
  },
  FORECAST_ASSIGNED_BM: {
    heading: "Forecast Assigned",
    content: (userName: string, budgetYear: any) =>
      `${userName} has assigned you the Budget of the year ${budgetYear}.`,
  },
  FORECAST_APPROVED: {
    heading: "Forecast approved and locked",
    content: (branchName: string, userName: string, budgetYear: any) =>
      `The Budget of the ${branchName} and ${budgetYear} is approved by ${userName}`,
  },
  FORECAST_ASSIGNED_ADMIN_NOTIFICATION: {
    heading: "Forecast Assigned",
    content: (userName: string, budgetYear: any, bmUser: any) =>
      `${userName} has assigned to the Budget of the year ${budgetYear} to ${bmUser}.`,
  },
  FORECAST_REMINDER_NOTIFICATION: {
    heading: "Forecast Reminder",
    content: (userName: string, budgetYear: any) =>
      `Hello ${userName}, kindly complete your Budget for ${budgetYear}.`,
  },
  SIDE_LETTER_NOTIFICATION: {
    heading: "Side Letter Shared With You",
    content: (senderName: string, sideLetterName: string) =>
      `${senderName} has sent you the side letter ${sideLetterName}.`,
  },
});

export const ROLE_CONSTANT = Object.freeze({
  SUPER_ADMIN: "Super Admin",
  ADMIN: "Admin",
  DIRECTOR: "Director",
  HR: "HR",
  AREA_MANAGER: "Area Manager",
  ACCOUNTANT: "Accountant",
  BRANCH_MANAGER: "Branch Manager",
  ASSIGN_BRANCH_MANAGER: "Assist. Branch Manager",
  HEAD_CHEF: "Head Chef",
  BAR_MANAGER: "Bar Manager",
  FOH: "FOH",
  BAR: "Bar",
  KITCHEN: "Kitchen",
  HOTEL_MANAGER: "Hotel Manager",
  ASSIGN_HOTEL_MANAGER: "Assist. Hotel Manager",
  RECEPTIONIST: "Receptionist",
  HEAD_HOUSEKEEPER: "Head Housekeeper",
  HOUSE_KEEPER: "House Keeper",
  SIGNATURE: "Signature",
});

export const ADMIN_SIDE_USER = [
  ROLE_CONSTANT.SUPER_ADMIN,
  ROLE_CONSTANT.ADMIN,
  ROLE_CONSTANT.DIRECTOR,
  ROLE_CONSTANT.ACCOUNTANT,
  ROLE_CONSTANT.HR,
  ROLE_CONSTANT.AREA_MANAGER,
  ROLE_CONSTANT.BRANCH_MANAGER,
  ROLE_CONSTANT.HOTEL_MANAGER,
  ROLE_CONSTANT.SIGNATURE,
];

export const NORMAL_USER = [
  ROLE_CONSTANT.SUPER_ADMIN,
  ROLE_CONSTANT.ADMIN,
  ROLE_CONSTANT.BRANCH_MANAGER,
  ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
  ROLE_CONSTANT.HEAD_CHEF,
  ROLE_CONSTANT.BAR_MANAGER,
  ROLE_CONSTANT.FOH,
  ROLE_CONSTANT.BAR,
  ROLE_CONSTANT.KITCHEN,
  ROLE_CONSTANT.HOTEL_MANAGER,
  ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER,
  ROLE_CONSTANT.RECEPTIONIST,
  ROLE_CONSTANT.HEAD_HOUSEKEEPER,
  ROLE_CONSTANT.HOUSE_KEEPER,
];

export const ROLE_PERMISSIONS = {
  NONE: 0,
  VIEW: 1,
  CREATE: 2,
  EDIT: 4,
  DELETE: 8,
}

export const OLD_ROLE_PERMISSION: any = {
  0: 0,
  1: 1,
  2: 6,
  3: 15,
}

export const CHECKLISTTYPE = {
  ADMIN: 1,
  USER: 2,
};

export const URL_CONSTANT = Object.freeze({
  AVATAR_URL: (fileName: string) => {
    return `${global.config.API_BASE_URL}user_avatars/${fileName}`;
  },
  SIGNATURE_URL: (signaturesName: string) => {
    return `${global.config.API_BASE_URL}signatures/${signaturesName}`;
  },
  USER_VERIFICATION_DOC_URL: (user_verification_doc: string) => {
    return `${global.config.API_BASE_URL}user_verification/${user_verification_doc}`;
  },
});

export const RABBITMQ_QUEUE = Object.freeze({
  STAFF_CREATION_DETAILS: 'staff_creation_details',
  STAFF_CREATION_SUCCESS: 'staff_creation_success',
  ORG_MASTER_USER: 'org_master_user',
  ORG_MASTER_USER_VERIFICATION_SUCCESS: 'org_master_user_verification_success',
  STAFF_CREATION: 'staff_creation',
  STAFF_PASSWORD_PIN_GENERATE: 'staff_password_pin_generate',
  SESSION_STORE: 'session_store',
  USER_ACTIVITY_LOG: 'user_activity_log',
  BANNER_NOTIFICATION: 'banner_notification',
  EMAIL_NOTIFICATION: 'email_notification',
  PUSH_NOTIFICATION_SUCCESS: 'push_notification_success'
})

export const LEAVE_POLICY = {
  HOURS: 8,
  WEEKEND_DAYS: 2,
};

export const EMAIL_ADDRESS = Object.freeze({
  ADDRESS: "Suite-1 Francis House, Queens Road, Norwich, England, NR1 3PN",
  PHONE_NUMBER: "+44 7485129630",
  EMAIL: "<EMAIL>",
  ORGANIZATION_NAME: "MicrOffice",
  CONFIDENTIALITY_STATEMENT: "The information in this report is confidential and intended for internal use only.Please do not share, reproduce, or distribute without appropriate authorization."
});

export const NOTIFICATION_TYPE = Object.freeze({
  INDIVIDUAL: 'individual',
  BRANCH: "branch",
  DEPARTMENT: "department",
  ROLE: "role",
  BRANCH_DEPARTMENT: "branch_department",
  ALL: "all",
  BRANCH_ROLE: "branch_role",
  ADMIN: "admin",
  SHIFT_ADD: "shifts_add",
  SHIFT_DROP: "shifts_drop",
  SHIFT_COVER: "shifts_cover"
})

export const REDIRECTION_TYPE = Object.freeze({
  LEAVE: "leave",
  ONBOARDING: "onboarding",
  MEDIA: "media",
  RESIGNATION: "resignation",
  PROFILE: "profile",
  DSR: 'dsr',
  DSR_REQUEST: 'dsr_request',
  CHANGE_REQUEST: 'change_request',
  CATEGORY: "category",
  EXPENSE: 'expense',
  EXPENSE_REQUEST: 'expense_request',
  WSR: 'wsr',
  WSR_REQUEST: 'wsr_request',
  FORECAST: 'forecast',
  SHIFTS: 'shift_details',
  SIDE_LATTER: 'side_letter'
})

export const FILE_UPLOAD_CONSTANT = Object.freeze({
  USER_PROFILE_API: {
    folder: "user_avatars",
    destinationPath: (orgName: string, userId: any, fileName: string) =>
      `${orgName}/users/${userId}/user_avatar/${fileName}`,
  },
  USER_SIGNATURE_PATH: {
    folder: "signatures",
    destinationPath: (orgName: string, userId: any, fileName: string) =>
      `${orgName}/users/${userId}/signatures/${fileName}`,
  },
  SIGNATURES_PATH: {
    folder: "signatures",
    destinationPath: (orgName: string, filename: string) =>
      `${orgName}/signatures/${filename}`
  },
  USER_VERIFICATION_DOC_PATH: {
    folder: "user_verification",
    destinationPath: (orgName: string, userId: any, fileName: string) =>
      `${orgName}/users/${userId}/user_verification/${fileName}`,
  },
  ORG_SETTINGS: {
    folder: "settings",
    destinationPath: (orgName: string, fileName: string) =>
      `${orgName}/settings/${fileName}`,
  },
  MEDIA_FILES: {
    folder: "media",
    destinationPath: (orgName: string, fileName: string) =>
      `${orgName}/media/${fileName}`,
  },
  NOTIFICATION_FILES: {
    folder: "notification",
    destinationPath: (orgName: string, fileName: string) =>
      `${orgName}/notification/${fileName}`,
  },
  DOCUMENT_CATEGORY_FILES: {
    folder: "document_category",
    destinationPath: (orgName: string, fileName: string) =>
      `${orgName}/document_category/${fileName}`,
  },
  CHANGE_REQUEST_FILES: {
    folder: "change_request",
    destinationPath: (orgName: string, userId: string, fileName: string) =>
      `${orgName}/users/${userId}/change_request/${fileName}`,
  },
  ONBOARDING_FILES: {
    folder: "onboarding",
    destinationPath: (orgName: string, userId: string, fileName: string) =>
      `${orgName}/users/${userId}/onboarding/${fileName}`,
  },
  SIDE_LETTER_FILES: {
    folder: "side_letters",
    destinationPath: (orgName: string, fileName: string) =>
      `${orgName}/side_letters/${fileName}`,
  },
  USER_VERIFICATION: {
    folder: 'user_verification',
    destinationPath: (orgName: string, fileName: string, userId: string) =>
      `${orgName}/users/${userId}/user_verification/${fileName}`,
  }
});
