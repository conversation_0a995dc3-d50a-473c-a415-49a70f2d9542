import { NextFunction, Request, Response } from "express";
import { ADMIN_SIDE_USER, ROLE_CONSTANT } from "../helper/constant";
import { Role } from "../models/Role";

// Define a type for module names
type ModuleName = 'all' | 'branch' | 'notification' | 'staff' | 'bank' | 'department' | 'category' | 'media' | 'document' | 'user_verification';

// Role-based access control mapping
const roleAccessMap: Record<string, ModuleName[]> = {
    [ROLE_CONSTANT.SUPER_ADMIN]: ['all'],
    [ROLE_CONSTANT.ADMIN]: ['all'],
    [ROLE_CONSTANT.DIRECTOR]: ['all'],
    [ROLE_CONSTANT.HR]: ['all'],
    [ROLE_CONSTANT.AREA_MANAGER]: ['branch', 'notification'],
    [ROLE_CONSTANT.ACCOUNTANT]: ['staff', 'branch', 'bank'],
    [ROLE_CONSTANT.BRANCH_MANAGER]: ['branch', 'department', 'notification', 'staff', 'category', 'media', 'document'],
    [ROLE_CONSTANT.HOTEL_MANAGER]: ['branch', 'department', 'notification', 'staff', 'category', 'media', 'document'],
    [ROLE_CONSTANT.SIGNATURE]: ['staff', 'user_verification'],
    [ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER]: [],
    [ROLE_CONSTANT.HEAD_CHEF]: [],
    [ROLE_CONSTANT.BAR_MANAGER]: [],
    [ROLE_CONSTANT.FOH]: [],
    [ROLE_CONSTANT.BAR]: [],
    [ROLE_CONSTANT.KITCHEN]: [],
    [ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER]: [],
    [ROLE_CONSTANT.RECEPTIONIST]: [],
    [ROLE_CONSTANT.HEAD_HOUSEKEEPER]: [],
    [ROLE_CONSTANT.HOUSE_KEEPER]: []
};

// Path patterns mapped to modules based on FILE_UPLOAD_CONSTANT destination paths
const pathModulePatterns = [
    { pattern: '/user_avatars/', module: 'staff' },
    { pattern: '/user_avatar/', module: 'staff' },
    { pattern: '/signatures/', module: 'staff' },
    { pattern: '/user_verification/', module: 'user_verification' },
    { pattern: '/settings/', module: 'branch' },
    { pattern: '/media/', module: 'media' },
    { pattern: '/notification/', module: 'notification' },
    { pattern: '/document_category/', module: 'category' },
    { pattern: '/change_request/', module: 'document' },
    { pattern: '/onboarding/', module: 'staff' },
    { pattern: '/side_letters/', module: 'document' },
    { pattern: '/branch/', module: 'branch' },
    { pattern: '/department/', module: 'department' },
    { pattern: '/bank/', module: 'bank' }
] as const;

// User-specific folder paths - these contain user ID in the path
const userSpecificPaths = [
    '/users/', // This covers all user-specific paths
];

const secureDocs = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { location } = req.query;
        if (!location) {
            return next();
        }

        const locationPath = location as string;
        const user = req.user;

        if (!user) {
            return res.status(401).send({ status: false, message: "User not authenticated" });
        }

        // If not a web platform request, allow access
        if (!req.headers || req.headers['platform-type'] !== 'web') {
            return next();
        }

        // Check if the user is accessing their own files
        // User-specific paths contain the user's ID in the format /users/{userId}/...
        if (userSpecificPaths.some(path => locationPath.includes(path))) {
            // Extract user ID from the path - this assumes path format like .../users/123/...
            const pathSegments = locationPath.split('/');
            const userIdIndex = pathSegments.findIndex(segment => segment === 'users') + 1;

            if (userIdIndex > 0 && userIdIndex < pathSegments.length) {
                const pathUserId = pathSegments[userIdIndex];
                // If the user is accessing their own files, allow access
                if (user.id.toString() === pathUserId) {
                    return next();
                }
            }
        }

        // Find user role for role-based access control
        const findUserRole = await Role.findOne({ where: { id: user?.web_user_active_role_id } });
        if (!findUserRole) {
            return res.status(403).send({ status: false, message: "Role not found" });
        }

        const roleName = findUserRole.role_name;

        // Check if admin side user - they have broad access
        if (ADMIN_SIDE_USER.some(role => role === roleName)) {
            // Super Admin, Admin, Director, HR have access to everything
            const highAccessRoles = [
                ROLE_CONSTANT.SUPER_ADMIN,
                ROLE_CONSTANT.ADMIN,
                ROLE_CONSTANT.DIRECTOR,
                ROLE_CONSTANT.HR
            ];

            if (highAccessRoles.some(role => role === roleName)) {
                return next();
            }

            // For other roles, check permission based on path
            const userPermissions = roleAccessMap[roleName] || [];

            // If role has 'all' permission, grant access
            if (userPermissions.includes('all')) {
                return next();
            }

            // Determine which module the requested path belongs to
            let moduleFound = false;
            let hasAccess = false;

            // Check if the location matches any of our path patterns
            for (const { pattern, module } of pathModulePatterns) {
                if (locationPath.includes(pattern)) {
                    moduleFound = true;
                    // Check if user has permission for this module
                    if (userPermissions.includes(module as ModuleName)) {
                        hasAccess = true;
                        break;
                    }
                }
            }

            if (!moduleFound || hasAccess) {
                return next();
            } else {
                return res.status(403).send({ status: false, message: "You don't have permission to access this resource" });
            }
        } else {
            // Non-admin users don't have access to protected documents
            return res.status(403).send({ status: false, message: "You don't have permission to access this resource" });
        }
    } catch (error: any) {
        console.log(error);
        return res.status(401).send({ status: false, message: error.message });
    }
};

export default secureDocs;