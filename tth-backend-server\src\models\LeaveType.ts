"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { LeaveAccuralPolicy } from "./LeaveAccuralPolicy";

interface LeaveTypeAttributes {
    id: number;
    name: string;
    status: string;
    remark: string;
    has_annual_leave: boolean;
    leave_deduction_type: string;
    user_probation_status: boolean;
    leave_type_color: string;
    leave_period_type: string;
    organization_id: string;
    created_by: number;
    updated_by: number;
}

export class LeaveTypeModel
    extends Model<LeaveTypeAttributes, never>
    implements LeaveTypeAttributes {
    public id!: number;
    public name!: string;
    public status!: string;
    public remark!: string;
    public has_annual_leave!: boolean;
    public created_by!: number;
    public updated_by!: number;
    public leave_deduction_type!: string;
    public user_probation_status!: boolean;
    public leave_type_color!: string;
    public leave_period_type!: string;
    public organization_id!: string;
    public readonly createdAt!: Date;
    public readonly updatedAt!: Date;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

export enum status {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DRAFT = "draft",
    DELETED = "deleted"
}

export enum leave_deduction_type {
    PAID = "paid",
    UNPAID = "unpaid"
}

export enum leave_period_type {
    HOUR = "hour",
    DAY = "day",
}


LeaveTypeModel.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        name: {
            type: DataTypes.STRING(100),
            allowNull: false,
        },
        remark: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        status: {
            type: DataTypes.ENUM,
            values: Object.values(status),
            defaultValue: status.ACTIVE,
        },
        leave_deduction_type: {
            type: DataTypes.ENUM,
            values: Object.values(leave_deduction_type),
            allowNull: true
        },
        user_probation_status: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
        },
        has_annual_leave: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
        },
        leave_type_color: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        leave_period_type: {
            type: DataTypes.ENUM,
            values: Object.values(leave_period_type),
            allowNull: true
        },
        organization_id: {
            type: DataTypes.STRING,
            allowNull: false
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_leave_type",
        modelName: "LeaveTypeModel",
    },
);

LeaveAccuralPolicy.belongsTo(LeaveTypeModel, { foreignKey: "leave_type_id", as: "leave_accural_leave_type_id" });
LeaveTypeModel.hasMany(LeaveAccuralPolicy, { foreignKey: "leave_type_id", as: "leave_accural_policy" });

// Define hooks for leave type model
LeaveTypeModel.addHook("afterUpdate", async (LeaveTypeModel: any) => {
    await addActivity("LeaveType", "updated", LeaveTypeModel);
});

LeaveTypeModel.addHook("afterCreate", async (LeaveTypeModel: LeaveTypeModel) => {
    await addActivity("LeaveType", "created", LeaveTypeModel);
});

LeaveTypeModel.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});


