import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { payment_type_usage, PaymentType, payment_type_status as paymentTypeStatus } from "../models/PaymentType";
import { addSpacesBeforeCapitals, createNotification, generateReport, getFirstAndLastDates, getUserFullName, isDateInRange, permittedForAdminEnhanced, validateModulePermission } from "../helper/common";
import { NOTIFICATION_TYPE, NOTIFICATIONCONSTANT, ROLE_CONSTANT, REDIRECTION_TYPE, ROLE_PERMISSIONS } from "../helper/constant";
import { ExpenseDetail, expense_detail_status } from "../models/ExpenseDetail";
import { Role } from "../models/Role";
import { Role as MORole } from "../models/MORole";
import { ExpenseItem, expense_item_status } from "../models/ExpenseItem";
import { Branch, branch_status } from "../models/Branch";
import { Op } from "sequelize";
import { getPaginatedItems, getPagination } from "../helper/utils";
import { User, user_status } from "../models/User";
import { sequelize } from "../models";
import { ExpenseRequest, expense_request_status, expense_request_status as request_status } from "../models/ExpenseRequest";
import { ExpenseItemRequest, expense_request_item_status } from "../models/ExpenseItemRequest";
import { Activity } from "../models/Activity";
import { PaymentTypeCategory, payment_type_category_status as paymentTypeCategoryStatus, payment_type_category_pattern as paymentTypeCategoryPattern } from "../models/PaymentTypeCategory";
import { PaymentTypeCategoryBranch, payment_type_category_branch_status as paymentTypeCategoryBranchStatus } from "../models/PaymentTypeCategoryBranch";
import { PaymentTypeRemark } from "../models/PaymentTypeRemark";

const addExpenseDetail = async (req: Request, res: Response) => {
    try {

        let findRole: any

        // Get user role using MORole system if available, otherwise fallback to old system
        if (req.user.user_role_id) {
            // Use MORole system
            findRole = await MORole.findOne({
                attributes: ['id', 'role_name'],
                where: {
                    id: req.user.user_role_id,
                    organization_id: req.user.organization_id,
                    role_status: 'active'
                },
                raw: true
            });
        } else {
            // Fallback to old role system
            if (req.headers["platform-type"] == "web") {
                findRole = await Role.findOne({
                    attributes: ['id', 'role_name'],
                    where: { id: req.user?.web_user_active_role_id ? req.user.web_user_active_role_id : req.user.user_active_role_id },
                    raw: true
                });
            } else if ((req.headers["platform-type"] == "ios" || req.headers["platform-type"] == "android") && req.user.user_active_role_id) {
                findRole = await Role.findOne({
                    attributes: ['id', 'role_name'],
                    where: { id: req.user?.user_active_role_id },
                    raw: true
                });
            }
        }

        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'expense', // Expense module slug
            ROLE_PERMISSIONS.CREATE,
            req?.headers?.["platform-type"]
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [
        //         ROLE_CONSTANT.BRANCH_MANAGER,
        //         ROLE_CONSTANT.HOTEL_MANAGER,
        //         ROLE_CONSTANT.SUPER_ADMIN,
        //         ROLE_CONSTANT.ADMIN,
        //         ROLE_CONSTANT.ACCOUNTANT,
        //         ROLE_CONSTANT.DIRECTOR
        //     ]
        // );

        // Also check old permission system for backward compatibility
        const oldPermissionCheck = findRole && [
            ROLE_CONSTANT.BRANCH_MANAGER,
            ROLE_CONSTANT.HOTEL_MANAGER,
            ROLE_CONSTANT.SUPER_ADMIN,
            ROLE_CONSTANT.ADMIN,
            ROLE_CONSTANT.ACCOUNTANT,
            ROLE_CONSTANT.DIRECTOR
        ].includes(findRole?.role_name);

        // User has permission if any check passes
        const hasPermission = checkModulePermission || /* checkAdminPermission || */ oldPermissionCheck;

        if (!hasPermission) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PERMISSION_DENIED"),
            });
        }

        const { expense_month, expense_year, branch_id, data = [], current_datetime } = req.body;

        const {
            firstDateOfCurrentMonth,
            lastDateOfTargetMonth
        } = getFirstAndLastDates(expense_month, expense_year, 1);

        const findBranch = await Branch.findOne({ attributes: ['id', 'branch_name'], where: { id: branch_id, branch_status: { [Op.not]: branch_status.DELETED }, organization_id: req.user.organization_id }, raw: true })
        if (!findBranch) {
            return res
                .status(StatusCodes.BAD_REQUEST)
                .json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
        }

        const getExpenseDetail = await ExpenseDetail.findOne({ attributes: ['id'], where: { branch_id: branch_id, expense_month, expense_year, expense_detail_status: expense_detail_status.ACTIVE }, raw: true })
        if (getExpenseDetail) {
            return res
                .status(StatusCodes.BAD_REQUEST)
                .json({ status: false, message: res.__("EXPENSE_ALREADY_ADDED") });
        }

        if (findRole?.role_name == ROLE_CONSTANT.BRANCH_MANAGER || findRole?.role_name == ROLE_CONSTANT.HOTEL_MANAGER || findRole?.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER || findRole?.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER) {
            // Check if collectionDate is yesterday
            if (!isDateInRange(current_datetime, firstDateOfCurrentMonth, lastDateOfTargetMonth)) {
                // Check if current time is before 8 AM today
                return res
                    .status(StatusCodes.EXPECTATION_FAILED)
                    .json({ status: false, message: res.__("FAIL_CANNOT_ADD_MONTH_EXPENSE") });
            }
        }

        const expenseDetail = await ExpenseDetail.setHeaders(req).create({ user_id: req.user.id, branch_id: branch_id, expense_month, expense_year, expense_detail_status: expense_detail_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id } as any)
        if (expenseDetail) {
            if (data && data.length > 0) {
                for (const item of data) {
                    const findExpenseRemark = await PaymentTypeRemark.findOne({ attributes: ['detail_id'], where: { detail_id: expenseDetail.id, payment_type_id: item.id }, raw: true });
                    if (findExpenseRemark) {
                        await PaymentTypeRemark.setHeaders(req).update({ payment_type_remark: item.payment_type_remark, updated_by: req.user.id }, { where: { detail_id: expenseDetail.id, payment_type_id: item.id } });
                    } else {
                        await PaymentTypeRemark.setHeaders(req).create({
                            detail_id: expenseDetail.id,
                            payment_type_id: item.id, // TODO: CHECK IF THIS IS CORRECTL ,
                            payment_type_remark: item.payment_type_remark,
                            created_by: req.user.id,
                            updated_by: req.user.id
                        } as any);
                    }
                    if (item?.payment_type_category.length > 0) {
                        for (const category of item.payment_type_category) {
                            if (category.payment_type_category_pattern == paymentTypeCategoryPattern.MULTIPLE) {
                                for (const option of category.categoryBranchValue) {
                                    if (option?.expense_amount) {
                                        const findDocumentCategoryBranch = await PaymentTypeCategoryBranch.findOne({
                                            attributes: ['id'],
                                            where: {
                                                id: option?.reference_id, branch_id: branch_id, payment_type_category_branch_status
                                                    : paymentTypeCategoryBranchStatus.ACTIVE
                                            }, raw: true
                                        })
                                        if (findDocumentCategoryBranch) {
                                            const findExpenseItem = await ExpenseItem.findOne({ attributes: ['id'], where: { expense_detail_id: expenseDetail.id, reference_id: option.reference_id, payment_type_category_id: category.payment_type_category_id }, raw: true })
                                            if (!findExpenseItem) {
                                                await ExpenseItem.setHeaders(req).create({ expense_detail_id: expenseDetail.id, payment_type_category_id: category.payment_type_category_id, expense_amount: option.expense_amount, reference_id: option.reference_id, expense_item_status: expense_item_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id } as any)
                                            }
                                        }
                                    }
                                }
                            } else {
                                if (category?.expense_amount) {
                                    const findExpenseItem = await ExpenseItem.findOne({ attributes: ['id'], where: { expense_detail_id: expenseDetail.id, payment_type_category_id: category.payment_type_category_id }, raw: true })
                                    if (!findExpenseItem) {
                                        await ExpenseItem.setHeaders(req).create({ expense_detail_id: expenseDetail.id, payment_type_category_id: category.payment_type_category_id, expense_amount: category.expense_amount, expense_item_status: expense_item_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id } as any)
                                    }
                                }
                            }
                        }
                    }

                }
            }
            return res
                .status(StatusCodes.OK)
                .json({ status: true, message: res.__("EXPENSE_ADDED_SUCCESSFULLY") });
        } else {
            return res
                .status(StatusCodes.EXPECTATION_FAILED)
                .json({ status: false, message: res.__("FAIL_TO_ADD_EXPENSE") });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const updateExpenseDetail = async (req: Request, res: Response) => {
    try {

        let findRole: any

        // Get user role using MORole system if available, otherwise fallback to old system
        if (req.user.user_role_id) {
            // Use MORole system
            findRole = await MORole.findOne({
                attributes: ['id', 'role_name'],
                where: {
                    id: req.user.user_role_id,
                    organization_id: req.user.organization_id,
                    role_status: 'active'
                },
                raw: true
            });
        } else {
            // Fallback to old role system
            if (req.headers["platform-type"] == "web") {
                findRole = await Role.findOne({
                    attributes: ['id', 'role_name'],
                    where: { id: req.user?.web_user_active_role_id ? req.user.web_user_active_role_id : req.user.user_active_role_id },
                    raw: true
                });
            } else if ((req.headers["platform-type"] == "ios" || req.headers["platform-type"] == "android") && req.user.user_active_role_id) {
                findRole = await Role.findOne({
                    attributes: ['id', 'role_name'],
                    where: { id: req.user?.user_active_role_id },
                    raw: true
                });
            }
        }
        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'expense', // Expense module slug
            ROLE_PERMISSIONS.EDIT,
            req?.headers?.["platform-type"]
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [
        //         ROLE_CONSTANT.BRANCH_MANAGER,
        //         ROLE_CONSTANT.HOTEL_MANAGER,
        //         ROLE_CONSTANT.SUPER_ADMIN,
        //         ROLE_CONSTANT.ADMIN,
        //         ROLE_CONSTANT.ACCOUNTANT,
        //         ROLE_CONSTANT.DIRECTOR
        //     ]
        // );

        // Also check old permission system for backward compatibility
        const oldPermissionCheck = findRole && [
            ROLE_CONSTANT.BRANCH_MANAGER,
            ROLE_CONSTANT.HOTEL_MANAGER,
            ROLE_CONSTANT.SUPER_ADMIN,
            ROLE_CONSTANT.ADMIN,
            ROLE_CONSTANT.ACCOUNTANT,
            ROLE_CONSTANT.DIRECTOR
        ].includes(findRole?.role_name);

        // User has permission if any check passes
        const hasPermission = checkModulePermission || /* checkAdminPermission || */ oldPermissionCheck;

        if (!hasPermission) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PERMISSION_DENIED"),
            });
        }
        const { data = [], current_datetime, expense_month, expense_year } = req.body;
        const { expense_detail_id } = req.params;
        const getExpenseDetail: any = await ExpenseDetail.findOne({ attributes: ['id', 'branch_id', 'expense_month', 'expense_year'], where: { id: expense_detail_id, expense_detail_status: expense_detail_status.ACTIVE }, raw: true })
        if (getExpenseDetail) {
            if (findRole?.role_name == ROLE_CONSTANT.SUPER_ADMIN || findRole?.role_name == ROLE_CONSTANT.ADMIN || findRole?.role_name == ROLE_CONSTANT.DIRECTOR || findRole?.role_name == ROLE_CONSTANT.ACCOUNTANT) {
                const findExpenseBasedDate = await ExpenseDetail.findOne({ attributes: ['id'], where: { expense_month: expense_month, expense_year: expense_year, expense_detail_status: expense_detail_status.ACTIVE, branch_id: getExpenseDetail.branch_id }, raw: true })
                if (findExpenseBasedDate) {
                    if (getExpenseDetail.id != findExpenseBasedDate?.id) {
                        return res.status(StatusCodes.BAD_REQUEST).json({
                            status: false,
                            message: res.__("EXPENSE_ALREADY_EXISTS"),
                        })
                    }
                }
            }
            const {
                firstDateOfCurrentMonth,
                lastDateOfTargetMonth
            } = getFirstAndLastDates(getExpenseDetail.expense_month, getExpenseDetail.expense_year, 1);
            const getFullName = await getUserFullName(req.user.id)
            // Check if collectionDate is yesterday and current time is past 8 AM today
            if (findRole?.role_name == ROLE_CONSTANT.BRANCH_MANAGER || findRole?.role_name == ROLE_CONSTANT.HOTEL_MANAGER || findRole?.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER || findRole?.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER) {
                if (!isDateInRange(current_datetime, firstDateOfCurrentMonth, lastDateOfTargetMonth)) {
                    // request code
                    const findRequest = await ExpenseRequest.findOne({ attributes: ['id'], where: { expense_detail_id: getExpenseDetail.id, expense_request_status: expense_request_status.PENDING }, raw: true })
                    if (findRequest) {
                        return res
                            .status(StatusCodes.EXPECTATION_FAILED)
                            .json({ status: false, message: res.__("EXPENSE_UPDATED_REQUEST_EXIST") });
                    }
                    const expenseRequest = await ExpenseRequest.setHeaders(req).create({ expense_detail_id: getExpenseDetail.id, user_id: req.user.id, expense_request_status: request_status.PENDING, created_by: req.user.id, updated_by: req.user.id } as any)
                    if (expenseRequest) {
                        if (data && data.length > 0) {
                            for (const item of data) {
                                const findExpenseRemark = await PaymentTypeRemark.findOne({ attributes: ['detail_id'], where: { detail_id: expense_detail_id, payment_type_id: item.id }, raw: true });
                                if (findExpenseRemark) {
                                    await PaymentTypeRemark.setHeaders(req).update({ payment_type_remark: item.payment_type_remark, updated_by: req.user.id }, { where: { detail_id: expense_detail_id, payment_type_id: item.id } });
                                } else {
                                    await PaymentTypeRemark.setHeaders(req).create({
                                        detail_id: expense_detail_id,
                                        payment_type_id: item.id, // TODO: CHECK IF THIS IS CORRECTL ,
                                        payment_type_remark: item.payment_type_remark,
                                        created_by: req.user.id,
                                        updated_by: req.user.id
                                    } as any);
                                }
                                if (item?.payment_type_category.length > 0) {
                                    for (const category of item.payment_type_category) {
                                        if (category.payment_type_category_pattern == paymentTypeCategoryPattern.MULTIPLE) {
                                            for (const option of category.categoryBranchValue) {
                                                if (option.expense_amount || option.old_expense_amount) {
                                                    const findDocumentCategoryBranch = await PaymentTypeCategoryBranch.findOne({
                                                        attributes: ['id'],
                                                        where: {
                                                            id: option?.reference_id, branch_id: getExpenseDetail.branch_id, payment_type_category_branch_status
                                                                : paymentTypeCategoryBranchStatus.ACTIVE
                                                        }, raw: true
                                                    })
                                                    if (findDocumentCategoryBranch) {
                                                        const findExpenseItem = await ExpenseItemRequest.findOne({ attributes: ['id'], where: { expense_request_detail_id: expenseRequest.id, reference_id: option.reference_id, payment_type_category_id: category.payment_type_category_id }, raw: true })
                                                        if (!findExpenseItem) {
                                                            option.expense_amount = option.expense_amount ? option.expense_amount : null;
                                                            await ExpenseItemRequest.setHeaders(req).create({ expense_request_detail_id: expenseRequest.id, payment_type_category_id: category.payment_type_category_id, expense_amount: option.expense_amount, reference_id: option.reference_id, expense_request_item_status: expense_request_item_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id, old_expense_amount: option.old_expense_amount } as any)
                                                        }
                                                    }
                                                }
                                            }
                                        } else {
                                            if (category.expense_amount || category.old_expense_amount) {
                                                const findExpenseItemRequest = await ExpenseItemRequest.findOne({ attributes: ['id'], where: { expense_request_detail_id: expenseRequest.id, payment_type_category_id: category.payment_type_category_id }, raw: true })
                                                if (!findExpenseItemRequest) {
                                                    category.expense_amount = category.expense_amount ? category.expense_amount : null;
                                                    await ExpenseItemRequest.setHeaders(req).create({ expense_request_detail_id: expenseRequest.id, payment_type_category_id: category.payment_type_category_id, expense_amount: category.expense_amount, expense_request_item_status: expense_request_item_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id, old_expense_amount: category.old_expense_amount } as any)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        // Find users with admin roles using both MORole and old role systems
                        const adminRoleNames = [ROLE_CONSTANT.SUPER_ADMIN, ROLE_CONSTANT.ADMIN, ROLE_CONSTANT.DIRECTOR, ROLE_CONSTANT.ACCOUNTANT];

                        const findUsers = (await User.findAll({
                            attributes: ['id', 'appToken', 'webAppToken'],
                            where: {
                                user_status: {
                                    [Op.not]: [
                                        user_status.DELETED,
                                        user_status.PENDING,
                                        user_status.CANCELLED,
                                    ],
                                },
                                [Op.or]: [
                                    // MORole system users
                                    {
                                        user_role_id: {
                                            [Op.in]: [
                                                sequelize.literal(
                                                    `(SELECT id FROM mo_roles WHERE organization_id = '${req.user.organization_id}' AND role_name IN ('${adminRoleNames.join("','")}') AND role_status = 'active')`
                                                ),
                                            ],
                                        },
                                    },
                                    // Old role system users (fallback)
                                    {
                                        user_role_id: { [Op.is]: null },
                                        id: {
                                            [Op.in]: [
                                                sequelize.literal(
                                                    `(SELECT nv_user_roles.user_id from nv_user_roles where nv_user_roles.role_id IN (SELECT id from nv_roles where role_name IN ('${adminRoleNames.join("','")}')))`
                                                ),
                                            ],
                                        },
                                    },
                                ],
                                organization_id: req.user.organization_id
                            },
                            raw: true,
                            group: ['id']
                        } as any)) || [];
                        const findBranch = await Branch.findOne({ attributes: ['id', 'branch_name'], where: { id: getExpenseDetail.branch_id, branch_status: { [Op.not]: branch_status.DELETED }, organization_id: req.user.organization_id }, raw: true })
                        await createNotification(findUsers, req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.EXPENSE_UPDATE_REQUEST.content(getFullName, findBranch?.branch_name, getExpenseDetail.expense_month, getExpenseDetail.expense_year), NOTIFICATIONCONSTANT.EXPENSE_UPDATE_REQUEST.heading, REDIRECTION_TYPE.EXPENSE, getExpenseDetail.id, { expense_id: getExpenseDetail.id })
                        return res
                            .status(StatusCodes.OK)
                            .json({ status: true, message: res.__("EXPENSE_REQUEST_ADDED") });
                    } else {
                        return res
                            .status(StatusCodes.EXPECTATION_FAILED)
                            .json({ status: false, message: res.__("FAIL_TO_ADD_EXPENSE_REQUEST") });
                    }

                }
            }
            if (data && data.length > 0) {
                const expenseItemIds: any = []
                for (const item of data) {
                    const findExpenseRemark = await PaymentTypeRemark.findOne({ attributes: ['detail_id'], where: { detail_id: expense_detail_id, payment_type_id: item.id }, raw: true });
                    if (findExpenseRemark) {
                        await PaymentTypeRemark.setHeaders(req).update({ payment_type_remark: item.payment_type_remark, updated_by: req.user.id }, { where: { detail_id: expense_detail_id, payment_type_id: item.id } });
                    } else {
                        await PaymentTypeRemark.setHeaders(req).create({
                            detail_id: expense_detail_id,
                            payment_type_id: item.id, // TODO: CHECK IF THIS IS CORRECTL ,
                            payment_type_remark: item.payment_type_remark,
                            created_by: req.user.id,
                            updated_by: req.user.id
                        } as any);
                    }
                    if (item?.payment_type_category.length > 0) {
                        for (const category of item.payment_type_category) {
                            if (category.payment_type_category_pattern == paymentTypeCategoryPattern.MULTIPLE) {
                                for (const option of category.categoryBranchValue) {
                                    const findDocumentCategoryBranch = await PaymentTypeCategoryBranch.findOne({
                                        attributes: ['id'],
                                        where: {
                                            id: option?.reference_id, branch_id: getExpenseDetail.branch_id, payment_type_category_branch_status
                                                : paymentTypeCategoryBranchStatus.ACTIVE
                                        }, raw: true
                                    })
                                    if (findDocumentCategoryBranch) {
                                        if (option.wsr_amount) {
                                            if (option.expense_item_id) {
                                                const findExpenseItem = await ExpenseItem.findOne({ attributes: ['id'], where: { id: option.expense_item_id }, raw: true })
                                                if (findExpenseItem) {
                                                    expenseItemIds.push(findExpenseItem.id)
                                                    await ExpenseItem.setHeaders(req).update({ expense_amount: option.expense_amount, expense_item_status: expense_item_status.ACTIVE, updated_by: req.user.id }, { where: { id: option.expense_item_id } })
                                                } else {
                                                    const findExpenseItem = await ExpenseItem.findOne({ attributes: ['id'], where: { expense_detail_id: getExpenseDetail.id, reference_id: option.reference_id, payment_type_category_id: category.payment_type_category_id }, raw: true })
                                                    if (!findExpenseItem) {
                                                        const createItem = await ExpenseItem.setHeaders(req).create({ expense_detail_id: getExpenseDetail.id, payment_type_category_id: category.payment_type_category_id, expense_amount: option.expense_amount, reference_id: option.reference_id, expense_item_status: expense_item_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id } as any)
                                                        if (createItem) {
                                                            expenseItemIds.push(createItem.id)
                                                        }
                                                    } else {
                                                        expenseItemIds.push(findExpenseItem.id)
                                                        await ExpenseItem.setHeaders(req).update({ expense_amount: option.expense_amount, expense_item_status: expense_item_status.ACTIVE, updated_by: req.user.id }, { where: { id: findExpenseItem.id } })
                                                    }
                                                }
                                            } else {
                                                const findExpenseItem = await ExpenseItem.findOne({ attributes: ['id'], where: { expense_detail_id: getExpenseDetail.id, reference_id: option.reference_id, payment_type_category_id: category.payment_type_category_id }, raw: true })
                                                if (!findExpenseItem) {
                                                    const createItem = await ExpenseItem.setHeaders(req).create({ expense_detail_id: getExpenseDetail.id, payment_type_category_id: category.payment_type_category_id, expense_amount: option.expense_amount, reference_id: option.reference_id, expense_item_status: expense_item_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id } as any)
                                                    if (createItem) {
                                                        expenseItemIds.push(createItem.id)
                                                    }
                                                } else {
                                                    expenseItemIds.push(findExpenseItem.id)
                                                    await ExpenseItem.setHeaders(req).update({ expense_amount: option.expense_amount, expense_item_status: expense_item_status.ACTIVE, updated_by: req.user.id }, { where: { id: findExpenseItem.id } })
                                                }
                                            }
                                        }
                                    }
                                }
                            } else {
                                if (category.expense_amount) {
                                    if (category.expense_item_id) {
                                        const findExpenseItem = await ExpenseItem.findOne({ attributes: ['id'], where: { id: category.expense_item_id }, raw: true })
                                        if (findExpenseItem) {
                                            expenseItemIds.push(findExpenseItem.id)
                                            await ExpenseItem.setHeaders(req).update({ expense_amount: category.expense_amount, expense_item_status: expense_item_status.ACTIVE, updated_by: req.user.id }, { where: { id: category.expense_item_id } })
                                        } else {
                                            const findExpenseItem = await ExpenseItem.findOne({ attributes: ['id'], where: { expense_detail_id: getExpenseDetail.id, payment_type_category_id: category.payment_type_category_id }, raw: true })
                                            if (!findExpenseItem) {
                                                const addExpenseItem = await ExpenseItem.setHeaders(req).create({ expense_detail_id: getExpenseDetail.id, payment_type_category_id: category.payment_type_category_id, expense_amount: category.expense_amount, expense_item_status: expense_item_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id } as any)
                                                if (addExpenseItem) {
                                                    expenseItemIds.push(addExpenseItem.id)
                                                }
                                            } else {
                                                expenseItemIds.push(findExpenseItem.id)
                                                await ExpenseItem.setHeaders(req).update({ expense_amount: category.expense_amount, expense_item_status: expense_item_status.ACTIVE, updated_by: req.user.id }, { where: { id: findExpenseItem.id } })
                                            }
                                        }
                                    } else {
                                        const findExpenseItem = await ExpenseItem.findOne({ attributes: ['id'], where: { expense_detail_id: getExpenseDetail.id, payment_type_category_id: category.payment_type_category_id }, raw: true })
                                        if (!findExpenseItem) {
                                            const addExpenseItem = await ExpenseItem.setHeaders(req).create({ expense_detail_id: getExpenseDetail.id, payment_type_category_id: category.payment_type_category_id, expense_amount: category.expense_amount, expense_item_status: expense_item_status.ACTIVE, created_by: req.user.id, updated_by: req.user.id } as any)

                                            if (addExpenseItem) {
                                                expenseItemIds.push(addExpenseItem.id)
                                            }
                                        } else {
                                            expenseItemIds.push(findExpenseItem.id)
                                            await ExpenseItem.setHeaders(req).update({ expense_amount: category.expense_amount, expense_item_status: expense_item_status.ACTIVE, updated_by: req.user.id }, { where: { id: findExpenseItem.id } })
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                if (expenseItemIds.length > 0) {
                    await ExpenseItem.setHeaders(req).update({ expense_item_status: expense_item_status.INACTIVE }, { where: { id: { [Op.notIn]: expenseItemIds }, expense_detail_id: getExpenseDetail.id } })
                }
                // Only super admin, admin, director, accountant can update WSR date
                if (findRole?.role_name == ROLE_CONSTANT.SUPER_ADMIN || findRole?.role_name == ROLE_CONSTANT.ADMIN || findRole?.role_name == ROLE_CONSTANT.DIRECTOR || findRole?.role_name == ROLE_CONSTANT.ACCOUNTANT) {
                    // Update the total amount for the DSR detail
                    await ExpenseDetail.setHeaders(req).update({ expense_month: expense_month, expense_year: expense_year }, { where: { id: expense_detail_id } });
                }
            }
            return res
                .status(StatusCodes.OK)
                .json({ status: true, message: res.__("EXPENSE_UPDATED_SUCCESSFULLY") });
        } else {
            return res
                .status(StatusCodes.EXPECTATION_FAILED)
                .json({ status: false, message: res.__("FAIL_EXPENSE_NOT_FOUND") });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getExpenseById = async (req: Request, res: Response) => {
    try {
        const { expense_detail_id }: any = req.params
        const findExpenseDetail: any = await ExpenseDetail.findOne({
            include: [
                {
                    model: User,
                    as: "expense_user",
                    attributes: [
                        'id', [
                            sequelize.fn(
                                "concat",
                                sequelize.col("user_first_name"),
                                " ",
                                sequelize.col("user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: Branch,
                    as: "expense_branch",
                    attributes: ["id", "branch_name"],
                    where: {
                        organization_id: req.user.organization_id
                    }
                }
            ], where: { id: expense_detail_id }, raw: true, nest: true
        })
        if (findExpenseDetail) {

            // Define base query conditions
            const whereObj: any = {
                organization_id: req.user.organization_id,
                payment_type_usage: { [Op.in]: [payment_type_usage.EXPENSE] },
            };

            whereObj[Op.or] = [
                { payment_type_status: paymentTypeStatus.ACTIVE },
                {
                    id: {
                        [Op.in]: sequelize.literal(`(SELECT pt.id
                            FROM nv_expense_items AS di 
                            JOIN nv_payment_type_category AS ptc ON ptc.id = di.payment_type_category_id
                            JOIN nv_payment_type AS pt ON pt.id = ptc.payment_type_id
                            WHERE di.expense_detail_id = ${expense_detail_id}
                            GROUP BY pt.id
                        )`)
                    }
                }
            ];

            // Fetch Payment Type Details
            let getPaymentTypeDetails: any = await PaymentType.findAll({
                attributes: ['id', 'payment_type_title', 'payment_type_usage', 'has_include_amount', 'has_field_currency', [sequelize.literal(`( 
                    SELECT payment_type_remark
                        FROM nv_payment_type_remarks AS pyr
                        WHERE pyr.detail_id = ${findExpenseDetail.id} AND pyr.payment_type_id = PaymentType.id )
                    `), 'payment_type_remark']],
                include: [
                    {
                        model: PaymentTypeCategory,
                        as: "payment_type_category",
                        attributes: [['id', 'payment_type_category_id'], 'payment_type_category_remarks', 'payment_type_category_title', 'payment_type_category_status', 'payment_type_category_pattern', 'payment_type_category_order', [sequelize.literal(`(SELECT id 
                            FROM nv_payment_type_category_branch 
                            WHERE branch_id = ${findExpenseDetail.branch_id} 
                            -- AND payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                            AND parent_id is null 
                            AND has_default_active = 1
                            AND payment_type_category_id = payment_type_category.id
                        )`), 'payment_type_category_branch_id'],
                        [sequelize.literal(`( 
                                SELECT di.expense_amount 
                                    FROM nv_expense_items AS di 
                                    WHERE di.expense_detail_id = ${findExpenseDetail.id} AND di.payment_type_category_id = payment_type_category.id AND di.expense_item_status = '${expense_item_status.ACTIVE}' AND  di.reference_id IS NULL
                                UNION SELECT 0 LIMIT 1)
                                `), 'expense_amount'],
                        [sequelize.literal(`( 
                                SELECT di.id 
                                    FROM nv_expense_items AS di 
                                    WHERE di.expense_detail_id = ${findExpenseDetail.id} AND di.payment_type_category_id = payment_type_category.id AND di.expense_item_status = '${expense_item_status.ACTIVE}' AND  di.reference_id IS NULL)
                                `), 'expense_item_id']
                        ],
                        where: {
                            [Op.or]: [
                                {
                                    payment_type_category_status: paymentTypeCategoryStatus.ACTIVE,
                                    id: {
                                        [Op.in]: [
                                            sequelize.literal(`(
                                                SELECT payment_type_category_id 
                                                FROM nv_payment_type_category_branch 
                                                WHERE branch_id = ${findExpenseDetail.branch_id} 
                                                AND payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                                                AND parent_id is null 
                                                AND has_default_active = 1)`)
                                        ]
                                    },
                                },
                                {
                                    id: {
                                        [Op.in]: [
                                            sequelize.literal(`(
                                                SELECT ptc.id
                                                FROM nv_expense_items AS di 
                                                JOIN nv_payment_type_category AS ptc ON ptc.id = di.payment_type_category_id
                                                WHERE di.expense_detail_id = ${expense_detail_id}
                                                GROUP BY ptc.id
                                            )`)
                                        ]
                                    }
                                }
                            ]
                        },
                    }
                ],
                where: whereObj,
                order: [
                    ['payment_type_usage', 'ASC'],
                    ['payment_type_order', 'ASC'],
                    [{ model: PaymentTypeCategory, as: 'payment_type_category' }, 'payment_type_category_order', 'ASC']
                ],
            });

            // If payment types exist, process the categories and fetch their branches
            if (getPaymentTypeDetails.length > 0) {
                // Convert data to JSON format for easier manipulation
                getPaymentTypeDetails = JSON.parse(JSON.stringify(getPaymentTypeDetails));

                // Loop through each payment type's category
                for (const paymentType of getPaymentTypeDetails) {
                    if (paymentType?.payment_type_category?.length > 0) {
                        for (const category of paymentType.payment_type_category) {

                            // Fetch category branches for each category
                            const findCategoryBranch = await PaymentTypeCategoryBranch.findAll({
                                attributes: [
                                    ['id', 'reference_id'],
                                    [sequelize.literal(`
                                        (SELECT nv_payment_type_category_value.field_value 
                                        FROM nv_payment_type_category_value 
                                        INNER JOIN nv_payment_type_category_field 
                                        ON nv_payment_type_category_value.payment_type_category_field_id = nv_payment_type_category_field.id
                                        WHERE nv_payment_type_category_value.payment_type_category_branch_id = PaymentTypeCategoryBranch.id 
                                        -- AND nv_payment_type_category_field.field_type = 'string' 
                                        AND nv_payment_type_category_field.payment_type_category_field_status = 'active'
                                        -- AND nv_payment_type_category_field.payment_type_category_id = ${category.payment_type_category_id}
                                        ORDER BY nv_payment_type_category_value.createdAt ASC 
                                        LIMIT 1
                                    )`), 'first_field_value'],
                                    [sequelize.literal(`( 
                                    SELECT di.expense_amount 
                                        FROM nv_expense_items AS di 
                                        WHERE di.expense_detail_id = ${findExpenseDetail.id} AND di.payment_type_category_id = ${category.payment_type_category_id} AND di.expense_item_status = '${expense_item_status.ACTIVE}' AND di.reference_id = PaymentTypeCategoryBranch.id 
                                    UNION SELECT 0 LIMIT 1)
                                    `), 'expense_amount'],
                                    [sequelize.literal(`( 
                                        SELECT di.id 
                                        FROM nv_expense_items AS di 
                                        WHERE di.expense_detail_id = ${findExpenseDetail.id} AND di.payment_type_category_id = ${category.payment_type_category_id} AND di.expense_item_status = '${expense_item_status.ACTIVE}' AND di.reference_id = PaymentTypeCategoryBranch.id )
                                        `), 'expense_item_id']
                                ],
                                where: {
                                    branch_id: findExpenseDetail.branch_id,
                                    parent_id: category.payment_type_category_branch_id,
                                    [Op.or]: [
                                        {
                                            payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE,
                                            has_default_active: 1,
                                        }, {
                                            id: {
                                                [Op.in]: [sequelize.literal(`(SELECT ptcb.id
                                                    FROM nv_expense_items AS di 
                                                    LEFT JOIN nv_payment_type_category_branch AS ptcb ON ptcb.id = di.reference_id
                                                    WHERE di.expense_detail_id = ${expense_detail_id} AND di.payment_type_category_id = ${category.payment_type_category_id}
                                                    GROUP BY ptcb.id
                                                )`)]
                                            }
                                        }
                                    ]
                                },
                                order: [['payment_type_category_branch_order', 'ASC']],
                            });

                            // Assign found branch value to category
                            category.categoryBranchValue = findCategoryBranch;
                        }
                    }
                }
            }

            findExpenseDetail.expenseItems = getPaymentTypeDetails;
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                data: findExpenseDetail,
                vat_per_data: global.config.VAT_PER_DATA
            });
        } else {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_DATA_NOT_FOUND"),
                data: {}
            });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getExpenseList = async (req: Request, res: Response) => {
    try {
        const { page, size, search, branch_id, expense_start_year, expense_end_year, expense_start_month, expense_end_month }: any = req.query
        const { limit, offset } = getPagination(page, size);
        const userDetail: any = await User.findOne({
            attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'user_role_id', 'branch_id'],
            where: { id: req.user.id, organization_id: req.user.organization_id },
            raw: true
        });
        if (!userDetail) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("ERROR_USER_NOT_FOUND"),
            });
        }

        let findRole: any;

        // Get user role using MORole system if available, otherwise fallback to old system
        if (userDetail.user_role_id) {
            // Use MORole system
            findRole = await MORole.findOne({
                where: {
                    id: userDetail.user_role_id,
                    organization_id: req.user.organization_id,
                    role_status: 'active'
                },
                raw: true
            });
        } else {
            // Fallback to old role system
            if (req.headers["platform-type"] == "web") {
                findRole = await Role.findOne({
                    where: { id: userDetail.web_user_active_role_id ? userDetail.web_user_active_role_id : userDetail.user_active_role_id },
                    raw: true
                });
            } else if ((req.headers["platform-type"] == "ios" || req.headers["platform-type"] == "android") && userDetail.user_active_role_id) {
                findRole = await Role.findOne({
                    where: { id: userDetail.user_active_role_id },
                    raw: true
                });
            }
        }

        const whereObj = {
            organization_id: req.user.organization_id
        }

        const expenseObj: any = {
            where: { expense_detail_status: { [Op.not]: expense_detail_status.DELETED } },
            attributes: ['id', 'expense_month', 'expense_year', 'expense_detail_status', [sequelize.literal(`IF((SELECT SUM(expense_amount) FROM nv_expense_items WHERE expense_detail_id = ExpenseDetail.id AND expense_item_status = '${expense_item_status.ACTIVE}') is not null,(SELECT SUM(expense_amount) FROM nv_expense_items WHERE expense_detail_id = ExpenseDetail.id AND expense_item_status = '${expense_item_status.ACTIVE}'),0)
            `), 'amount'],
                [
                    sequelize.literal(`
                  IF(
                    (
                      SELECT ROUND(SUM(expense_amount), 2)
                      FROM nv_expense_items AS items
                      INNER JOIN nv_payment_type_category AS ptc
                        ON items.payment_type_category_id = ptc.id
                      INNER JOIN nv_payment_type AS pt
                        ON ptc.payment_type_id = pt.id
                      WHERE items.expense_detail_id = ExpenseDetail.id
                      AND items.expense_item_status = '${expense_item_status.ACTIVE}'
                      AND pt.has_include_amount = true
                    ) IS NOT NULL,
                    (
                      SELECT ROUND(SUM(expense_amount), 2)
                      FROM nv_expense_items AS items
                      INNER JOIN nv_payment_type_category AS ptc
                        ON items.payment_type_category_id = ptc.id
                      INNER JOIN nv_payment_type AS pt
                        ON ptc.payment_type_id = pt.id
                      WHERE items.expense_detail_id = ExpenseDetail.id
                      AND items.expense_item_status = '${expense_item_status.ACTIVE}'
                      AND pt.has_include_amount = true
                    ),
                    0
                  )
                `),
                    'amount'
                ],
                [sequelize.literal(`EXISTS (SELECT 1 FROM nv_expense_requests WHERE nv_expense_requests.expense_detail_id = ExpenseDetail.id AND expense_request_status != '${expense_request_status.DELETED}')`), 'has_request']
            ],
            include: [
                {
                    model: User,
                    as: "expense_user",
                    attributes: [
                        'id', [
                            sequelize.fn(
                                "concat",
                                sequelize.col("expense_user.user_first_name"),
                                " ",
                                sequelize.col("expense_user.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: User,
                    as: "expense_detail_updated_by",
                    attributes: [
                        'id', [
                            sequelize.fn(
                                "concat",
                                sequelize.col("expense_detail_updated_by.user_first_name"),
                                " ",
                                sequelize.col("expense_detail_updated_by.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: Branch,
                    as: "expense_branch",
                    attributes: [
                        "id",
                        "branch_name",
                        "branch_color",
                        "text_color"
                    ],
                    where: whereObj
                },
            ],
            order: [['expense_year', 'DESC'], ['expense_month', 'DESC']],
        }
        if (page && size) {
            expenseObj.limit = Number(limit);
            expenseObj.offset = Number(offset);
        }
        if (branch_id) {
            expenseObj.where.branch_id = branch_id;
        }
        if (search) {
            expenseObj.where[Op.or] = [{ '$expense_user.user_first_name$': { [Op.like]: `%${search}%` } }, { '$expense_user.user_last_name$': { [Op.like]: `%${search}%` } }, { '$expense_branch.branch_name$': { [Op.like]: `%${search}%` } }]
        }
        if ((findRole?.role_name == ROLE_CONSTANT.BRANCH_MANAGER) || findRole?.role_name == ROLE_CONSTANT.HOTEL_MANAGER) {
            expenseObj.where.branch_id = userDetail?.branch_id
        }

        /** add date filter of expense_month and expense_year */
        if (expense_start_month && expense_end_month) {
            expenseObj.where.expense_month = {
                [Op.between]: [expense_start_month, expense_end_month]
            };
        }
        if (expense_start_year && expense_end_year) {
            expenseObj.where.expense_year = {
                [Op.between]: [expense_start_year, expense_end_year]
            };
        }

        const { count, rows: getExpenseList } = await ExpenseDetail.findAndCountAll(expenseObj)

        const { total_pages } = getPaginatedItems(
            size,
            page,
            count || 0,
        );
        return res.status(StatusCodes.OK).json({
            status: true,
            data: getExpenseList,
            message: res.__("SUCCESS_FETCHED"),
            count: count,
            page: parseInt(page),
            size: parseInt(size),
            total_pages,
        });
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const deleteExpenseById = async (req: Request, res: Response) => {
    try {
        const { expense_detail_id }: any = req.params
        const findExpenseDetail = await ExpenseDetail.findOne({ attributes: ['id'], where: { id: expense_detail_id }, raw: true })
        if (!findExpenseDetail) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FAIL_EXPENSE_NOT_FOUND"),
            });
        }
        const deleteExpense = await ExpenseDetail.setHeaders(req).update({ expense_detail_status: expense_detail_status.DELETED }, { where: { id: expense_detail_id } })
        if (deleteExpense.length > 0) {
            await ExpenseItem.setHeaders(req).update({ expense_item_status: expense_item_status.DELETED }, { where: { expense_detail_id: expense_detail_id } })
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("EXPENSE_DELETED_SUCCESSFULLY"),
            });
        } else {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAILED_TO_DELETE_EXPENSE"),
            });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getExpenseRequestList = async (req: Request, res: Response) => {
    try {
        const { page, size, search, branch_id, expense_id, expense_start_month, expense_end_month, expense_start_year, expense_end_year, request_status }: any = req.query
        const { limit, offset } = getPagination(page, size);
        const branchWhereObj: any = {
            organization_id: req.user.organization_id
        }
        const userDetail: any = await User.findOne({
            attributes: ['id', 'web_user_active_role_id', 'user_active_role_id', 'user_role_id', 'branch_id'],
            where: { id: req.user.id, organization_id: req.user.organization_id },
            raw: true
        });

        let findRole: any;

        // Get user role using MORole system if available, otherwise fallback to old system
        if (userDetail.user_role_id) {
            // Use MORole system
            findRole = await MORole.findOne({
                attributes: ['id', 'role_name'],
                where: {
                    id: userDetail.user_role_id,
                    organization_id: req.user.organization_id,
                    role_status: 'active'
                },
                raw: true
            });
        } else {
            // Fallback to old role system
            if (req.headers["platform-type"] == "web") {
                findRole = await Role.findOne({
                    attributes: ['id', 'role_name'],
                    where: { id: userDetail.web_user_active_role_id ? userDetail.web_user_active_role_id : userDetail.user_active_role_id },
                    raw: true
                });
            } else if ((req.headers["platform-type"] == "ios" || req.headers["platform-type"] == "android") && userDetail.user_active_role_id) {
                findRole = await Role.findOne({
                    attributes: ['id', 'role_name'],
                    where: { id: userDetail.user_active_role_id },
                    raw: true
                });
            }
        }
        if (branch_id) {
            branchWhereObj.id = branch_id
        }
        if ((findRole?.role_name == ROLE_CONSTANT.BRANCH_MANAGER) || findRole?.role_name == ROLE_CONSTANT.HOTEL_MANAGER) {
            branchWhereObj.id = userDetail?.branch_id
        }
        const expenseWhereObj: any = {}
        if (expense_start_month && expense_end_month) {
            expenseWhereObj.expense_month = {
                [Op.between]: [expense_start_month, expense_end_month]
            }
        }
        if (expense_start_year && expense_end_year) {
            expenseWhereObj.expense_year = {
                [Op.between]: [expense_start_year, expense_end_year]
            }
        }
        if (expense_id) {
            expenseWhereObj.id = expense_id
        }
        const expenseObj: any = {
            where: { expense_request_status: { [Op.not]: expense_request_status.DELETED } },
            attributes: ['id', 'expense_detail_id', 'expense_detail.branch_id', 'expense_detail.expense_month', 'expense_detail.expense_year', 'expense_detail.expense_detail_status', 'expense_request_status', [sequelize.literal(`IF((SELECT SUM(expense_amount) FROM nv_expense_item_requests WHERE expense_request_detail_id = ExpenseRequest.id AND expense_request_item_status = '${expense_request_item_status.ACTIVE}') is not null,(SELECT SUM(expense_amount) FROM nv_expense_item_requests WHERE expense_request_detail_id = ExpenseRequest.id AND expense_request_item_status = '${expense_request_item_status.ACTIVE}'),0)`), 'amount'],

                [
                    sequelize.literal(`
                  IF(
                    (
                      SELECT ROUND(SUM(expense_amount), 2)
                      FROM nv_expense_item_requests AS items
                      INNER JOIN nv_payment_type_category AS ptc
                        ON items.payment_type_category_id = ptc.id
                      INNER JOIN nv_payment_type AS pt
                        ON ptc.payment_type_id = pt.id
                      WHERE items.expense_request_detail_id = ExpenseRequest.id
                      AND items.expense_request_item_status = '${expense_request_item_status.ACTIVE}'
                      AND pt.has_include_amount = true
                    ) IS NOT NULL,
                    (
                      SELECT ROUND(SUM(expense_amount), 2)
                      FROM nv_expense_item_requests AS items
                      INNER JOIN nv_payment_type_category AS ptc
                        ON items.payment_type_category_id = ptc.id
                      INNER JOIN nv_payment_type AS pt
                        ON ptc.payment_type_id = pt.id
                      WHERE items.expense_request_detail_id = ExpenseRequest.id
                      AND items.expense_request_item_status = '${expense_request_item_status.ACTIVE}'
                      AND pt.has_include_amount = true
                    ),
                    0
                  )
                `),
                    'amount'
                ]
            ],
            include: [
                {
                    model: ExpenseDetail,
                    as: "expense_detail",
                    attributes: [
                        "id",
                        "branch_id", "expense_month", "expense_year", "expense_detail_status"],
                    where: expenseWhereObj,
                    include: [{
                        model: Branch,
                        as: "expense_branch",
                        attributes: [
                            "id",
                            "branch_name",
                            "branch_color",
                            "text_color"
                        ],
                        where: branchWhereObj
                    }]
                },
                {
                    model: User,
                    as: "expense_request_user",
                    attributes: [
                        'id', [
                            sequelize.fn(
                                "concat",
                                sequelize.col("expense_request_user.user_first_name"),
                                " ",
                                sequelize.col("expense_request_user.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: User,
                    as: "expense_request_updated_by",
                    attributes: [
                        'id', [
                            sequelize.fn(
                                "concat",
                                sequelize.col("expense_request_updated_by.user_first_name"),
                                " ",
                                sequelize.col("expense_request_updated_by.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                }
            ],
            order: [['updatedAt', 'DESC']],
            raw: true,
            nest: true
        }
        if (page && size) {
            expenseObj.limit = Number(limit);
            expenseObj.offset = Number(offset);
        }

        if (search) {
            expenseObj.where[Op.or] = [{ '$expense_request_user.user_first_name$': { [Op.like]: `%${search}%` } }, { '$expense_request_user.user_last_name$': { [Op.like]: `%${search}%` } }, { '$expense_detail.expense_branch.branch_name$': { [Op.like]: `%${search}%` } }]
        }

        if (request_status) {
            expenseObj.where.expense_request_status = request_status;
        }
        const { count, rows: getExpenseList } = await ExpenseRequest.findAndCountAll(expenseObj)
        const transformedData = getExpenseList.map((item: any) => {
            return {
                id: item.id,
                expense_month: item.expense_detail.expense_month,
                expense_year: item.expense_detail.expense_year,
                expense_detail_status: item.expense_detail.expense_detail_status,
                expense_request_status: item.expense_request_status,
                amount: item.amount,
                expense_user: {
                    id: item.expense_request_user.id,
                    user_full_name: item.expense_request_user.user_full_name
                },
                expense_branch: {
                    id: item.expense_detail.expense_branch.id,
                    branch_name: item.expense_detail.expense_branch.branch_name,
                    branch_color: item.expense_detail.expense_branch.branch_color,
                    text_color: item.expense_detail.expense_branch.text_color
                },
                expense_request_updated_by: {
                    id: item.expense_request_updated_by.id,
                    user_full_name: item.expense_request_updated_by.user_full_name
                },
            };
        });
        const { total_pages } = getPaginatedItems(
            size,
            page,
            count || 0,
        );
        return res.status(StatusCodes.OK).json({
            status: true,
            data: transformedData,
            message: res.__("SUCCESS_FETCHED"),
            count: count,
            page: parseInt(page),
            size: parseInt(size),
            total_pages,
        });
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getExpenseRequestById = async (req: Request, res: Response) => {
    try {
        const { expense_request_id }: any = req.params
        const findExpenseRequest: any = await ExpenseRequest.findOne({
            include: [
                {
                    model: User,
                    as: "expense_request_user",
                    attributes: [
                        'id', [
                            sequelize.fn(
                                "concat",
                                sequelize.col("expense_request_user.user_first_name"),
                                " ",
                                sequelize.col("expense_request_user.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: User,
                    as: "expense_request_updated_by",
                    attributes: [
                        'id', [
                            sequelize.fn(
                                "concat",
                                sequelize.col("expense_request_updated_by.user_first_name"),
                                " ",
                                sequelize.col("expense_request_updated_by.user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: ExpenseDetail,
                    as: "expense_detail",
                    attributes: [
                        "id",
                        "branch_id", "expense_month", "expense_year", "expense_detail_status"],
                    include: [{
                        model: Branch,
                        as: "expense_branch",
                        attributes: ["id", "branch_name"],
                        where: {
                            organization_id: req.user.organization_id
                        }
                    }]
                }
            ], where: { id: expense_request_id }, raw: true, nest: true
        })
        if (findExpenseRequest) {

            // Define base query conditions
            const whereObj: any = {
                organization_id: req.user.organization_id,
                payment_type_usage: { [Op.in]: [payment_type_usage.EXPENSE] },
            };

            whereObj[Op.or] = [
                { payment_type_status: paymentTypeStatus.ACTIVE },
                {
                    id: {
                        [Op.in]: sequelize.literal(`(SELECT pt.id
                            FROM nv_expense_item_requests AS dir 
                            JOIN nv_payment_type_category AS ptc ON ptc.id = dir.payment_type_category_id
                            JOIN nv_payment_type AS pt ON pt.id = ptc.payment_type_id
                            WHERE dir.expense_request_detail_id = ${findExpenseRequest.id}
                            GROUP BY pt.id
                        )`)
                    }
                }
            ];

            // Fetch Payment Type Details
            let getPaymentTypeDetails: any = await PaymentType.findAll({
                attributes: ['id', 'payment_type_title', 'payment_type_usage', 'has_include_amount', 'has_field_currency', [sequelize.literal(`( 
                    SELECT payment_type_remark
                        FROM nv_payment_type_remarks AS pyr
                        WHERE pyr.detail_id = ${findExpenseRequest?.expense_detail?.id} AND pyr.payment_type_id = PaymentType.id )
                    `), 'payment_type_remark']],
                include: [
                    {
                        model: PaymentTypeCategory,
                        as: "payment_type_category",
                        attributes: [['id', 'payment_type_category_id'], 'payment_type_category_remarks', 'payment_type_category_title', 'payment_type_category_status', 'payment_type_category_pattern', 'payment_type_category_order', [sequelize.literal(`(SELECT id 
                            FROM nv_payment_type_category_branch 
                            WHERE branch_id = ${findExpenseRequest?.expense_detail?.branch_id} 
                          -- AND payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                            AND parent_id is null 
                            AND has_default_active = 1
                            AND payment_type_category_id = payment_type_category.id
                        )`), 'payment_type_category_branch_id'],
                        [sequelize.literal(`( 
                                SELECT dir.expense_amount 
                                    FROM nv_expense_item_requests AS dir 
                                    WHERE dir.expense_request_detail_id = ${findExpenseRequest.id} AND dir.payment_type_category_id = payment_type_category.id AND dir.expense_request_item_status = '${expense_request_item_status.ACTIVE}' AND dir.reference_id IS NULL
                                UNION SELECT 0 LIMIT 1)
                                `), 'expense_amount'],
                        [sequelize.literal(`( 
                                SELECT dir.id 
                                    FROM nv_expense_item_requests AS dir 
                                    WHERE dir.expense_request_detail_id = ${findExpenseRequest.id} AND dir.payment_type_category_id = payment_type_category.id AND dir.expense_request_item_status = '${expense_request_item_status.ACTIVE}' AND dir.reference_id IS NULL)
                                `), 'expense_request_item_id'],
                        [sequelize.literal(`( 
                                    SELECT dir.old_expense_amount 
                                        FROM nv_expense_item_requests AS dir 
                                        WHERE dir.expense_request_detail_id = ${findExpenseRequest.id} AND dir.payment_type_category_id = payment_type_category.id AND dir.expense_request_item_status = '${expense_request_item_status.ACTIVE}' AND dir.reference_id IS NULL
                                    UNION SELECT 0 LIMIT 1)
                                    `), 'old_expense_amount']
                        ],
                        where: {
                            [Op.or]: [
                                {
                                    payment_type_category_status: paymentTypeCategoryStatus.ACTIVE,
                                    id: {
                                        [Op.in]: [
                                            sequelize.literal(`(
                                                SELECT payment_type_category_id 
                                                FROM nv_payment_type_category_branch 
                                                WHERE branch_id = ${findExpenseRequest?.expense_detail?.branch_id} 
                                                AND payment_type_category_branch_status = '${paymentTypeCategoryBranchStatus.ACTIVE}'
                                                AND parent_id is null 
                                                AND has_default_active = 1)`)
                                        ]
                                    },
                                },
                                {
                                    id: {
                                        [Op.in]: [
                                            sequelize.literal(`(
                                                SELECT ptc.id
                                                FROM nv_expense_item_requests AS dir 
                                                JOIN nv_payment_type_category AS ptc ON ptc.id = dir.payment_type_category_id
                                                WHERE dir.expense_request_detail_id = ${findExpenseRequest.id}
                                                GROUP BY ptc.id
                                            )`)
                                        ]
                                    }
                                }
                            ]
                        },
                    }
                ],
                where: whereObj,
                order: [
                    ['payment_type_usage', 'ASC'],
                    ['payment_type_order', 'ASC'],
                    [{ model: PaymentTypeCategory, as: 'payment_type_category' }, 'payment_type_category_order', 'ASC']
                ],
            });

            // If payment types exist, process the categories and fetch their branches
            if (getPaymentTypeDetails.length > 0) {
                // Convert data to JSON format for easier manipulation
                getPaymentTypeDetails = JSON.parse(JSON.stringify(getPaymentTypeDetails));

                // Loop through each payment type's category
                for (const paymentType of getPaymentTypeDetails) {
                    if (paymentType?.payment_type_category?.length > 0) {
                        for (const category of paymentType.payment_type_category) {

                            // Fetch category branches for each category
                            const findCategoryBranch = await PaymentTypeCategoryBranch.findAll({
                                attributes: [
                                    ['id', 'reference_id'],
                                    [sequelize.literal(`
                                        (SELECT nv_payment_type_category_value.field_value 
                                        FROM nv_payment_type_category_value 
                                        INNER JOIN nv_payment_type_category_field 
                                        ON nv_payment_type_category_value.payment_type_category_field_id = nv_payment_type_category_field.id
                                        WHERE nv_payment_type_category_value.payment_type_category_branch_id = PaymentTypeCategoryBranch.id 
                                        -- AND nv_payment_type_category_field.field_type = 'string' 
                                        AND nv_payment_type_category_field.payment_type_category_field_status = 'active'
                                        ORDER BY nv_payment_type_category_value.createdAt ASC 
                                        LIMIT 1
                                    )`), 'first_field_value'],
                                    [sequelize.literal(`( 
                                    SELECT dir.expense_amount 
                                        FROM nv_expense_item_requests AS dir 
                                        WHERE dir.expense_request_detail_id = ${findExpenseRequest.id} AND dir.payment_type_category_id = ${category.payment_type_category_id} AND dir.expense_request_item_status = '${expense_request_item_status.ACTIVE}' AND dir.reference_id = PaymentTypeCategoryBranch.id 
                                    UNION SELECT 0 LIMIT 1)
                                    `), 'expense_amount'],
                                    [sequelize.literal(`( 
                                        SELECT dir.id 
                                        FROM nv_expense_item_requests AS dir
                                        WHERE dir.expense_request_detail_id = ${findExpenseRequest.id} AND dir.payment_type_category_id = ${category.payment_type_category_id} AND dir.expense_request_item_status = '${expense_request_item_status.ACTIVE}' AND dir.reference_id = PaymentTypeCategoryBranch.id )
                                        `), 'expense_item_id'],
                                    [sequelize.literal(`( 
                                            SELECT dir.old_expense_amount 
                                                FROM nv_expense_item_requests AS dir 
                                                WHERE dir.expense_request_detail_id = ${findExpenseRequest.id} AND dir.payment_type_category_id = ${category.payment_type_category_id} AND dir.expense_request_item_status = '${expense_request_item_status.ACTIVE}' AND dir.reference_id = PaymentTypeCategoryBranch.id 
                                            UNION SELECT 0 LIMIT 1)
                                            `), 'old_expense_amount']
                                ],
                                where: {
                                    branch_id: findExpenseRequest?.expense_detail?.branch_id,
                                    parent_id: category.payment_type_category_branch_id,
                                    [Op.or]: [
                                        {
                                            payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE,
                                            has_default_active: 1
                                        }, {
                                            id: {
                                                [Op.in]: [sequelize.literal(`(SELECT ptcb.id
                                                    FROM nv_expense_item_requests AS dir 
                                                    LEFT JOIN nv_payment_type_category_branch AS ptcb ON ptcb.id = dir.reference_id
                                                    WHERE dir.expense_request_detail_id = ${findExpenseRequest.id} AND dir.payment_type_category_id = ${category.payment_type_category_id}
                                                    GROUP BY ptcb.id
                                                )`)]
                                            }
                                        }
                                    ]
                                },
                                order: [['payment_type_category_branch_order', 'ASC']],
                            });

                            // Assign found branch value to category
                            category.categoryBranchValue = findCategoryBranch;
                        }
                    }
                }
            }

            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                data: {
                    id: findExpenseRequest?.expense_detail?.id,
                    expense_month: findExpenseRequest?.expense_detail?.expense_month,
                    expense_year: findExpenseRequest?.expense_detail?.expense_year,
                    expense_detail_status: findExpenseRequest?.expense_detail?.expense_detail_status,
                    request_remark: findExpenseRequest.request_remark,
                    expense_request_status: findExpenseRequest.expense_request_status,
                    expense_branch: findExpenseRequest?.expense_detail?.expense_branch?.branch_name,
                    expense_branch_id: findExpenseRequest?.expense_detail?.expense_branch?.id,
                    submitted_user: findExpenseRequest?.expense_request_user?.user_full_name,
                    expense_items: getPaymentTypeDetails || [],
                    expense_request_updated_by: findExpenseRequest?.expense_request_updated_by
                },
                vat_per_data: global.config.VAT_PER_DATA
            });
        } else {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_DATA_NOT_FOUND"),
                data: {}
            });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const approveRejectRequest = async (req: Request, res: Response) => {
    try {
        const { expense_request_id, request_status, request_remark }: any = req.body
        const findExpenseDetail = await ExpenseRequest.findOne({ attributes: ['id', 'expense_detail_id', 'created_by'], where: { id: expense_request_id, expense_request_status: expense_request_status.PENDING }, raw: true })
        if (!findExpenseDetail) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_EXPENSE_REQUEST_NOT_FOUND"),
            });
        }
        const findExpenseDate: any = await ExpenseDetail.findOne({ where: { id: findExpenseDetail.expense_detail_id } })

        const findUser = await User.findOne({ attributes: ['id', 'appToken', 'webAppToken'], where: { id: findExpenseDate?.user_id, organization_id: req.user.organization_id }, raw: true })
        if (!findUser) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("ERROR_USER_NOT_FOUND"),
            });
        }
        const getActionUserFullName: any = await getUserFullName(req.user.id)
        if (request_status == expense_request_status.APPROVED) {
            const updateExpenseRequest = await ExpenseRequest.setHeaders(req).update({ expense_request_status: expense_request_status.APPROVED, request_remark: request_remark, updated_by: req.user.id }, { where: { id: expense_request_id } })
            if (updateExpenseRequest.length > 0) {
                const findExpenseItemList = await ExpenseItemRequest.findAll({ attributes: ['id', 'payment_type_category_id', 'reference_id', 'expense_amount'], where: { expense_request_detail_id: findExpenseDetail.id, expense_request_item_status: expense_request_item_status.ACTIVE }, raw: true })
                if (findExpenseItemList.length > 0) {
                    const expenseItemIds = []
                    for (const expenseItem of findExpenseItemList) {
                        const findExpenseItem = await ExpenseItem.findOne({ attributes: ['id', 'reference_id'], where: { expense_detail_id: findExpenseDetail.expense_detail_id, payment_type_category_id: expenseItem.payment_type_category_id, expense_item_status: expense_item_status.ACTIVE, reference_id: expenseItem.reference_id }, raw: true })
                        if (findExpenseItem) {
                            if (expenseItem.reference_id) {
                                if (expenseItem.reference_id == findExpenseItem.reference_id) {

                                    const findDocumentCategoryBranch = await PaymentTypeCategoryBranch.findOne({
                                        where: {
                                            id: expenseItem.reference_id, branch_id: findExpenseDate.branch_id, payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE
                                        }, raw: true
                                    })
                                    if (findDocumentCategoryBranch) {
                                        await ExpenseItem.setHeaders(req).update({ expense_amount: expenseItem.expense_amount, expense_item_status: expense_item_status.ACTIVE, updated_by: findExpenseDetail.created_by }, { where: { expense_detail_id: findExpenseDetail.expense_detail_id, payment_type_category_id: expenseItem.payment_type_category_id, id: findExpenseItem.id } })
                                        expenseItemIds.push(findExpenseItem.id)
                                    }
                                } else {
                                    const findDocumentCategoryBranch = await PaymentTypeCategoryBranch.findOne({
                                        attributes: ['id'],
                                        where: {
                                            id: expenseItem.reference_id, branch_id: findExpenseDate.branch_id, payment_type_category_branch_status: paymentTypeCategoryBranchStatus.ACTIVE
                                        }, raw: true
                                    })
                                    if (findDocumentCategoryBranch) {
                                        const createExpenseItem = await ExpenseItem.setHeaders(req).create({ expense_detail_id: findExpenseDetail.expense_detail_id, payment_type_category_id: expenseItem?.payment_type_category_id, expense_amount: expenseItem.expense_amount, reference_id: expenseItem.reference_id, expense_item_status: expense_item_status.ACTIVE, created_by: findExpenseDetail.created_by, updated_by: findExpenseDetail.created_by } as any)
                                        if (createExpenseItem) {
                                            expenseItemIds.push(createExpenseItem.id)
                                        }
                                    }
                                }
                            } else {
                                await ExpenseItem.setHeaders(req).update({ expense_amount: expenseItem.expense_amount, expense_item_status: expense_item_status.ACTIVE, updated_by: findExpenseDetail.created_by }, { where: { expense_detail_id: findExpenseDetail.expense_detail_id, payment_type_category_id: expenseItem.payment_type_category_id, id: findExpenseItem.id } })
                                expenseItemIds.push(findExpenseItem.id)
                            }
                        } else {
                            if (expenseItem.reference_id) {
                                const createExpenseItem = await ExpenseItem.setHeaders(req).create({ expense_detail_id: findExpenseDetail.expense_detail_id, payment_type_category_id: expenseItem?.payment_type_category_id, expense_amount: expenseItem.expense_amount, reference_id: expenseItem.reference_id, expense_item_status: expense_item_status.ACTIVE, created_by: findExpenseDetail.created_by, updated_by: findExpenseDetail.created_by } as any)
                                if (createExpenseItem) {
                                    expenseItemIds.push(createExpenseItem.id)
                                }
                            } else {
                                const createExpenseItem = await ExpenseItem.setHeaders(req).create({ expense_detail_id: findExpenseDetail.expense_detail_id, payment_type_category_id: expenseItem?.payment_type_category_id, expense_amount: expenseItem.expense_amount, expense_item_status: expense_item_status.ACTIVE, created_by: findExpenseDetail.created_by, updated_by: findExpenseDetail.created_by } as any)
                                if (createExpenseItem) {
                                    expenseItemIds.push(createExpenseItem.id)
                                }
                            }
                        }
                    }
                    if (expenseItemIds.length > 0) {
                        await ExpenseItem.setHeaders(req).update({ expense_item_status: expense_item_status.INACTIVE }, { where: { id: { [Op.notIn]: expenseItemIds }, expense_detail_id: findExpenseDetail.expense_detail_id } })
                    }
                }

                await createNotification([findUser], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.EXPENSE_RESPONSE.content(getActionUserFullName, expense_request_status.APPROVED, findExpenseDate?.expense_month, findExpenseDate?.expense_year), NOTIFICATIONCONSTANT.EXPENSE_RESPONSE.heading, REDIRECTION_TYPE.EXPENSE_REQUEST, findExpenseDate.id, { expense_id: findExpenseDate.id, request_id: findExpenseDetail.id })
            }
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("EXPENSE_REQUEST_APPROVED"),
            });
        } else if (request_status == expense_request_status.REJECTED) {
            const updateExpenseRequest = await ExpenseRequest.setHeaders(req).update({ expense_request_status: expense_request_status.REJECTED, request_remark: request_remark, updated_by: req.user.id }, { where: { id: expense_request_id } })
            if (updateExpenseRequest.length > 0) {

                await ExpenseItemRequest.setHeaders(req).update({ expense_request_item_status: expense_request_item_status.ACTIVE }, { where: { expense_request_detail_id: expense_request_id } })
                await createNotification([findUser], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.EXPENSE_RESPONSE.content(getActionUserFullName, expense_request_status.REJECTED, findExpenseDate.expense_month, findExpenseDate.expense_year), NOTIFICATIONCONSTANT.EXPENSE_RESPONSE.heading, REDIRECTION_TYPE.EXPENSE_REQUEST, findExpenseDate.id, { expense_id: findExpenseDate.id, request_id: findExpenseDetail.id })
                return res.status(StatusCodes.OK).json({
                    status: true,
                    message: res.__("EXPENSE_REQUEST_REJECTED"),
                });
            }
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getExpenseReport = async (req: Request, res: Response) => {
    try {
        const { start_date, end_date, expense_payment_type, branch_id, type }: any = req.query

        const reportData = await generateReport(branch_id, start_date, end_date, expense_payment_type, 0, type, req.user.organization_id)

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: reportData
        });
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const checkExpenseExist = async (req: Request, res: Response) => {
    try {
        const { branch_id, expense_month, expense_year }: any = req.body
        const getExpenseDetail = await ExpenseDetail.findOne({ attributes: ['id'], where: { branch_id: branch_id, expense_month, expense_year, expense_detail_status: expense_detail_status.ACTIVE }, raw: true })
        if (getExpenseDetail) {
            return res
                .status(StatusCodes.BAD_REQUEST)
                .json({ status: false, message: res.__("EXPENSE_ALREADY_ADDED") });
        } else {
            return res
                .status(StatusCodes.OK)
                .json({ status: true, message: res.__("FAIL_EXPENSE_NOT_FOUND") });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const getExpenseActivity = async (req: Request, res: Response) => {
    try {
        // Extract expense_id from route params and page/size from query params
        const { expense_id }: any = req.params
        const { page, size }: any = req.query

        // Get pagination limit and offset based on page and size
        const { limit, offset } = getPagination(Number(page), Number(size));

        // Fetch expense detail including related user and branch data
        const findExpenseDetail: any = await ExpenseDetail.findOne({
            include: [
                {
                    model: User,
                    as: "expense_user",
                    attributes: [
                        'id', [
                            sequelize.fn(
                                "concat",
                                sequelize.col("user_first_name"),
                                " ",
                                sequelize.col("user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
                {
                    model: Branch,
                    as: "expense_branch",
                    attributes: ["id", "branch_name"],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
            ],
            where: {
                id: expense_id,
                expense_detail_status: { [Op.not]: expense_detail_status.DELETED }
            },
            raw: true,
            nest: true
        })

        // Check if the expense detail was found, if not return an error response
        if (!findExpenseDetail) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FAIL_EXPENSE_NOT_FOUND"),
            });
        }

        // Construct query object for fetching expense activities related to the expense detail
        const expenseDetailObj: any = {
            include: [
                {
                    model: User,
                    as: "users",
                    attributes: [
                        "id",
                        [
                            sequelize.fn(
                                "concat",
                                sequelize.col("user_first_name"),
                                " ",
                                sequelize.col("user_last_name"),
                            ),
                            "user_full_name",
                        ],
                        "user_email",
                        "employment_number"
                    ],
                    where: {
                        organization_id: req.user.organization_id
                    }
                },
            ],
            where: {
                activity_table: { [Op.in]: ['ExpenseItem', 'ExpenseRequest', 'ExpenseDetail'] },
                [Op.or]: [
                    { previous_data: { [Op.like]: `%"expense_detail_id":${expense_id}%` } },
                    { new_data: { [Op.like]: `%"expense_detail_id":${expense_id}%` } }
                ]
            },
            order: [['createdAt', 'DESC']]
        }

        // Apply pagination if page and size are provided
        if (page && size) {
            expenseDetailObj.limit = Number(limit)
            expenseDetailObj.offset = Number(offset)
        }

        // Fetch activities and count matching the expense detail
        const { count, rows: getExpenseList } = await Activity.findAndCountAll(expenseDetailObj)
        const getExpenseDetail = JSON.parse(JSON.stringify(getExpenseList))

        // Check if any expense activities were found
        if (getExpenseDetail.length > 0) {

            // Format the activity_table field by adding spaces before capital letters
            getExpenseDetail.forEach((log: any) => {
                log.activity_table = addSpacesBeforeCapitals(log.activity_table);
            })

            // Get total pages for paginated response
            const { total_pages } = getPaginatedItems(
                size,
                page,
                count || 0,
            );

            // Return successful response with expense details and activities
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                data: {
                    id: findExpenseDetail?.id,
                    expense_branch: findExpenseDetail?.expense_branch?.branch_name,
                    expense_user: findExpenseDetail?.expense_user?.user_full_name,
                    expense_month: findExpenseDetail.expense_month,
                    expense_year: findExpenseDetail.expense_year,
                    expense_activity: getExpenseDetail
                },
                page: parseInt(page),
                size: parseInt(size),
                count: count,
                total_pages,
            });
        } else {
            // Return response when no expense activities are found
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("FAIL_DATA_FETCHED"),
                data: [],
                page: 0,
                size: 0,
                count: 0,
                total_pages: 0,
            });
        }
    } catch (error) {
        // Log the error and return a service unavailable response in case of an exception
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

export default {
    addExpenseDetail,
    updateExpenseDetail,
    getExpenseById,
    getExpenseList,
    deleteExpenseById,
    approveRejectRequest,
    getExpenseRequestList,
    getExpenseRequestById,
    getExpenseReport,
    checkExpenseExist,
    getExpenseActivity
}; 