"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

interface itemOwnerAttributes {
    id: number;
    item_id: number;
    owner_id: number;
}

export class ItemOwner
  extends Model<itemOwnerAttributes, never>
  implements itemOwnerAttributes
{
  id!: number;
  item_id!: number;
  owner_id!: number;
}

ItemOwner.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    item_id: {
      type: DataTypes.INTEGER,
    },
    owner_id: {
      type: DataTypes.INTEGER,
    }
  },
  {
    sequelize: sequelize,
    tableName: "nv_item_owners",
    modelName: "ItemOwner",
    timestamps: true,
  }
);
