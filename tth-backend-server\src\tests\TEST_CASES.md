# Test Cases for validateModulePermission Function

This document outlines all test cases for the `validateModulePermission` function that validates user permissions using MORole, MOPermission, and MOModule models.

## Test Categories

### 1. **Successful Permission Validation**
These tests verify that the function returns `true` when all conditions are met and the user has the required permission.

#### Test Cases:
- ✅ **VIEW Permission**: User has VIEW permission for a module
- ✅ **CREATE Permission**: User has CREATE permission for a module  
- ✅ **EDIT Permission**: User has EDIT permission for a module
- ✅ **DELETE Permission**: User has DELETE permission for a module
- ✅ **Combined Permissions**: User has multiple permissions (e.g., CREATE + EDIT)
- ✅ **User ID Input**: Function works with user ID instead of user object

### 2. **Failed Validation - Missing Parameters**
These tests verify that the function returns `false` when required parameters are missing or invalid.

#### Test Cases:
- ❌ **Null User**: User parameter is null or undefined
- ❌ **Missing Organization ID**: organization_id is empty or missing
- ❌ **Missing Module ID**: module_id is 0 or missing
- ❌ **Undefined Permission Type**: permission_type is undefined

### 3. **Failed Validation - User Issues**
These tests verify that the function returns `false` when user-related validations fail.

#### Test Cases:
- ❌ **User Not Found**: User doesn't exist in database
- ❌ **Wrong Organization**: User belongs to different organization
- ❌ **Inactive User Status**: User status is PENDING, DELETED, or CANCELLED
- ❌ **No Active Role**: User has no active role assigned

### 4. **Failed Validation - Role Issues**
These tests verify that the function returns `false` when role-related validations fail.

#### Test Cases:
- ❌ **Role Not Found**: User's role doesn't exist
- ❌ **Inactive Role**: User's role status is inactive
- ❌ **Role Wrong Organization**: Role belongs to different organization

### 5. **Failed Validation - Module Issues**
These tests verify that the function returns `false` when module-related validations fail.

#### Test Cases:
- ❌ **Module Not Found**: Requested module doesn't exist in MOModule table

### 6. **Failed Validation - Permission Issues**
These tests verify that the function returns `false` when permission-related validations fail.

#### Test Cases:
- ❌ **No Permission Record**: No permission record exists for user's role and module
- ❌ **Insufficient Permission**: User lacks the required permission type
- ❌ **Inactive Permission**: Permission record status is inactive

### 7. **Error Handling**
These tests verify that the function gracefully handles database errors and exceptions.

#### Test Cases:
- ❌ **Database Connection Error**: Database query fails
- ❌ **Role Query Error**: Role lookup query fails
- ❌ **Module Query Error**: Module lookup query fails
- ❌ **Permission Query Error**: Permission lookup query fails

### 8. **Bitwise Permission Logic**
These tests verify that the bitwise permission checking works correctly.

#### Test Cases:
- ✅ **Single Permission Check**: Correctly validates individual permissions
- ✅ **Combined Permission Check**: Correctly validates multiple permissions using bitwise OR
- ❌ **Missing Permission in Combination**: Correctly identifies missing permissions in combinations

## Permission Constants Used in Tests

```typescript
ROLE_PERMISSIONS = {
  NONE: 0,
  VIEW: 1,
  CREATE: 2,
  EDIT: 4,
  DELETE: 8
}
```

## Test Data Structure

### Sample User Object:
```typescript
{
  id: 1,
  organization_id: 'org-123',
  user_active_role_id: 2,
  web_user_active_role_id: 3,
  user_status: 'active'
}
```

### Sample Role Object:
```typescript
{
  id: 3,
  role_name: 'Manager',
  role_status: 'active',
  organization_id: 'org-123'
}
```

### Sample Module Object:
```typescript
{
  id: 1,
  module: 'user',
  module_name: 'User Management'
}
```

### Sample Permission Object:
```typescript
{
  id: 1,
  role_id: 3,
  module_id: 1,
  permission: 7, // VIEW + CREATE + EDIT (1 + 2 + 4)
  status: 'active',
  organization_id: 'org-123'
}
```

## Running the Tests

### Prerequisites:
1. Install Jest and related dependencies:
```bash
npm install --save-dev jest @types/jest ts-jest
```

2. Add test script to package.json:
```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage"
  }
}
```

### Commands:
- **Run all tests**: `npm test`
- **Run tests in watch mode**: `npm run test:watch`
- **Run tests with coverage**: `npm run test:coverage`
- **Run specific test file**: `npm test validateModulePermission.test.ts`

## Coverage Goals

- **Function Coverage**: 100% - All functions should be tested
- **Line Coverage**: 95%+ - Most lines should be covered
- **Branch Coverage**: 90%+ - Most conditional branches should be tested
- **Statement Coverage**: 95%+ - Most statements should be executed

## Mock Strategy

The tests use Jest mocks for:
- **User Model**: Mock database queries for user lookup
- **MORole Model**: Mock database queries for role lookup  
- **MOModule Model**: Mock database queries for module lookup
- **MOPermission Model**: Mock database queries for permission lookup

This ensures tests run quickly and don't depend on actual database connections.

## Test File Location

- **Main Test File**: `src/tests/validateModulePermission.test.ts`
- **Setup File**: `src/tests/setup.ts`
- **Configuration**: `jest.config.js`
- **Documentation**: `src/tests/TEST_CASES.md`

## Expected Test Results

When all tests pass, you should see output similar to:
```
✓ Successful permission validation (6 tests)
✓ Failed permission validation - Missing parameters (4 tests)  
✓ Failed permission validation - User issues (4 tests)
✓ Failed permission validation - Role issues (3 tests)
✓ Failed permission validation - Module issues (1 test)
✓ Failed permission validation - Permission issues (3 tests)
✓ Error handling (2 tests)
✓ Bitwise permission checks (1 test)

Total: 24 tests passed
```
