import { Segments, Joi, celebrate } from "celebrate";
export default {
  applyLeave: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        subject: Joi.string().required(),
        request_reason: Joi.string().required(),
        leave_type: Joi.string().allow(null, ""),
        leave_days: Joi.number().allow(null),
        start_date: Joi.date().required(),
        end_date: Joi.date().required(),
        role_id: Joi.number().required().messages({
          'any.required': "Please enter your role."
        }),
        leave_request_type: Joi.number().required().messages({
          'any.required': "Please enter your leave type."
        }),
        duration_type: Joi.string().valid('Days', 'Hours'),
        has_unlimited: Joi.boolean().required(),
        leave_days_obj: Joi.string().allow(null, "")
      }),
    }),
  approveRejectRequest: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        action: Joi.string().required(),
        remark: Joi.string().allow(null, ''),
        request_id: Joi.number().required(),
        leave_deduction_type: Joi.string().allow(null),
        leave_calculation_type: Joi.string().allow(null, ""),
        leave_days: Joi.number().allow(null),
        leave_days_obj: Joi.string().allow(null, "")
      }),
    }),
  createLeaveType: () => celebrate({
    [Segments.BODY]: Joi.object().keys({
      name: Joi.string().required(),
      remark: Joi.string().allow(null, ""),
      status: Joi.string().allow(null, ""),
      has_annual_leave: Joi.boolean().allow(null),
      leave_type_color: Joi.string().allow(null),
      leave_deduction_type: Joi.string().allow(null),
      user_probation_status: Joi.boolean().allow(null),
      leave_period_type: Joi.string().allow(null)
    })
  }),
  createLeavePolicy: () => celebrate({
    [Segments.BODY]: Joi.object().keys({
      name: Joi.string().required(),
      remark: Joi.string().allow(null, ""),
      status: Joi.string().allow(null, ""),
      leave_types: Joi.array().items({
        type_id: Joi.number().required(),
        days: Joi.number().allow(),
        duration_type: Joi.string().valid("Hours", "Days").required(),
        has_unlimited: Joi.boolean().allow(null)
      })
    })
  }),
  cancelLeaveRequest: () => celebrate({
    [Segments.BODY]: Joi.object().keys({
      remark: Joi.string().allow(null, ""),
      request_id: Joi.number().required(),
      leave_calculation_type: Joi.string().allow(null, ""),
      leave_days: Joi.number().allow(null),
      leave_days_obj: Joi.string().allow(null, "")
    })
  }),
  convertLeaveFormat: () => celebrate({
    [Segments.BODY]: Joi.object().keys({
      leave_days_obj: Joi.object().required().messages({
        'any.required': "Please provide leave days object."
      }),
      conversion_type: Joi.string().valid('day_to_hour', 'hour_to_day').required().messages({
        'any.required': "Please specify conversion type.",
        'any.only': "Conversion type must be either 'day_to_hour' or 'hour_to_day'."
      })
    })
  }),
};
