import { Jo<PERSON> } from "celebrate";

export default {
  sendNotification: Joi.object().keys({
    user_ids: Joi.string().allow(null, ""),
    branch_ids: Joi.string().allow(null, ""),
    department_ids: Joi.string().allow(null, ""),
    role_ids: Joi.string().allow(null, ""),
    notification_image: Joi.string().allow(null, ""),
    notification_subject: Joi.string().required(),
    notification_content: Joi.string().required(),
    notification_type: Joi.string().allow(null, "")
  }),
  notification_image: Joi.any().allow(null, ""),
};
