"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface settingAttributes {
  id: number;
  key: string;
  value: string;
  setting_status: string;
  organization_id: string;
  created_by: number;
  updated_by: number;
}

export enum setting_status {
  ACTIVE = "active",
  DELETE = "delete",
}

export class Setting
  extends Model<settingAttributes, never>
  implements settingAttributes {
  id!: number;
  key!: string;
  value!: string;
  setting_status!: string;
  organization_id!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

Setting.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    key: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    value: {
      type: DataTypes.TEXT("long"),
      allowNull: true,
    },
    setting_status: {
      type: DataTypes.ENUM,
      values: Object.values(setting_status),
      allowNull: true,
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_settings",
    modelName: "Setting",
  },
);

Setting.addHook("afterUpdate", async (setting: any) => {
  await addActivity("Setting", "updated", setting);
});

Setting.addHook("afterCreate", async (setting: Setting) => {
  await addActivity("Setting", "created", setting);
});

Setting.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

