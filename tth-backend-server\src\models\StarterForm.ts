"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface starterFormAttributes {
  id: number;
  checklist_id: number;
  user_id: number;
  medical_disability: boolean;
  medical_disability_detail: string;
  kin1_name: string;
  kin1_relation: string;
  kin1_address: string;
  kin1_mobile_number: string;
  kin2_name: string;
  kin2_relation: number;
  kin2_address: string;
  kin2_mobile_number: string;
  professional1_name_contact: string;
  professional1_role_description: number;
  professional1_start_date: number;
  professional1_end_date: number;
  professional2_name_contact: number;
  professional2_role_description: string;
  professional2_start_date: number;
  professional2_end_date: number;
  passport_no: string;
  issued_date: string;
  permit_type: string;
  permit_type_other: string;
  validity: string;
  bank_account_name: string;
  bank_account_number: string;
  bank_sort_code: string;
  bank_society_name: string;
  bank_address: string;
  has_student_or_pg_loan: boolean;
  has_p45_form: boolean;
  hmrc_p45_form: string;
  status: string;
  created_by: number;
  updated_by: number;
}


export class StarterForm
  extends Model<starterFormAttributes, never>
  implements starterFormAttributes {
  id!: number;
  checklist_id!: number;
  user_id!: number;
  medical_disability!: boolean;
  medical_disability_detail!: string;
  kin1_name!: string;
  kin1_relation!: string;
  kin1_address!: string;
  kin1_mobile_number!: string;
  kin2_name!: string;
  kin2_relation!: number;
  kin2_address!: string;
  kin2_mobile_number!: string;
  professional1_name_contact!: string;
  professional1_role_description!: number;
  professional1_start_date!: number;
  professional1_end_date!: number;
  professional2_name_contact!: number;
  professional2_role_description!: string;
  professional2_start_date!: number;
  professional2_end_date!: number;
  passport_no!: string;
  issued_date!: string;
  permit_type!: string;
  permit_type_other!: string;
  validity!: string;
  bank_account_name!: string;
  bank_account_number!: string;
  bank_sort_code!: string;
  bank_society_name!: string;
  bank_address!: string;
  has_student_or_pg_loan!: boolean;
  has_p45_form!: boolean;
  hmrc_p45_form!: string;
  status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

export enum status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export enum permit_type {
  BRITISH_NATIONAL_VISA = "British National (Overseas) Visa",
  CHARITY_WORKER_VISA = "Charity Worker Visa (Temporary Work)",
  CREATIVE_WORKER_VISA = "Creative Worker Visa (Temporary Work)",
  DEPENDENT_VISA = "Dependent Visa",
  ENTREPRENEUR_VISA = "Entrepreneur Visa (Tier 1)",
  EXEMPT_VIGNETTE = "Exempt Vignette",
  FRONTIER_WORKER_PERMIT = "Frontier Worker Permit",
  GLOBAL_TALENT_VISA = "Global Talent Visa",
  GOVT_AUTH_EXCHANGE_VISA = "Government Authorised Exchange Visa (Temporary Work)",
  GRADUATE_TRAINEE_VISA = "Graduate Trainee Visa (Global Business Mobility)",
  GRADUATE_VISA = "Graduate Visa",
  HEALTH_CARE_WORK_VISA = "Health and Care Worker Visa",
  HPI_VISA = "High Potential Individual (HPI) Visa",
  INDIA_YOUNG_PROFESSIONALS_VISA = "India Young Professionals Scheme Visa",
  INNOVATOR_FOUNDER_VISA = "Innovator Founder Visa",
  INTL_AGREEMENT_VISA = "International Agreement Visa (Temporary Work)",
  SPORTSPERSON_VISA = "International Sportsperson Visa",
  INVESTOR_VISA = "Investor Visa (Tier 1)",
  MINISTER_RELIGION_VISA = "Minister of Religion Visa (T2)",
  OTHER = "Other",
  OVERSEAS_VISA = "Overseas Visa",
  OVERSEAS_DOMESTIC_VISA = "Overseas Domestic Worker Visa",
  RELIGIOUS_WORKER_VISA = "Religious Worker Visa (Temporary Work)",
  OVERSEAS_BUSINESS_VISA = "Representative of an Overseas Business Visa",
  SCALE_UP_WORKER_VISA = "Scale-up Worker Visa",
  SCALE_WORKER_VISA = "Scale worker visa",
  SEASONAL_WORKER_VISA = "Seasonal Worker Visa (Temporary Work)",
  SECONDMENT_WORKER_VISA = "Secondment Worker Visa (Global Business Mobility)",
  SENIOR_SPECIALIST_VISA = "Senior or Specialist Worker Visa (Global Business Mobility)",
  SWISS_SERVICE_VISA = "Service Providers from Switzerland Visa",
  SERVICE_SUPPLIER_VISA = "Service Supplier Visa (Global Business Mobility)",
  SKILED_WORK_VISA = "Skilled Worker Visa",
  STARTUP_VISA = "Start-up Visa",
  STUDENT = "Student",
  TIER1 = "Tier-1",
  TIER2 = "Tier-2",
  TURKISH_BUSINESSPERSON_VISA = "Turkish Businessperson Visa",
  TURKISH_WORKER_VISA = "Turkish Worker Visa",
  UK_ANCESTRY_VISA = "UK Ancestry Visa",
  UK_EXPANSION_VISA = "UK Expansion Worker Visa (Global Business Mobility)",
  YOUTH_MOBILITY_VISA = "Youth Mobility Scheme Visa",
}
StarterForm.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    checklist_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    medical_disability: {
      type: DataTypes.BOOLEAN,
    },
    medical_disability_detail: {
      type: DataTypes.STRING(255),
    },
    kin1_name: {
      type: DataTypes.STRING(255),
    },
    kin1_relation: {
      type: DataTypes.STRING(255),
    },
    kin1_address: {
      type: DataTypes.STRING(255),
    },
    kin1_mobile_number: {
      type: DataTypes.STRING,
    },
    kin2_name: {
      type: DataTypes.STRING(255),
    },
    kin2_relation: {
      type: DataTypes.STRING(255),
    },
    kin2_address: {
      type: DataTypes.STRING(255),
    },
    kin2_mobile_number: {
      type: DataTypes.STRING,
    },
    professional1_name_contact: {
      type: DataTypes.STRING(255),
    },
    professional1_role_description: {
      type: DataTypes.TEXT("long"),
    },
    professional1_start_date: {
      type: DataTypes.DATE,
    },
    professional1_end_date: {
      type: DataTypes.DATE,
    },
    professional2_name_contact: {
      type: DataTypes.STRING(255),
    },
    professional2_role_description: {
      type: DataTypes.TEXT("long"),
    },
    professional2_start_date: {
      type: DataTypes.DATE,
    },
    professional2_end_date: {
      type: DataTypes.DATE,
    },
    passport_no: {
      type: DataTypes.STRING,
    },
    issued_date: {
      type: DataTypes.DATE,
    },
    permit_type: {
      type: DataTypes.ENUM,
      values: Object.values(permit_type),
    },
    permit_type_other: {
      type: DataTypes.STRING,
    },
    validity: {
      type: DataTypes.DATE,
    },
    bank_account_name: {
      type: DataTypes.STRING,
    },
    bank_account_number: {
      type: DataTypes.STRING,
    },
    bank_sort_code: {
      type: DataTypes.STRING,
    },
    bank_society_name: {
      type: DataTypes.STRING,
    },
    bank_address: {
      type: DataTypes.STRING,
    },
    has_student_or_pg_loan: {
      type: DataTypes.BOOLEAN,
    },
    has_p45_form: {
      type: DataTypes.BOOLEAN,
    },
    hmrc_p45_form: {
      type: DataTypes.STRING,
    },
    status: {
      type: DataTypes.ENUM,
      values: Object.values(status),
      defaultValue: status.ACTIVE,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_starter_form",
    modelName: "StarterForm",
  },
);

StarterForm.addHook("afterUpdate", async (starterForm: any) => {
  await addActivity("StarterForm", "updated", starterForm);
});

StarterForm.addHook("afterCreate", async (starterForm: StarterForm) => {
  await addActivity("StarterForm", "created", starterForm);
});

StarterForm.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

