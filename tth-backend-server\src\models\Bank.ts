"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { Branch } from "./Branch";

interface bankAttributes {
  id: number;
  bank_name: string;
  bank_account_number: string;
  bank_status: string;
  bank_sort_code: string;
  bank_order: number;
  branch_id: number;
  created_by: number;
  updated_by: number;
}

export enum bank_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export class Bank
  extends Model<bankAttributes, never>
  implements bankAttributes {
  id!: number;
  bank_name!: string;
  bank_account_number!: string;
  bank_status!: string;
  bank_sort_code!: string;
  bank_order!: number;
  branch_id!: number;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

Bank.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    bank_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    bank_account_number: {
      type: DataTypes.STRING,
      defaultValue: false
    },
    bank_sort_code: {
      type: DataTypes.STRING,
      defaultValue: false
    },
    branch_id: {
      type: DataTypes.INTEGER,
      defaultValue: false
    },
    bank_status: {
      type: DataTypes.ENUM,
      values: Object.values(bank_status),
      defaultValue: bank_status.ACTIVE,
    },
    bank_order: {
      type: DataTypes.INTEGER,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_banks",
    modelName: "Bank",
  },
);


Bank.belongsTo(Branch, { foreignKey: "branch_id", as: "branch" });
Branch.hasMany(Bank, { foreignKey: "branch_id", as: "bank_branch" });


// Define hooks for Card model
Bank.addHook("afterUpdate", async (bank: any) => {
  await addActivity("Bank", "updated", bank);
});

Bank.addHook("afterCreate", async (bank: Bank) => {
  await addActivity("Bank", "created", bank);
});

Bank.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
