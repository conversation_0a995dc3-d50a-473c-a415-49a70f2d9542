"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/common";
import { LeaveRestrictionPolicy } from "./LeaveRestrictionPolicy";
import { LeaveApplicationRulesPolicy } from "./LeaveApplicationRulesPolicy";
import { LeaveHolidayWeekendPolicy } from "./LeaveHolidayWeekendPolicy";
import { LeaveApprovalPolicy } from "./LeaveApprovalPolicy";
import { LeaveApprovalMetaPolicy } from "./LeaveApprovalMetaPolicy";

interface LeaveAccuralPolicyAttributes {
    id: number;
    leave_policy_name: string;
    leave_type_id: number;
    leave_calender_year_start_from: Date;
    leave_policy_description: string;
    leave_policy_end_date: string;
    has_leave_policy_default: boolean;
    salary_deduction_hours: number;
    effective_after_count: number;
    effective_after_type: string;
    effective_from_type: string;
    new_employee_leave_entitlement_count: number;
    new_employee_leave_entitlement_type: string;
    new_employee_leave_entitlement_their_type: string;
    has_employee_leave_in_probation: boolean;
    probation_leave_days_count: number;
    leave_policy_accural: boolean;
    stop_policy_accural: boolean;
    stop_policy_accural_limit: number;
    stop_policy_accural_timewise_type: string;
    stop_policy_accural_timewise_value: string;
    leave_accural_based_on_contract: boolean;
    earned_leave_accural_based: boolean;
    restrict_leave_accural_on_emp_exit: boolean;
    pro_rate_accural: string;
    leave_policy_reset: boolean;
    leave_policy_reset_type: string;
    leave_policy_reset_value: string;
    prioritize_encasment_carry_forward: string;
    deduct_encashment_amount: boolean;
    perquisite_value_calculated: string;
    perquisite_value_calculated_formula: string;
    encashment_multiply_by_number: number;
    restrict_encashment_emp_notice_period: boolean;
    allow_for_anytime_encashment: boolean;
    allow_for_anytime_encashment_value: string;
    carry_forward_date: string;
    leave_balance_based_on_emp_contract: boolean;
    status: string;
    created_by: number;
    updated_by: number;
}

export enum status {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DRAFT = "draft",
    DELETED = "deleted"
}

export enum perquisite_value_calculated {
    FIXED_AMOUNT = "fixed_amount",
    FORMULA_BASED = "formula_based",
}

export enum prioritize_encasment_carry_forward {
    ENCASEMENT = "encashment",
    CARRY_FOREWARD = "carry_forward",
}

export enum leave_policy_reset_type {
    YEARLY = "yearly",
    MONTHLY = "monthly",
    QUATERLY = "quarterly",
    HALF_YEARLY = "half_yearly",
    ONE_TIME = "one_time"
}

export enum pro_rate_accural {
    PRO_RATE = "pro_rate",
    NON_PRO_RATE = "non_pro_rate",
}

export enum stop_policy_accural_timewise_type {
    YEARLY = "yearly",
    MONTHLY = "monthly",
    QUATERLY = "quarterly",
    HALF_YEARLY = "half_yearly",
    ONE_TIME = "one_time"
}

export enum new_employee_leave_entitlement_their_type {
    DATE_OF_JOINING = "date_of_joining",
    AFTER_INTERNSHIP_END = "after_internship_end",
    AFTER_PROBATION_END = "after_probation_end",
}

export enum new_employee_leave_entitlement_type {
    DAYS = "days",
    MONTHS = "months",
}

export enum effective_from_type {
    DATE_OF_JOINING = "date_of_joining",
    AFTER_INTERNSHIP_END = "after_internship_end",
    AFTER_PROBATION_END = "after_probation_end",
}

export enum effective_after_type {
    DAYS = "days",
    MONTHS = "months",
}


export class LeaveAccuralPolicy
    extends Model<LeaveAccuralPolicyAttributes, never>
    implements LeaveAccuralPolicyAttributes {
    id!: number;
    leave_policy_name!: string;
    leave_type_id!: number;
    leave_calender_year_start_from!: Date;
    leave_policy_description!: string;
    leave_policy_end_date!: string;
    has_leave_policy_default!: boolean;
    salary_deduction_hours!: number;
    effective_after_count!: number;
    effective_after_type!: string;
    effective_from_type!: string;
    new_employee_leave_entitlement_count!: number;
    new_employee_leave_entitlement_type!: string;
    new_employee_leave_entitlement_their_type!: string;
    has_employee_leave_in_probation!: boolean;
    probation_leave_days_count!: number;
    leave_policy_accural!: boolean;
    stop_policy_accural!: boolean;
    stop_policy_accural_limit!: number;
    stop_policy_accural_timewise_type!: string;
    stop_policy_accural_timewise_value!: string;
    leave_accural_based_on_contract!: boolean;
    earned_leave_accural_based!: boolean;
    restrict_leave_accural_on_emp_exit!: boolean;
    pro_rate_accural!: string;
    leave_policy_reset!: boolean;
    leave_policy_reset_type!: string;
    leave_policy_reset_value!: string;
    prioritize_encasment_carry_forward!: string;
    deduct_encashment_amount!: boolean;
    perquisite_value_calculated!: string;
    perquisite_value_calculated_formula!: string;
    encashment_multiply_by_number!: number;
    restrict_encashment_emp_notice_period!: boolean;
    allow_for_anytime_encashment!: boolean;
    allow_for_anytime_encashment_value!: string;
    carry_forward_date!: string;
    leave_balance_based_on_emp_contract!: boolean;
    status!: status;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

LeaveAccuralPolicy.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        leave_policy_name: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        leave_type_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        leave_calender_year_start_from: {
            type: DataTypes.DATE,
            allowNull: true
        },
        leave_policy_description: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        leave_policy_end_date: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        has_leave_policy_default: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
        },
        salary_deduction_hours: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        effective_after_count: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        effective_after_type: {
            type: DataTypes.ENUM,
            values: Object.values(effective_after_type),
            allowNull: true,
        },
        effective_from_type: {
            type: DataTypes.ENUM,
            values: Object.values(effective_from_type),
            allowNull: true,
        },
        new_employee_leave_entitlement_count: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        new_employee_leave_entitlement_type: {
            type: DataTypes.ENUM,
            values: Object.values(new_employee_leave_entitlement_type),
            allowNull: true,
        },
        new_employee_leave_entitlement_their_type: {
            type: DataTypes.ENUM,
            values: Object.values(new_employee_leave_entitlement_their_type),
            allowNull: true,
        },
        has_employee_leave_in_probation: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        probation_leave_days_count: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        leave_policy_accural: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        stop_policy_accural: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        stop_policy_accural_limit: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        stop_policy_accural_timewise_type: {
            type: DataTypes.ENUM,
            values: Object.values(stop_policy_accural_timewise_type),
            allowNull: true,
        },
        stop_policy_accural_timewise_value: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        leave_accural_based_on_contract: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        earned_leave_accural_based: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        restrict_leave_accural_on_emp_exit: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        pro_rate_accural: {
            type: DataTypes.ENUM,
            values: Object.values(pro_rate_accural),
            allowNull: true,
        },
        leave_policy_reset: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        leave_policy_reset_type: {
            type: DataTypes.ENUM,
            values: Object.values(leave_policy_reset_type),
            allowNull: true,
        },
        leave_policy_reset_value: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        prioritize_encasment_carry_forward: {
            type: DataTypes.ENUM,
            values: Object.values(prioritize_encasment_carry_forward),
            allowNull: true,
        },
        deduct_encashment_amount: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        perquisite_value_calculated: {
            type: DataTypes.ENUM,
            values: Object.values(perquisite_value_calculated),
            allowNull: true,
        },
        perquisite_value_calculated_formula: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        encashment_multiply_by_number: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        restrict_encashment_emp_notice_period: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        allow_for_anytime_encashment: {
            type: DataTypes.BOOLEAN,
            allowNull: true,
        },
        allow_for_anytime_encashment_value: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        carry_forward_date: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        leave_balance_based_on_emp_contract: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
        },
        status: {
            type: DataTypes.ENUM,
            values: Object.values(status),
            defaultValue: status.ACTIVE,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_leave_accural_policy",
        modelName: "LeaveAccuralPolicy",
    },
);

LeaveApplicationRulesPolicy.belongsTo(LeaveAccuralPolicy, { foreignKey: "leave_accural_policy_id", as: "leave_accural_leave_accural_policy_id" });
LeaveAccuralPolicy.hasOne(LeaveApplicationRulesPolicy, { foreignKey: "leave_accural_policy_id", as: "leave_application_rules_form" });

LeaveRestrictionPolicy.belongsTo(LeaveAccuralPolicy, { foreignKey: "leave_accural_policy_id", as: "leave_restriction_leave_accural_policy_id" });
LeaveAccuralPolicy.hasOne(LeaveRestrictionPolicy, { foreignKey: "leave_accural_policy_id", as: "leave_restricition_form" });
// LeaveRestrictionPolicy.belongsTo(LeaveAccuralPolicy, { foreignKey: "club_leave_with_other_leave_policy_id", as: "leave_restriction_club_leave_with_other_leave_policy_id" });
// LeaveAccuralPolicy.hasMany(LeaveRestrictionPolicy, { foreignKey: "club_leave_with_other_leave_policy_id" });
// LeaveRestrictionPolicy.belongsTo(LeaveAccuralPolicy, { foreignKey: "bind_leave_policy_id", as: "leave_restriction_bind_leave_policy_id" });
// LeaveAccuralPolicy.hasMany(LeaveRestrictionPolicy, { foreignKey: "bind_leave_policy_id" });

LeaveHolidayWeekendPolicy.belongsTo(LeaveAccuralPolicy, { foreignKey: "leave_accural_policy_id", as: "leave_holiday_weekend_accural_policy_id" });
LeaveAccuralPolicy.hasOne(LeaveHolidayWeekendPolicy, { foreignKey: "leave_accural_policy_id", as: "holiday_weekend_form" });

LeaveApprovalPolicy.belongsTo(LeaveAccuralPolicy, { foreignKey: "leave_accural_policy_id", as: "leave_approval_accural_policy_id" });
LeaveAccuralPolicy.hasOne(LeaveApprovalPolicy, { foreignKey: "leave_accural_policy_id", as: "leave_approval_form" });

LeaveApprovalMetaPolicy.belongsTo(LeaveAccuralPolicy, { foreignKey: "leave_accural_policy_id", as: "leave_approval_meta_accural_policy_id" });
LeaveAccuralPolicy.hasMany(LeaveApprovalMetaPolicy, { foreignKey: "leave_accural_policy_id", as: "leave_approval_meta_form" });

// Define hooks for this model
LeaveAccuralPolicy.addHook("afterUpdate", async (LeaveAccuralPolicy: any) => {
    await addActivity("LeaveAccuralPolicy", "updated", LeaveAccuralPolicy);
});

LeaveAccuralPolicy.addHook("afterCreate", async (LeaveAccuralPolicy: LeaveAccuralPolicy) => {
    await addActivity("LeaveAccuralPolicy", "created", LeaveAccuralPolicy);
});

LeaveAccuralPolicy.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});


