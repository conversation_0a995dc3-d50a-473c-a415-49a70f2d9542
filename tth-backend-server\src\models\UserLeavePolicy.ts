"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { LeaveAccuralPolicy } from "./LeaveAccuralPolicy";

interface userLeavePolicyAttributes {
  id: number;
  user_id: number;
  leave_accural_policy_id: number;
  user_leave_policy_status: string;
  created_by: number;
  updated_by: number;
}

export class UserLeavePolicy
  extends Model<userLeavePolicyAttributes>
  implements userLeavePolicyAttributes {
  id!: number
  user_id!: number;
  leave_accural_policy_id!: number;
  user_leave_policy_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

export enum user_leave_policy_status {
  ACTIVE = "active",
  INACTIVE = "inactive"
}

UserLeavePolicy.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
    },
    leave_accural_policy_id: {
      type: DataTypes.INTEGER,
    },
    user_leave_policy_status: {
      type: DataTypes.ENUM,
      values: Object.values(user_leave_policy_status),
      defaultValue: user_leave_policy_status.ACTIVE,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER
    }
  },
  {
    sequelize: sequelize,
    tableName: "nv_user_leave_policy",
    modelName: "UserLeavePolicy",
    timestamps: true,
  },
);

UserLeavePolicy.belongsTo(LeaveAccuralPolicy, { foreignKey: "leave_accural_policy_id", as: "user_leave_policy" });
LeaveAccuralPolicy.hasOne(UserLeavePolicy, { foreignKey: "leave_accural_policy_id", as: "user_leave_policy_list" });


UserLeavePolicy.addHook("afterUpdate", async (userLeavePolicy: any) => {
  await addActivity("UserLeavePolicy", "updated", userLeavePolicy);
});

UserLeavePolicy.addHook("afterCreate", async (userLeavePolicy: UserLeavePolicy) => {
  await addActivity("UserLeavePolicy", "created", userLeavePolicy);
});

UserLeavePolicy.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
