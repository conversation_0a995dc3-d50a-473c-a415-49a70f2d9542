import { Router } from "express";
import dsrController from "../../controller/dsr.controller";
import paymentTypeCategoryController from "../../controller/paymentTypeCategory.controller";
import dsrValidator from "../../validators/dsr.validator";
const router: Router = Router();


router.get("/get-payment-type/:branch_id", dsrController.getDsrPaymentType);

router.post("/add", dsrValidator.addDsr(), dsrController.addDsrDetail);

router.put("/update/:dsr_detail_id", dsrValidator.updateDsr(), dsrController.updateDsrDetail);

router.get("/get-dsr-by-id/:dsr_detail_id", dsrController.getDsrById);

router.get("/get-dsr-list", dsrController.getDsrList);

router.delete("/delete/:dsr_detail_id", dsrController.deleteDsrById);

router.get("/get-dsr-request-list", dsrController.getDsrRequestList);

router.get("/get-dsr-request-by-id/:dsr_request_id", dsrController.getDsrRequestById);

router.post("/approve-reject-request", dsrController.approveRejectRequest);

router.post("/get-dsr-report", dsrController.getDsrReport);

router.post("/check-dsr-exist", dsrController.checkDsrExist);

router.get("/get-dsr-activity/:dsr_id", dsrController.getDsrActivity);

router.post("/download-pdf-excel", dsrController.downloadPdfExcel);


router.post("/create-payment-type", dsrValidator.addPaymentType(), paymentTypeCategoryController.createPaymentType);

router.put("/update-payment-type/:payment_type_id", dsrValidator.updatePaymentType(), paymentTypeCategoryController.updatePaymentType);

router.get("/get-one-payment-type/:payment_type_id", paymentTypeCategoryController.getPaymentTypeById);

router.get("/get-all-payment-type", paymentTypeCategoryController.getPaymentTypeList);

router.put("/update-payment-type-order", dsrValidator.updatePaymentTypeOrder(), paymentTypeCategoryController.updatePaymentTypeOrder);

router.post("/create-payment-type-category/:payment_type_id", dsrValidator.addPaymentTypeCategory(), paymentTypeCategoryController.createPaymentTypeCategory);

router.put("/update-payment-type-category/:payment_type_category_id", dsrValidator.updatePaymentTypeCategory(), paymentTypeCategoryController.updatePaymentTypeCategory);

router.put("/update-payment-type-category-order", dsrValidator.updatePaymentTypeCategoryOrder(), paymentTypeCategoryController.updatePaymentTypeCategoryOrder);

router.get("/get-all-payment-type-child", paymentTypeCategoryController.getAllPaymentTypeWithChild);

router.delete("/delete-payment-type/:payment_type_id", paymentTypeCategoryController.deletePaymentType);

router.delete("/delete-payment-type-category/:payment_category_type_id", paymentTypeCategoryController.deletePaymentTypeCategory);

router.post("/add-field-value/:branch_id", dsrValidator.addPaymentTypeCategoryFieldValue(), paymentTypeCategoryController.addValueInCategoryBranch);

router.put("/update-field-value", dsrValidator.updatePaymentTypeCategoryFieldValue(), paymentTypeCategoryController.updateValueInCategoryBranch)

router.get("/get-field-value-by-id/:payment_type_category_branch_id", paymentTypeCategoryController.getValueInCategoryBranchById)

router.get("/get-all-payment-type-child-branch/:branch_id", paymentTypeCategoryController.getAllCategoryBasedOnBranch);

router.put("/make-category-default-active", dsrValidator.makeDafalutActive(), paymentTypeCategoryController.updateStatusPaymentTypeCategory);

router.put("/update-category-value-order", dsrValidator.updatePaymentTypeValueOrder(), paymentTypeCategoryController.updatePaymentTypeValueOrder);

router.delete("/delete-category-branch-value/:payment_type_category_branch_id", paymentTypeCategoryController.deletePaymentTypeCategoryValue)

router.post("/add-wsr", dsrValidator.addWsr(), dsrController.addWsrDetail);

router.put("/update-wsr/:wsr_detail_id", dsrValidator.updateWsr(), dsrController.updateWsrDetail)

router.get("/get-wsr-by-id/:wsr_detail_id", dsrController.getWsrById);

router.get("/get-wsr-list", dsrController.getWsrList);

router.delete("/delete-wsr/:wsr_detail_id", dsrController.deleteWsrById);

router.get("/get-wsr-request-list", dsrController.getWsrRequestList);

router.get("/get-wsr-request-by-id/:wsr_request_id", dsrController.getWsrRequestById);

router.post("/approve-reject-wsr-request", dsrController.approveRejectRequestWsr);

router.post("/check-wsr-exist", dsrController.checkWsrExist);

router.get("/get-wsr-activity/:wsr_id", dsrController.getWsrActivity);


export default router;
