import { Request, Response } from "express";
import { Role, role_status } from '../models/MORole';
import { Op, Transaction } from "sequelize";
import { getPaginatedItems, getPagination } from "../helper/utils";
import { StatusCodes } from "http-status-codes";
import { ROLE_CONSTANT, ROLE_PERMISSIONS } from "../helper/constant";
import { MOPermission, status as permission_status } from "../models/MOPermission";
import { MOModule } from "../models/MOModule";
import { sequelize } from "../models";
import _ from 'lodash'
import { permittedForAdmin, permittedForAdminEnhanced, validateModulePermission } from "../helper/common";

// Create Role
export const createRole = async (req: Request, res: Response) => {
    try {
        const { role_name, parent_role_id, platform, additional_permissions } = req.body;

        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'role', // Role module slug
            ROLE_PERMISSIONS.CREATE
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [ROLE_CONSTANT.SUPER_ADMIN]
        // );

        // User has permission if either check passes
        const hasPermission = checkModulePermission; // || checkAdminPermission;

        if (!hasPermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        // Duplicate validation
        const existingRole = await Role.findOne({
            where: {
                role_name,
                organization_id: req.user.organization_id
            }
        });
        if (existingRole) {
            if (existingRole.role_status === role_status.ACTIVE) {
                return res.status(400).json({
                    status: false,
                    message: res.__("ROLE_ALREADY_EXISTS"),
                });
            } else {
                await Role.setHeaders(req).update(
                    { role_status: role_status.ACTIVE },
                    {
                        where: {
                            id: existingRole.id,
                            organization_id: req.user.organization_id
                        }
                    }
                )
                return res.status(200).json({
                    status: true,
                    message: res.__("ROLE_UPDATED_SUCCESSFULLY")
                });
            }
        }

        const role = await Role.setHeaders(req).create({
            role_name,
            parent_role_id,
            platform,
            additional_permissions: additional_permissions ? JSON.stringify(additional_permissions) : null,
            created_by: req.user.id,
            updated_by: req.user.id,
            role_status: role_status.ACTIVE,
            organization_id: req.user.organization_id
        } as any);

        return res.status(201).json({
            status: true,
            message: res.__("ROLE_CREATED_SUCCESSFULLY"),
            data: role
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Get Roles
export const getRoles = async (req: Request, res: Response) => {
    try {
        const { search, status, page, size, id } = req.query;
        const { limit, offset } = getPagination(Number(page), Number(size));
        
        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'role', // Role module slug
            ROLE_PERMISSIONS.VIEW
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [ROLE_CONSTANT.SUPER_ADMIN]
        // );

        // User has permission if either check passes
        const hasPermission = checkModulePermission; // || checkAdminPermission;

        if (!hasPermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }
        
        const whereClause: any = {
            where: {
                [Op.or]: [
                    { organization_id: req.user.organization_id }
                ]
            },
        };

        if (page && size) {
            whereClause.limit = limit;
            whereClause.offset = offset;
        }

        if (id) {
            whereClause.where.id = id;
        }

        if (search) {
            whereClause.where.role_name = { [Op.like]: `%${search}%` };
        }

        if (status) {
            whereClause.where.role_status = status;
        }

        const Roles = await Role.findAndCountAll(whereClause);
        const response = getPaginatedItems(Number(size), Number(page), Roles?.count);

        const data = {
            data: Roles.rows,
            ...response
        }
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            ...data,
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Update Role
export const updateRole = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { role_name, parent_role_id, platform, additional_permissions } = req.body;

        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'role', // Role module slug
            ROLE_PERMISSIONS.EDIT
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [ROLE_CONSTANT.SUPER_ADMIN]
        // );

        // User has permission if either check passes
        const hasPermission = checkModulePermission; // || checkAdminPermission;

        if (!hasPermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        if (role_name) {
            // Duplicate validation
            const existingRole = await Role.findOne({
                where: {
                    role_name,
                    id: { [Op.ne]: id },
                    organization_id: req.user.organization_id
                }
            });
            if (existingRole && existingRole.role_status != role_status.INACTIVE) {
                return res.status(400).json({
                    status: false,
                    message: res.__("ROLE_ALREADY_EXISTS"),
                });
            }
        }

        const role = await Role.findOne({
            where: {
                id,
                organization_id: req.user.organization_id
            }
        });
        if (!role) {
            return res.status(404).json({
                status: false,
                message: res.__("ROLE_NOT_FOUND"),
            });
        }

        const updateObj: any = {
            role_name: role_name ? role_name : role.role_name,
            parent_role_id: parent_role_id ? parent_role_id : role.parent_role_id,
            platform: platform ? platform : role.platform,
            additional_permissions: additional_permissions !== undefined ?
                (additional_permissions ? JSON.stringify(additional_permissions) : null) :
                role.additional_permissions,
            updated_by: req.user.id,
            role_status: role_status.ACTIVE,
        }

        await Role.setHeaders(req).update(updateObj, {
            where: {
                id: id,
                organization_id: req.user.organization_id
            }
        })

        return res.status(200).json({
            status: true,
            message: res.__("ROLE_UPDATED_SUCCESSFULLY")
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Soft Delete Role (Set status to inactive)
export const deleteRole = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'role', // Role module slug
            ROLE_PERMISSIONS.DELETE
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [ROLE_CONSTANT.SUPER_ADMIN]
        // );

        // User has permission if either check passes
        const hasPermission = checkModulePermission; // || checkAdminPermission;
        if (!hasPermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }


        const role = await Role.findOne({
            where: {
                id,
                organization_id: req.user.organization_id
            }
        });
        if (!role) {
            return res.status(404).json({
                status: false,
                message: res.__("ROLE_NOT_FOUND"),
            });
        }

        await Role.setHeaders(req).update(
            { role_status: role_status.INACTIVE },
            {
                where: {
                    id: id,
                    organization_id: req.user.organization_id
                }
            }
        )

        return res.status(200).json({
            status: true,
            message: res.__("ROLE_SET_INACTIVE_SUCCESSFULLY")
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};


// Ensure view permission is added if create/edit/delete is present
export const setViewPermission = (permission: number) => {
    if (permission & (ROLE_PERMISSIONS.CREATE | ROLE_PERMISSIONS.EDIT | ROLE_PERMISSIONS.DELETE)) {
        permission |= ROLE_PERMISSIONS.VIEW;  // Add view permission if create/edit/delete is present
    }
    return permission;
};

// Format permission response
export const formatPermissions = (permissions: any) => {
    const result: any = {};

    permissions.forEach((perm: any) => {
        const permObj = {
            none: perm.permission == 0,
            view: (perm.permission & ROLE_PERMISSIONS.VIEW) > 0,
            create: (perm.permission & ROLE_PERMISSIONS.CREATE) > 0,
            edit: (perm.permission & ROLE_PERMISSIONS.EDIT) > 0,
            delete: (perm.permission & ROLE_PERMISSIONS.DELETE) > 0,
            partial: perm.partial ? true : false,
            module_name: perm?.module?.module_name,
            type: perm?.module?.type
        };

        result[perm.module.module] = permObj;
    });

    return result;
};

export const createPermission = async (req: Request, res: Response) => {
    const { role_id, module_ids, permission, partial, platform, additional_permissions } = req.body; // Changed module_id to module_ids

    try {
        const createdPermissions = [];

        for (const module_id of module_ids) { // Loop through module_ids
            const existingPermission = await MOPermission.findOne({
                where: {
                    role_id,
                    module_id,
                    organization_id: req.user.organization_id
                }
            });

            if (existingPermission && existingPermission.status == permission_status.ACTIVE) {
                // Skip if permission already exists for this module
                continue;
            }

            // Ensure view permission is added if create/edit/delete is present
            const finalPermission = setViewPermission(permission);

            const newPermission = await MOPermission.setHeaders(req).create({
                role_id,
                module_id,
                permission: finalPermission,
                partial,
                created_by: req.user.id,
                updated_by: req.user.id,
                organization_id: req.user.organization_id
            } as any);

            createdPermissions.push(newPermission);

            // Propagate permissions to other roles in the organization for this module
            const Roles = await Role.findAll({
                where: {
                    id: { [Op.ne]: role_id },
                    organization_id: req.user.organization_id
                }
            });

            if (Roles.length) {
                await Promise.all(Roles.map(async (role) => {
                    const findRolePermission = await MOPermission.findOne({
                        where: {
                            role_id: role.id,
                            module_id: module_id,
                            organization_id: req.user.organization_id
                        }
                    });
                    if (findRolePermission) {
                        await MOPermission.setHeaders(req).update(
                            { permission: findRolePermission.permission },
                            {
                                where: {
                                    role_id: role.id,
                                    module_id: module_id,
                                    organization_id: req.user.organization_id
                                }
                            }
                        )
                    } else {
                        await MOPermission.setHeaders(req).create({
                            role_id: role.id,
                            module_id,
                            permission: 0,
                            partial,
                            created_by: req.user.id,
                            updated_by: req.user.id,
                            organization_id: req.user.organization_id
                        } as any)
                    }
                }))
            }
        }
        const updateObj: any = { platform: platform }
        if (additional_permissions) {
            updateObj.additional_permissions = JSON.stringify(additional_permissions)
        }
        await Role.setHeaders(req).update(
            updateObj,
            {
                where: {
                    id: role_id,
                    organization_id: req.user.organization_id
                }
            }
        )
        // Platform and additional_permissions are now handled in role APIs
        if (createdPermissions.length === 0) {
            return res.status(400).json({
                status: false,
                message: res.__("MODULE_ALREADY_EXISTS_FOR_ROLE")
            });
        }

        return res.status(201).json({
            status: true,
            message: res.__("PERMISSION_CREATED_SUCCESSFULLY"),
            data: createdPermissions
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

export const updatePermission = async (req: Request, res: Response) => {
    const { roles } = req.body; // Array of roles with their module permissions

    try {
        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'role', // Role module slug
            ROLE_PERMISSIONS.EDIT,
            req?.headers?.['platform-type'] as string
        );

        if (!checkModulePermission) {
            return res.status(403).json({
                status: false,
                message: res.__("PERMISSION_DENIED")
            });
        }

        if (!roles || !Array.isArray(roles) || roles.length === 0) {
            return res.status(400).json({
                status: false,
                message: res.__("INVALID_ROLES_DATA")
            });
        }

        const permissionUpdates: any[] = [];
        const permissionCreates: any[] = [];
        let totalProcessed = 0;

        // Process each role and its modules
        for (const roleData of roles) {
            const { role_id, modules } = roleData;

            if (!role_id || !modules || !Array.isArray(modules)) {
                continue;
            }

            // Verify role exists and belongs to organization
            const roleExists = await Role.findOne({
                where: {
                    id: role_id,
                    organization_id: req.user.organization_id
                }
            });

            if (!roleExists) {
                continue; // Skip invalid roles
            }

            // Get all existing permissions for this role
            const existingPermissions = await MOPermission.findAll({
                where: {
                    role_id: role_id,
                    organization_id: req.user.organization_id
                },
                raw: true
            });

            // Create a map for quick lookup
            const existingPermMap = new Map();
            existingPermissions.forEach(perm => {
                existingPermMap.set(perm.module_id, perm);
            });

            // Process each module permission for this role
            for (const moduleData of modules) {
                const { module_id, permission, partial } = moduleData;

                if (module_id === undefined || permission === undefined) {
                    continue;
                }

                const existingPerm = existingPermMap.get(module_id);
                const finalPermission = setViewPermission(permission);

                if (existingPerm) {
                    // Update existing permission
                    permissionUpdates.push({
                        updateData: {
                            permission: finalPermission,
                            partial: partial || 0,
                            status: permission_status.ACTIVE,
                            updated_by: req.user.id
                        },
                        whereClause: {
                            id: existingPerm.id,
                            organization_id: req.user.organization_id
                        }
                    });
                } else {
                    // Create new permission
                    permissionCreates.push({
                        role_id: role_id,
                        module_id: module_id,
                        organization_id: req.user.organization_id,
                        permission: finalPermission,
                        partial: partial || 0,
                        status: permission_status.ACTIVE,
                        created_by: req.user.id,
                        updated_by: req.user.id
                    });
                }
                totalProcessed++;
            }
        }

        // Execute all updates in parallel
        const updatePromises = permissionUpdates.map(update =>
            MOPermission.setHeaders(req).update(update.updateData, {
                where: update.whereClause
            })
        );

        // Execute all creates in parallel
        const createPromises = permissionCreates.length > 0 ?
            [MOPermission.setHeaders(req).bulkCreate(permissionCreates as any)] : [];

        // Wait for all operations to complete
        await Promise.all([...updatePromises, ...createPromises]);

        return res.status(200).json({
            status: true,
            message: res.__("PERMISSIONS_UPDATED_SUCCESSFULLY"),
            data: {
                updated: permissionUpdates.length,
                created: permissionCreates.length,
                total_processed: totalProcessed,
                roles_processed: roles.length
            }
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};


export const getPermissions = async (req: Request, res: Response) => {
    const { id } = req.params;
    const { page, size, search = "", role_id = null, platform = null }: any = req.query;

    try {
        const { limit, offset } = getPagination(Number(page), Number(size));

        const whereObj: any = {
            where: {
                status: permission_status.ACTIVE,
                organization_id: req.user.organization_id
            },
            include: [
                {
                    model: Role,
                    as: 'role',
                    attributes: ['id', 'role_name', 'role_status', 'platform', 'additional_permissions']
                },
                {
                    model: MOModule,
                    as: 'module',
                    attributes: ['id', 'module', 'module_name']
                }
            ],
            raw: true,
            nest: true
        }


        if (id) whereObj.where.id = id
        if (role_id) whereObj.where.role_id = role_id;
        if (platform) {
            // Platform filter should be applied to the role, not permission
            whereObj.include[0].where = { platform: platform };
        }
        if (search) {
            whereObj.include[1].where = {
                [Op.or]: [
                    { module: { [Op.like]: `%${search}%` } },
                    { module_name: { [Op.like]: `%${search}%` } }
                ]
            };
        }

        // if (page && size) {
        //     whereObj.limit = limit;
        //     whereObj.offset = offset;
        // }

        const permissions = await MOPermission.findAndCountAll(whereObj);

        // Format the permissions for returning
        const groupedPermission = _.groupBy(permissions.rows, 'role_id')

        let formattedPermissions = []
        for (const roleId in groupedPermission) {
            const permission = groupedPermission[roleId];
            const formattedPermission = formatPermissions(permission)
            formattedPermissions.push({ role_id: parseInt(roleId), role_name: permission[0]?.role?.role_name, permissions: formattedPermission, platform: permission[0]?.role?.platform, additional_permissions: permission[0]?.role?.additional_permissions ? JSON.parse(permission[0]?.role?.additional_permissions) : {} })
        }
        const response = getPaginatedItems(Number(size), Number(page), formattedPermissions.length);

        if (page && size) {
            formattedPermissions = formattedPermissions.slice(offset, page * size);
        }

        return res.status(200).json({
            status: true,
            message: res.__("PERMISSIONS_FETCHED_SUCCESSFULLY"),
            data: formattedPermissions,
            ...response
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};


export const deletePermission = async (req: Request, res: Response) => {
    const { id } = req.params; // id is role_id

    try {
        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'role', // Role module slug
            ROLE_PERMISSIONS.DELETE,
            req?.headers?.['platform-type'] as string
        );

        if (!checkModulePermission) {
            return res.status(403).json({
                status: false,
                message: res.__("PERMISSION_DENIED")
            });
        }

        // Verify role exists and belongs to organization
        const roleRecord = await Role.findOne({
            where: {
                id: id,
                organization_id: req.user.organization_id
            }
        });

        if (!roleRecord) {
            return res.status(404).json({
                status: false,
                message: res.__("ROLE_NOT_FOUND")
            });
        }

        // Get all permissions for this role to check if any exist
        const existingPermissions = await MOPermission.findAll({
            where: {
                role_id: id,
                organization_id: req.user.organization_id,
                status: permission_status.ACTIVE
            }
        });

        // Delete all module permissions for this role (set status to INACTIVE)
        await MOPermission.setHeaders(req).update(
            {
                status: permission_status.INACTIVE,
                updated_by: req.user.id
            } as any,
            {
                where: {
                    role_id: id,
                    organization_id: req.user.organization_id
                }
            }
        );

        // Clear additional_permissions for this role
        await Role.setHeaders(req).update(
            {
                additional_permissions: null,
                updated_by: req.user.id
            } as any,
            {
                where: {
                    id: id,
                    organization_id: req.user.organization_id
                }
            }
        );

        return res.status(200).json({
            status: true,
            message: res.__("PERMISSIONS_DELETED_SUCCESSFULLY"),
            data: {
                role_id: id,
                permissions_deleted: existingPermissions.length,
                additional_permissions_cleared: true
            }
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};


export const copyPermissions = async (req: Request, res: Response) => {
    const { from_role, to_role } = req.body; // to_role is now an array

    try {
        // Validate input
        if (!Array.isArray(to_role) || to_role.length === 0) {
            return res.status(400).json({
                status: false,
                message: res.__("TO_ROLE_MUST_BE_NON_EMPTY_ARRAY")
            });
        }

        // Step 1: Fetch source role with platform and additional_permissions
        const fromRole = await Role.findOne({
            where: {
                id: from_role,
                organization_id: req.user.organization_id,
                role_status: 'active'
            },
            attributes: ['id', 'role_name', 'platform', 'additional_permissions']
        });

        if (!fromRole) {
            return res.status(404).json({
                status: false,
                message: res.__("FROM_ROLE_NOT_FOUND")
            });
        }

        // Step 2: Verify all destination roles exist
        const toRoles = await Role.findAll({
            where: {
                id: { [Op.in]: to_role },
                organization_id: req.user.organization_id,
                role_status: 'active'
            },
            attributes: ['id', 'role_name']
        });

        if (toRoles.length !== to_role.length) {
            const foundRoleIds = toRoles.map(role => role.id);
            const missingRoleIds = to_role.filter(id => !foundRoleIds.includes(id));
            return res.status(404).json({
                status: false,
                message: res.__("SOME_TO_ROLES_NOT_FOUND"),
                missing_role_ids: missingRoleIds
            });
        }

        // Step 3: Fetch all permissions for the from_role
        const fromPermissions = await MOPermission.findAll({
            where: {
                role_id: from_role,
                organization_id: req.user.organization_id
            }
        });

        if (!fromPermissions.length) {
            return res.status(404).json({
                status: false,
                message: res.__("NO_PERMISSIONS_FOUND_FOR_FROM_ROLE")
            });
        }

        // Step 4: Check for duplicate modules in all destination roles
        const duplicateInfo: any[] = [];

        for (const roleId of to_role) {
            const toPermissions = await MOPermission.findAll({
                where: {
                    role_id: roleId,
                    organization_id: req.user.organization_id
                }
            });

            const toPermissionModules = toPermissions.map((permission: any) => permission.module_id);
            const duplicateModules = fromPermissions.filter((permission: any) =>
                toPermissionModules.includes(permission.module_id)
            );

            if (duplicateModules.length > 0) {
                const roleName = toRoles.find(r => r.id === roleId)?.role_name || `Role ${roleId}`;
                duplicateInfo.push({
                    role_id: roleId,
                    role_name: roleName,
                    duplicate_modules: duplicateModules.map((permission: any) => permission.module_id)
                });
            }
        }

        if (duplicateInfo.length > 0) {
            return res.status(400).json({
                status: false,
                message: res.__("DUPLICATE_MODULES_FOUND_FOR_TO_ROLES"),
                duplicate_info: duplicateInfo
            });
        }

        await sequelize.transaction(async (t: Transaction) => {
            // Step 5: Copy role-level platform and additional_permissions to all destination roles
            for (const roleId of to_role) {
                await Role.setHeaders(req).update(
                    {
                        platform: fromRole.platform,
                        additional_permissions: fromRole.additional_permissions,
                        updated_by: req.user.id
                    },
                    {
                        where: {
                            id: roleId,
                            organization_id: req.user.organization_id
                        },
                        transaction: t
                    }
                );

                // Step 6: Copy all permissions to this role
                for (const permission of fromPermissions) {
                    await MOPermission.create(
                        {
                            role_id: roleId,
                            module_id: permission.module_id,
                            permission: permission.permission,
                            partial: permission.partial,
                            created_by: req.user.id,
                            updated_by: req.user.id,
                            organization_id: req.user.organization_id
                        } as any,
                        { transaction: t }
                    );
                }
            }
        });

        return res.status(200).json({
            status: true,
            message: res.__("PERMISSIONS_AND_ROLE_SETTINGS_COPIED_SUCCESSFULLY")
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// ==================== MODULE CRUD APIs ====================

// Create Module
export const createModule = async (req: Request, res: Response) => {
    try {
        const { module, module_name, type } = req.body;

        // Duplicate validation
        const existingModule = await MOModule.findOne({
            where: { module, organization_id: req.user.organization_id, type }
        });

        if (existingModule) {
            return res.status(400).json({
                status: false,
                message: res.__("MODULE_ALREADY_EXISTS"),
            });
        }

        const newModule = await MOModule.setHeaders(req).create({
            module,
            module_name,
            organization_id: req.user.organization_id,
            type,
            created_by: req.user.id,
            updated_by: req.user.id
        } as any);

        return res.status(201).json({
            status: true,
            message: res.__("MODULE_CREATED_SUCCESSFULLY"),
            data: newModule
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Get Modules
export const getModules = async (req: Request, res: Response) => {
    try {
        const { search, page, size, id, type } = req.query;
        const { limit, offset } = getPagination(Number(page), Number(size));

        const whereClause: any = {
            where: {
                organization_id: req.user.organization_id
            },
        };

        if (page && size) {
            whereClause.limit = limit;
            whereClause.offset = offset;
        }

        if (id) {
            whereClause.where.id = id;
        }

        if (type) {
            whereClause.where.type = type;
        }

        if (search) {
            whereClause.where[Op.or] = [
                { module: { [Op.like]: `%${search}%` } },
                { module_name: { [Op.like]: `%${search}%` } }
            ];
        }

        const modules = await MOModule.findAndCountAll(whereClause);
        const response = getPaginatedItems(Number(size), Number(page), modules?.count);

        const data = {
            data: modules.rows,
            ...response
        }

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            ...data,
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Update Module
export const updateModule = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { module, module_name, type } = req.body;

        // Check if module exists
        const existingModule = await MOModule.findByPk(id);

        if (!existingModule) {
            return res.status(404).json({
                status: false,
                message: res.__("MODULE_NOT_FOUND"),
            });
        }

        // Duplicate validation
        const duplicateModule = await MOModule.findOne({
            where: {
                module,
                organization_id: req.user.organization_id,
                type,
                id: { [Op.ne]: id }
            }
        });

        if (duplicateModule) {
            return res.status(400).json({
                status: false,
                message: res.__("MODULE_ALREADY_EXISTS"),
            });
        }

        const updateObj = {
            module: module ? module : existingModule.module,
            module_name: module_name ? module_name : existingModule.module_name,
            updated_by: req.user.id,
            type: type ? type : existingModule.type
        }

        await MOModule.setHeaders(req).update(updateObj, {
            where: { id: id }
        });

        return res.status(200).json({
            status: true,
            message: res.__("MODULE_UPDATED_SUCCESSFULLY")
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Delete Module
export const deleteModule = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        const module = await MOModule.findByPk(id);

        if (!module) {
            return res.status(404).json({
                status: false,
                message: res.__("MODULE_NOT_FOUND"),
            });
        }

        await MOModule.setHeaders(req).destroy({
            where: { id: id }
        });

        return res.status(200).json({
            status: true,
            message: res.__("MODULE_DELETED_SUCCESSFULLY")
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};


