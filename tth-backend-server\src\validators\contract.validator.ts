import { Joi, Segments, celebrate } from "celebrate";
import { type } from "../models/EmployeeContractCategory";
import { status } from "../models/EmployeeContractTemplate";
import { durationType, wageType } from "../models/ContractType";

export default {
    createFile: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                name: Joi.string().required(),
                type: Joi.string().required().valid(type.DEPT, type.GENERAL),
                content: Joi.string().required(),
                remark: Joi.string().allow(null, ""),
                department_id: Joi.when('type', {
                    is: Joi.string().valid(type.DEPT),
                    then: Joi.number().required(),
                    otherwise: Joi.number().allow(null),
                }),
                template_id: Joi.number().allow(null)
            }),
        }),
    deleteFile: () => celebrate({
        [Segments.BODY]: Joi.object().keys({
            template_ids: Joi.array().items(Joi.number()),
            status: Joi.string().valid(status.DELETED, status.INACTIVE, status.ACTIVE)
        })
    }),
    moveFile: () => celebrate({
        [Segments.BODY]: Joi.object().keys({
            category_id: Joi.number().required(),
        })
    }),
    createContractType: () => celebrate({
        [Segments.BODY]: Joi.object().keys({
            name: Joi.string().required(),
            duration_type: Joi.string().required().valid(durationType.MONTHLY, durationType.WEEKLY),
            wage_type: Joi.string().required().valid(wageType.FIXED, wageType.HOURS),
            working_hours: Joi.number().required(),
            wage_per_hour: Joi.number().required(),
            fixed_types: Joi.string().allow(null, ""),
            remark: Joi.string().allow(null, ""),
            status: Joi.string().allow(null, "")
        })
    }),
    createJobRole: () => celebrate({
        [Segments.BODY]: Joi.object().keys({
            name: Joi.string().required(),
            status: Joi.string().allow(null, "")
        })
    }),
    updateUserContract: () => celebrate({
        [Segments.BODY]: Joi.object().keys({
            general_template: Joi.number().integer().positive().allow(null, ""),
            user_id: Joi.number().integer().positive().required(),
            department_template: Joi.number().integer().positive().required(),
            additional_template: Joi.string().allow(null, ""),
            expire_date: Joi.date().allow(null, ""),
            // start_date: Joi.date().allow(null, ""),
            other: Joi.string().allow(null, ""),
            expire_duration: Joi.string().allow(null, ""),
            fixed_types: Joi.string().allow(null, ""),
            wages_hours: Joi.number().allow(null, ""),
            leave_policy_id: Joi.number().integer().positive().allow(null, ""),
            tips_grade: Joi.string().allow(null, ""),
            probation_length: Joi.number().allow(null, ""),
            working_hours: Joi.number().allow(null, ""),
            duration_type: Joi.string().allow(null, ""),
            wage_type: Joi.string().allow(null, ""),
            contract_remark: Joi.string().allow(null, ""),
            contract_name: Joi.string().allow(null, ""),
            leave_type_id: Joi.number().integer().positive().allow(null, ""),
            leave_days: Joi.number().allow(null, ""),
            leave_remark: Joi.string().allow(null, ""),
            leave_duration_type: Joi.string().allow(null, ""),
            place_of_work: Joi.string().allow(null, ""),
            contract_name_id: Joi.number().allow(null, ""),
            has_holiday_entitlement: Joi.boolean().allow(null, ""),
            holiday_entitlement_remark: Joi.string().allow(null),
            leave_policy_ids: Joi.string().allow(null, "")
        })
    }),
    createContractName: () => celebrate({
        [Segments.BODY]: Joi.object().keys({
            contract_name: Joi.string().required(),
        })
    })
};
