import fs from "fs";
import { UserRole } from "../models/UserRole";
import { Role, role_status } from "../models/Role";
import _ from "lodash";
import { col, fn, Op, QueryTypes, Sequelize, where } from "sequelize";
import { User, user_status } from "../models/User";
import { Branch, branch_status } from "../models/Branch";
import { FORMCONSTANT, NOTIFICATIONCONSTANT, ROLE_CONSTANT, RABBITMQ_QUEUE, EMAIL_ADDRESS, NOTIFICATION_TYPE, REDIRECTION_TYPE, FILE_UPLOAD_CONSTANT } from "./constant";
import { Activity } from "../models/Activity";
import path from "path";
import handlebars from "handlebars";
import { sequelize } from "../models";
import { Setting, setting_status } from "../models/Setting";
import puppeteer from "puppeteer";
import moment from "moment";
import UAParser from 'ua-parser-js'
import { invitation_status, UserInvite } from "../models/UserInvite";
import { encrypt, formatNumber, getPaginatedItems, getPagination, getStartDate } from "./utils";
import { Mail } from "../models/Mail";
import { PDFDocument } from "pdf-lib";
import sharp from "sharp";
import { rgb } from "pdf-lib";
import { crc32 } from "crc";
import mmm from 'mmmagic'
import CryptoJS from 'crypto-js'
import { Item, item_external_location, item_IEC, item_status, item_type } from "../models/Item";
import { ItemOwner } from "../models/ItemOwner";
import {
  document_category_branch_status,
  DocumentCategoryBranch,
} from "../models/DocumentCategoryBranch";
import { category_status, DocumentCategory } from "../models/DocumentCategory";
import { document_category_department_status, DocumentCategoryDepartment } from "../models/DocumentCategoryDepartment";
import { contract_status, UserEmploymentContract } from "../models/UserEmployementContract";
import { document_category_item_track_status, DocumentCategoryItemTrack } from "../models/DocumentCategoryItemTrack";
import { HealthSafetyCategoryItem, status } from "../models/HealthSafetyCategoryItem";
import { UserMeta, wageType } from "../models/UserMeta";
import { LeaveTypeModel, leave_period_type } from "../models/LeaveType";
import { EmpContractTemplateVersion } from "../models/EmployeeContractTemplateVersion";
import { HrmcForm } from "../models/HrmcForm";
import { check_list_status, UserCheckList } from "../models/UserCheckList";
import { wsr_detail_status, WsrDetail } from "../models/WsrDetail";
import { ExpenseDetail } from "../models/ExpenseDetail";
import { EmpContractTemplate } from "../models/EmployeeContractTemplate";
import { Geo } from "../models/Geo";
import { dsr_detail_status, DsrDetail } from "../models/DsrDetail";
import { payment_type_category_status, PaymentTypeCategory } from "../models/PaymentTypeCategory";
import { report_filter_status, ReportFilter } from "../models/ReportFilter";
import { payment_type_status, payment_type_usage, PaymentType } from "../models/PaymentType";
import { DashboardModel, model_status } from "../models/DashboardModel";
import { Department, department_status } from "../models/Department";
import { Forecast, forecast_status } from "../models/Forecast";
import { forecast_budget_data_status, forecast_category_status, ForecastBugdetData } from "../models/ForecastBugdetData";
import { forecast_category_type, forecast_type } from "../models/ForecastMeta";
import { request_status, UserRequest } from "../models/UserRequest";
import { UserBranch } from "../models/UserBranch";
import { LeaveApplicationRulesPolicy } from "../models/LeaveApplicationRulesPolicy";
import { LeaveAccuralPolicy } from "../models/LeaveAccuralPolicy";
import { LeaveRestrictionPolicy } from "../models/LeaveRestrictionPolicy";
import { LeaveHolidayWeekendPolicy } from "../models/LeaveHolidayWeekendPolicy";
import { LeaveApprovalPolicy } from "../models/LeaveApprovalPolicy";
import { user_leave_policy_status, UserLeavePolicy } from "../models/UserLeavePolicy";
import { ContractNameModel } from "../models/ContractNameModel";
import { user_holiday_policy_status, UserHolidayPolicy } from "../models/UserHolidayPolicy";
import { holiday_policy_status, HolidayPolicy } from "../models/HolidayPolicy";
import { approval_type, LeaveApprovalMetaPolicy } from "../models/LeaveApprovalMetaPolicy";
import { holiday_type_status, HolidayType } from "../models/HolidayType";
import { Resignation, resignation_status } from "../models/Resignation";
import { user_leave_policy_history_status, UserLeavePolicyHistory } from "../models/UserLeavePolicyHistory";
import { forecast_budget_data_history_status } from "../models/ForecastBugdetDataHistory";
import { user_weekday_status, UserWeekDay } from "../models/UserWeekDay";
import { payment_type_category_branch_status } from "../models/PaymentTypeCategoryBranch";
import { document_category_item_status } from "../models/DocumentCategoryItem";
import { DocumentCategoryItem } from "../models/DocumentCategoryItem";
import rabbitmqPublisher from "../rabbitmq/rabbitmq";
import { deleteFileFromBucket, s3 } from './upload.service';
import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { VersionSetting } from "../models/VersionSetting";
import { Role as MORole } from "../models/MORole";
import { MOPermission } from "../models/MOPermission";
import { MOModule } from "../models/MOModule";
import { ChangeRequest, change_request_status } from "../models/ChangeRequest";
import { ChangeRequestHistory, change_request_history_status } from "../models/ChangeRequestHistory";
import { ChangeRequestSettings } from "../models/ChangeRequestSettings";
import { RightToWorkCheckList } from "../models/RightToWorkCheckList";
import { StarterForm } from "../models/StarterForm";

/** read HTML templates for Sending EMails */
const readHTMLFile = function (path: any, cb: any) {
  // read file
  fs.readFile(path, "utf-8", function (err, data) {
    if (err) {
      console.log(err);
      throw err;
    } else {
      cb(null, data);
    }
  });
};

const permittedForAdmin = async (user_id: number, roleArray?: any) => {
  try {
    const roleData: any = await UserRole.findAll({
      where: {
        user_id: user_id,
      },
      include: {
        model: Role,
        as: "role",
        attributes: ["role_name"],
        where: {
          role_name: {
            [Op.in]: roleArray
          },
        },
      },
      raw: true,
      nest: true,
    });
    if (roleData.length > 0) {
      if (
        _.some(roleData, { role_name: ROLE_CONSTANT.DIRECTOR }) ||
        _.some(roleData, { role_name: ROLE_CONSTANT.HR })
      ) {
        const userStatus = await User.findOne({
          where: { id: user_id, user_status: { [Op.notIn]: [user_status.PENDING, user_status.DELETED, user_status.CANCELLED] } },
        });
        if (userStatus) {
          return true;
        }
      } else {
        return true;
      }
    } else {
      return false;
    }
  } catch (error) {
    console.log("error", error);
  }
};

/**
 * Check admin permissions using MORole model (new implementation)
 * @param user_id - User ID to check permissions for
 * @param organization_id - Organization ID for filtering
 * @param roleArray - Array of role names to check against
 * @returns Promise<boolean> - true if user has admin permission, false otherwise
 */
const permittedForAdminMO = async (user_id: number, organization_id: string, roleArray?: any): Promise<boolean | undefined> => {
  try {
    if (!user_id || !organization_id) {
      console.log("permittedForAdminMO: Missing required parameters");
      return false;
    }

    // Get user details with organization validation
    const userDetails = await User.findOne({
      where: {
        id: user_id,
        organization_id: organization_id
      },
      attributes: ['id', 'user_role_id', 'user_active_role_id', 'web_user_active_role_id', 'organization_id']
    });

    if (!userDetails) {
      console.log("permittedForAdminMO: User not found or not in organization");
      return false;
    }

    // Determine which role to use - prioritize user_role_id (MORole), then fallback to old roles
    const active_role_id = userDetails.user_role_id || userDetails.web_user_active_role_id || userDetails.user_active_role_id;

    if (!active_role_id) {
      console.log("permittedForAdminMO: User has no active role");
      return false;
    }

    // Check if using MORole (user_role_id exists)
    if (userDetails.user_role_id) {
      // Check if the role exists and is active in the organization
      const userRole = await MORole.findOne({
        where: {
          id: active_role_id,
          organization_id: organization_id,
          role_status: 'active'
        },
        attributes: ['id', 'role_name']
      });

      if (!userRole) {
        console.log("permittedForAdminMO: MORole not found or inactive");
        return false;
      }

      // Check if role name is in the allowed array
      if (roleArray && roleArray.length > 0) {
        if (!_.includes(roleArray, userRole.role_name)) {
          console.log("permittedForAdminMO: User role not in allowed roles array");
          return false;
        }
      }

      // Additional checks for specific roles (similar to old implementation)
      if (
        userRole.role_name === ROLE_CONSTANT.DIRECTOR ||
        userRole.role_name === ROLE_CONSTANT.HR
      ) {
        // These roles need additional validation
        return true;
      } else {
        return true;
      }
    } else {
      // Fallback to old role checking logic if user_role_id is not set
      console.log("permittedForAdminMO: Falling back to old role checking");
      return await permittedForAdmin(user_id, roleArray);
    }

  } catch (error) {
    console.log("permittedForAdminMO error:", error);
    return false;
  }
};

const checkBranchUser = async (branch_id: number, user_id: number) => {
  try {
    const findUserBranch = await User.findOne({
      where: {
        branch_id: branch_id,
        id: user_id,
        user_status: { [Op.not]: [user_status.PENDING, user_status.DELETED, user_status.CANCELLED] },
      },
    });
    if (findUserBranch) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.log("error", error);
  }
};

const roleName = async (role_ids: any) => {
  try {
    const roles = await Role.findAll({
      where: {
        id: { [Op.in]: role_ids },
      },
      attributes: ["id", "role_name"],
    });
    const roleMap = _.keyBy(roles, "id");
    return role_ids.map((id: string | number) => roleMap[id]?.role_name);
  } catch (error) {
    console.log("error", error);
  }
};

const getRoleName = async (role_id: any) => {
  try {
    const roleData = await Role.findAll({
      where: {
        id: role_id,
      },
      attributes: ["id", "role_name"],
      raw: true,
      nest: true,
    });

    return roleData ? roleData : {};
  } catch (error) {
    console.log("error", error);
  }
};

const findParentUserRole = async (user_id: any) => {
  try {
    const findUserDetail = await User.findOne({ where: { id: user_id } });
    if (!findUserDetail) {
      return false;
    }
    const getUserParentRole: any = await Role.findOne({
      where: {
        id: findUserDetail.web_user_active_role_id,
      },
    });
    const whereObj: any = {
      role_status: role_status.ACTIVE,
      id: {
        [Op.not]: findUserDetail.web_user_active_role_id, // Exclude req.user.active role id
      },
    };

    // Add condition when current role id is not 1
    if (findUserDetail.web_user_active_role_id !== 1) {
      whereObj.parent_role_id = {
        [Op.gt]: getUserParentRole.parent_role_id,
      };
    }

    const roleList = await Role.findAll({
      where: whereObj,
      attributes: ["role_name", "id"],
    });

    return roleList.length > 0
      ? _.map(roleList, (role: any) => {
        return role.id;
      })
      : [];
  } catch (error) {
    console.log("error", error);
  }
};

const userCreatePermission = async (user_id: any) => {
  try {
    const findUserCurrentRole = await User.findOne({
      where: { id: user_id },
      raw: true,
    });
    if (!findUserCurrentRole) {
      return false;
    }
    const getRoleName = await Role.findOne({
      where: { id: findUserCurrentRole?.web_user_active_role_id },
      attributes: ["role_name"],
      raw: true,
    });
    const adminSideUser = [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
      ROLE_CONSTANT.BRANCH_MANAGER,
      ROLE_CONSTANT.HOTEL_MANAGER,
    ];
    if (_.includes(adminSideUser, getRoleName?.role_name)) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.log("error", error);
  }
};

const deleteFiles = async (fileData: any) => {
  _.forEach(fileData, (files: any) => {
    _.forEach(files, async (file: { originalname: any; path: any }) => {
      try {
        await fs.unlinkSync(file.path);
        console.log(`File ${file.originalname} deleted successfully`);
      } catch (err) {
        console.error(`Error deleting file ${file.originalname}:`, err);
      }
    });
  });
};

const findUserExist = async (user_id: any, organization_id: any) => {
  try {
    const findUser = await User.findOne({
      attributes: ['id', 'keycloak_auth_id'],
      where: {
        id: user_id,
        user_status: {
          [Op.not]: [user_status.DELETED, user_status.CANCELLED],
        },
        organization_id: organization_id
      },
      raw: true
    });
    return findUser;
  } catch (error) {
    console.log("error", error);
    return false;
  }
};

const permissionForAdmin = async (role_id: any) => {
  try {
    const getRoleName = await Role.findOne({
      where: { id: role_id },
      attributes: ["role_name"],
      raw: true,
    });
    const adminSideUser = [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
      ROLE_CONSTANT.AREA_MANAGER,
      ROLE_CONSTANT.BRANCH_MANAGER,
      ROLE_CONSTANT.HOTEL_MANAGER,
    ];
    if (_.includes(adminSideUser, getRoleName?.role_name)) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.log("error", error);
    return false;
  }
};

/**
 * Helper function to determine platform from request headers
 * @param req - Request object with headers
 * @returns Platform number (1=web, 2=app)
 */
const getPlatformFromRequest = (req: any): number => {
  const platformType = req?.headers?.["platform-type"];
  return platformType == "ios" || platformType == "android" ? 2 : 1; // Default to web (1) if not specified
};

/**
 * Validate user permissions using MORole, MOPermission, and MOModule models
 * @param user - User object or user ID
 * @param organization_id - Organization ID for filtering
 * @param module_slug - Module slug (e.g., 'dashboard', 'branch', 'staff') to check permission for
 * @param permission_type - Permission type (VIEW=1, CREATE=2, EDIT=4, DELETE=8) from ROLE_PERMISSIONS constant
 * @param platform - Platform type (1=web, 2=app, 3=both) - optional, defaults to web
 * @returns Promise<boolean> - true if user has permission, false otherwise
 */
const validateModulePermission = async (
  user: any,
  organization_id: string,
  module_slug: string,
  permission_type: number,
  platform: any = "web" // Default to web platform
): Promise<boolean> => {
  try {
    platform = getPlatformFromRequest({ headers: { 'platform-type': platform } })
    // Extract user ID if user object is passed
    const user_id = typeof user === 'object' ? user.id : user;

    if (!user_id || !organization_id || !module_slug || permission_type === undefined) {
      console.log("validateModulePermission: Missing required parameters");
      return false;
    }

    // Get user details with organization validation
    const userDetails = await User.findOne({
      where: {
        id: user_id,
        organization_id: organization_id,
        user_status: {
          [Op.notIn]: [user_status.PENDING, user_status.DELETED, user_status.CANCELLED]
        }
      },
      attributes: ['id', 'user_role_id', 'user_active_role_id', 'web_user_active_role_id', 'organization_id']
    });

    if (!userDetails) {
      console.log("validateModulePermission: User not found or not in organization");
      return false;
    }

    // Prioritize user_role_id (MORole), then fallback to old role system
    const active_role_id = platform == 1 && userDetails.user_role_id ? userDetails.user_role_id :
      platform === 1 ? userDetails.web_user_active_role_id : userDetails.user_active_role_id;

    if (!active_role_id) {
      console.log("validateModulePermission: User has no active role");
      return false;
    }

    // If using user_role_id (MORole system), proceed with MORole validation
    if (userDetails.user_role_id && platform == 1) {
      // Check if the role exists and is active in the organization, and get platform info
      const userRole = await MORole.findOne({
        where: {
          id: active_role_id,
          organization_id: organization_id,
          role_status: 'active'
        },
        attributes: ['id', 'role_name', 'platform', 'additional_permissions']
      });

      if (!userRole) {
        console.log("validateModulePermission: User MORole not found or inactive");
        return false;
      }

      // Check if the role has access to the requested platform
      const rolePlatform = userRole.platform;
      if (rolePlatform !== 3 && rolePlatform !== platform) {
        console.log(`validateModulePermission: Role platform ${rolePlatform} doesn't match requested platform ${platform}`);
        return false;
      }

      // Check if the module exists and get its ID
      const moduleData = await MOModule.findOne({
        where: {
          module: module_slug,
          organization_id: organization_id
        },
        attributes: ['id', 'module', 'module_name']
      });

      if (!moduleData) {
        console.log(`validateModulePermission: Module '${module_slug}' not found`);
        return false;
      }

      // Get user's permission for the specific module (platform is now checked at role level)
      const userPermission = await MOPermission.findOne({
        where: {
          role_id: active_role_id,
          module_id: moduleData.id,
          organization_id: organization_id,
          status: 'active'
        }
      });

      if (!userPermission) {
        console.log("validateModulePermission: No permission record found for user role, module, and platform");
        return false;
      }

      // Check if user has the required permission using bitwise AND
      const hasPermission = (userPermission.permission & permission_type) > 0;

      if (!hasPermission) {
        console.log(`validateModulePermission: User lacks required permission. Required: ${permission_type}, User has: ${userPermission.permission}`);
        return false;
      }

      return true;
    } else {
      // Fallback to old role system validation
      console.log("validateModulePermission: Falling back to old role system");
      const status = await permissionForAdmin(active_role_id)
      return status || false; // You can implement old system fallback here if needed
    }

  } catch (error) {
    console.log("validateModulePermission error:", error);
    return false;
  }
};

/**
 * Enhanced admin permission check that works with both old and new role systems
 * This function combines the old permittedForAdmin and new permittedForAdminMO functions
 * @param user_id - User ID to check permissions for
 * @param organization_id - Organization ID for filtering
 * @param roleArray - Array of role names to check against
 * @returns Promise<boolean> - true if user has admin permission, false otherwise
 */
const permittedForAdminEnhanced = async (user_id: number, organization_id: string, roleArray?: any): Promise<boolean> => {
  try {
    // First try the new MORole system
    const newSystemResult = await permittedForAdminMO(user_id, organization_id, roleArray);

    // If new system returns true, user has permission
    if (newSystemResult) {
      return true;
    }

    // If new system returns false, fallback to old system for backward compatibility
    // const oldSystemResult = await permittedForAdmin(user_id, roleArray);

    return false // oldSystemResult || false;
  } catch (error) {
    console.log("permittedForAdminEnhanced error:", error);
    return false;
  }
};

const addActivity = async (
  activity_table: string,
  activity_action: string,
  data: any,
) => {
  try {
    await Activity.create({
      activity_table: activity_table,
      activity_action: activity_action,
      reference_id: data?.id,
      previous_data: JSON.stringify(data?._previousDataValues),
      new_data: JSON.stringify(data?.dataValues),
      organization_id: data.organization_id ? data.organization_id : null,
      created_by: data.created_by,
      updated_by: data.updated_by,
    } as any);

    return true;
  } catch (error) {
    console.log("error", error);
  }
};

let globalBrowser: any = null;
const templateCache: Map<string, string> = new Map();
const orgDataCache: Map<string, { name: string; logo: string }> = new Map();

// Initialize browser instance on startup
const initializeBrowser = async (): Promise<any> => {
  if (!globalBrowser) {
    const puppeteer = await import("puppeteer");
    globalBrowser = await puppeteer.default.launch({
      headless: true,
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-first-run",
        "--no-zygote",
        "--disable-gpu",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding",
      ],
      timeout: 30000,
    });
  }
  return globalBrowser;
};

const generateEmployeeContract = async (
  data: any,
  template: any,
): Promise<Buffer> => {
  return new Promise((resolve, reject) => {
    readHTMLFile(
      path.join(__dirname, `../../src/email_templates/${template}.html`),
      async function (err: any, html: any) {
        try {
          if (err) {
            console.error("Error reading HTML template:", err);
            reject(err);
            return;
          }

          const compiledTemplate = handlebars.compile(html);
          const htmlToSend = compiledTemplate(data);

          const browser = await puppeteer.launch({
            args: ["--no-sandbox", "--disable-setuid-sandbox"],
          });
          const page = await browser.newPage();
          await page.setContent(htmlToSend);

          // Generate the PDF as a buffer
          const pdfBuffer = await page.pdf({
            format: "a4",
            printBackground: true,
            margin: { top: "40px", bottom: "40px" },
          });

          // Close the browser
          await browser.close();

          console.log("PDF generated successfully!");
          resolve(pdfBuffer);
        } catch (e) {
          console.log("error", e);
          reject(e);
        }
      },
    );
  });
};

/** * Generate employee contract and store it in S3 * @param fileName - The name to give the generated PDF file * @param data - The data to use for template compilation * @param template - The template to use for generating the PDF * @param organization_id - The organization ID for storage path * @param user_id - The user ID for storage path * @returns Item ID and location of the stored contract */
export const generateS3EmployeeContract = async (
  fileName: any,
  data: any,
  template: any,
  organization_id: string,
  user_id: string
) => {
  try {

    if (!isNaN(fileName)) {
      const getItem: any = await Item.findOne({ where: { id: fileName }, raw: true })
      fileName = getItem?.item_name
    }

    const destKey = FILE_UPLOAD_CONSTANT.ONBOARDING_FILES.destinationPath(
      organization_id,
      user_id,
      fileName
    );
    // Generate the PDF as a buffer
    const fileBuffer = await generateEmployeeContract(data, template);

    const iFileBuffer = Buffer.from(fileBuffer.toString("base64").split(";base64,").pop() || "")

    const fileExt = path.extname(fileName);
    const fileSize = fileBuffer.length;
    const fileHash: any = await getHash(iFileBuffer, {
      originalname: fileName,
      size: fileSize,
    }, 'application/pdf');

    // Create item record
    const saveItem: any = {
      item_type: "pdf",
      item_name: fileName,
      item_hash: fileHash?.hash,
      item_mime_type: "application/pdf",
      item_extension: fileExt,
      item_size: fileSize,
      item_IEC: item_IEC.B,
      item_status: item_status.ACTIVE,
      item_external_location: item_external_location.NO,
      item_location: destKey,
      item_organization_id: organization_id,
    };

    // Save file info to database
    const item = await Item.create(saveItem);

    // Upload file to S3
    const s3BucketName = process.env.NODE_ENV!;

    const s3Params = {
      Bucket: s3BucketName,
      Key: destKey,
      Body: fileBuffer,
      ContentType: "application/pdf",
    };

    await s3.send(new PutObjectCommand(s3Params));

    return {
      success: true,
      item_id: item.id,
      path: destKey
    };
  } catch (error) {
    console.error("Error generating S3 employee contract:", error);
    return {
      success: false,
      error
    };
  }
};

const getAdminStaffs = async (
  branch_id?: any,
  login_user_id?: any,
  role_id: any = null,
  organization_id?: any,
) => {
  const roleWhere = role_id ? `AND nv_user_roles.role_id != ${role_id}` : "";
  const findUsers =
    (await User.findAll({
      attributes: ['id', 'appToken', 'webAppToken', 'user_email'],
      where: {
        user_status: {
          [Op.not]: [
            user_status.DELETED,
            user_status.PENDING,
            user_status.CANCELLED,
          ],
        },
        id: {
          [Op.in]: [
            sequelize.literal(
              `(SELECT nv_user_roles.user_id from nv_user_roles where nv_user_roles.role_id IN (SELECT id from nv_roles where role_name IN ('${ROLE_CONSTANT.SUPER_ADMIN}','${ROLE_CONSTANT.ADMIN}','${ROLE_CONSTANT.DIRECTOR}','${ROLE_CONSTANT.HR}','${ROLE_CONSTANT.AREA_MANAGER}','${ROLE_CONSTANT.BRANCH_MANAGER}','${ROLE_CONSTANT.HOTEL_MANAGER}')
              AND (
                (nv_roles.role_name != '${ROLE_CONSTANT.BRANCH_MANAGER}' AND nv_roles.role_name != '${ROLE_CONSTANT.HOTEL_MANAGER}') OR
                User.branch_id = ${branch_id}
              ) AND User.id != ${login_user_id} ${roleWhere}
              ))`,
            ),
          ],
        },
        organization_id: organization_id,
      },
      raw: true,
      group: ["id"],
    })) || [];
  return findUsers;
};

const moveFile = async (
  sourcePath: string,
  destinationPath: string,
  fileName: string,
) => {
  try {
    if (!fs.existsSync(destinationPath)) {
      fs.mkdirSync(destinationPath, { recursive: true });
    }
    fs.copyFile(
      sourcePath,
      path.resolve(destinationPath, fileName),
      (error) => {
        if (error) {
          console.log("error", error);
        }
        if (fs.existsSync(sourcePath)) {
          fs.unlinkSync(sourcePath);
        }
      },
    );

    return true;
  } catch (error) {
    console.log("error", error);
    return false;
  }
};

const checkUserProfileComplete = async (user_id: any) => {
  try {
    const getUserDetail = await sequelize.query(
      `SELECT * FROM nv_users
      WHERE
      (
          (
              user_gender IS NOT NULL AND user_gender != ''
          ) OR(
              user_gender_other IS NOT NULL AND user_gender_other != ''
          )
      ) AND(
          (
              marital_status IS NOT NULL AND marital_status != ''
          ) OR(
              marital_status_other IS NOT NULL AND marital_status_other != ''
          )
      ) AND(
          user_first_name IS NOT NULL AND user_first_name != ''
      ) AND(
          user_last_name IS NOT NULL AND user_last_name != ''
      ) AND(
          user_email IS NOT NULL AND user_email != ''
      ) AND(
          address_line1 IS NOT NULL AND address_line1 != ''
      ) AND(
          country IS NOT NULL AND country != ''
      ) AND(
          pin_code IS NOT NULL AND pin_code != ''
      ) AND(
          user_phone_number IS NOT NULL AND user_phone_number != ''
      ) AND(
          user_signature IS NOT NULL AND user_signature != ''
      ) AND(
        emergency_contact IS NOT NULL AND emergency_contact != ''
      ) AND user_joining_date IS NOT NULL AND date_of_birth IS NOT NULL AND id = ${user_id}`,
      { type: QueryTypes.SELECT },
    );
    if (getUserDetail && getUserDetail[0]) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.log("error", error);
    return false;
  }
};
const getGeneralSettingObj = async (organization_id: any = null) => {
  try {
    const getSettingList = await Setting.findAll({
      where: {
        setting_status: setting_status.ACTIVE,
        organization_id: organization_id,
      },
      raw: true,
    });

    if (getSettingList.length > 0) {
      const settingObj: any = {};
      for (const key of getSettingList) {
        settingObj[key.key] = key.value;

        if (key.key == "brand_logo") {
          // Get brand_logo from Item table if it's an item_id
          const item = await Item.findOne({
            where: { id: key.value },
            attributes: ["item_location"],
            raw: true,
          });

          settingObj["brand_logo_link"] = item?.item_location
            ? `${global.config.API_BASE_URL}${item.item_location}`
            : "";
        }
        if (key.key == 'leave_period_type') {
          settingObj["leave_period_type"] = key.value ? key.value : 'day';
        }
        if (key.key == "employer_sign") {
          // Get employer_sign from Item table if it's an item_id
          const item = await Item.findOne({
            where: { id: key.value },
            attributes: ["item_location"],
            raw: true,
          });

          settingObj["employer_sign"] = item?.item_location
            ? `${global.config.API_BASE_URL}${item.item_location}`
            : "";
        }
      }
      return settingObj;
    } else {
      return {};
    }
  } catch (error) {
    console.log("error", error);
  }
};

const getBranchSettingObj = async (branch_id: any) => {
  try {
    const getSettingList = await Branch.findOne({
      attributes: ['id', 'branch_sign', 'branch_employer_name', 'branch_heading_employer_name', 'branch_heading_name', 'branch_heading_work_place', 'branch_work_place', 'registration_number'],
      where: { id: branch_id, branch_status: branch_status.ACTIVE }, raw: true
    });
    if (getSettingList) {
      const settingObj: any = {};
      if (
        getSettingList.branch_sign ||
        getSettingList.branch_employer_name ||
        getSettingList.branch_heading_employer_name ||
        getSettingList.branch_heading_name ||
        getSettingList.branch_heading_work_place ||
        getSettingList.branch_work_place
      ) {
        // Get branch_sign from Item table if it's an item_id
        if (getSettingList.branch_sign) {
          const item = await Item.findOne({
            where: { id: getSettingList.branch_sign },
            attributes: ["item_location"],
            raw: true,
          });

          settingObj.branch_sign = item?.item_location
            ? `${global.config.API_BASE_URL}${item.item_location}`
            : null;
        } else {
          settingObj.branch_sign = null;
        }

        settingObj.branch_employer_name =
          getSettingList.branch_employer_name || null;
        settingObj.branch_heading_employer_name =
          getSettingList.branch_heading_employer_name || null;
        settingObj.branch_heading_name =
          getSettingList.branch_heading_name || null;
        settingObj.branch_heading_work_place =
          getSettingList.branch_heading_work_place || null;
        settingObj.branch_work_place = getSettingList.branch_work_place || null;
        settingObj.registration_number =
          getSettingList.registration_number || null;
      }
      return settingObj;
    } else {
      return {};
    }
  } catch (error) {
    console.log("error", error);
  }
};

const getUserDeviceId = async (users: any) => {
  try {
    const deviceIds = users.map((user: any) => {
      if (user.appToken && !user.webAppToken) {
        return user.appToken;
      } else if (!user.appToken && user.webAppToken) {
        return user.webAppToken;
      } else if ((user.webAppToken, user.appToken)) {
        return [user.webAppToken, user.appToken];
      } else {
        return [];
      }
    });
    return _.flatten(deviceIds);
  } catch (error) {
    console.log("error", error);
    return false;
  }
};

const createNotification = async (
  users: any,
  req: any,
  notification_type: any,
  notification_content: any,
  notification_subject: any,
  redirection_type?: any,
  reference_id?: any,
  redirection_object?: any,
  notification_meta_id?: any,
) => {
  try {
    const notificationObj: any = {
      notification_image: redirection_type != REDIRECTION_TYPE.ONBOARDING && req.file ? req.file.filename : null,
      description: notification_content,
      from_user_id: req && req.user ? req.user.id : req,
      notification_status: 'sent',
      notification_type: notification_type,
      redirection_type: redirection_type || null,
      reference_id: reference_id || null,
      created_by: req && req.user ? req.user.id : req,
      updated_by: req && req.user ? req.user.id : req,
      title: notification_subject,
      redirection_object: redirection_object ? JSON.stringify(redirection_object) : null,
      notification_meta_id: notification_meta_id || null
    }
    if (notificationObj.redirection_type == REDIRECTION_TYPE.ONBOARDING) {
      if (users.length > 0) {
        for (const user of users) {
          notificationObj.to_user_id = user.id
          notificationObj.redirection_object = JSON.stringify({ user_id: user.id })
          notificationObj.reference_id = user.id
          const deviceIds = await getUserDeviceId([user])
          if (deviceIds.length > 0) {
            notificationObj.redirection_object = notificationObj.redirection_object ? JSON.parse(notificationObj.redirection_object) : null
            notificationObj.deviceId = deviceIds
            await sendNotificationToUser(notificationObj)
          }
        }
      }
    } else if (notification_meta_id) {
      const deviceIds = await getUserDeviceId(users);

      if (users.length > 0) {
        for (const user of users) {
          notificationObj.to_user_id = user.id
        }
        if (deviceIds.length > 0) {
          notificationObj.redirection_object = redirection_object
          notificationObj.deviceId = deviceIds
          await sendNotificationToUser(notificationObj)
        }
      }
    } else {
      if (users.length > 0) {
        for (const user of users) {
          notificationObj.to_user_id = user.id
          const deviceIds = await getUserDeviceId([user])
          if (deviceIds.length > 0) {
            notificationObj.redirection_object = notificationObj.redirection_object ? JSON.parse(notificationObj.redirection_object) : null
            notificationObj.deviceId = deviceIds
            await sendNotificationToUser(notificationObj)
          }
        }
      }
    }

  } catch (error) {
    console.log("error", error);
    return false;
  }
};

const getUserFullName = async (user_id: any) => {
  try {
    const findUser: any = await User.findOne({
      where: { id: user_id },
      attributes: ["user_first_name", "user_middle_name", "user_last_name"],
      raw: true,
    });
    const employeeName = [];

    if (findUser?.user_first_name) {
      employeeName.push(findUser.user_first_name);
    }
    if (findUser?.user_middle_name) {
      employeeName.push(findUser.user_middle_name);
    }
    if (findUser?.user_last_name) {
      employeeName.push(findUser.user_last_name);
    }
    return employeeName.join(" ");
  } catch (error) {
    console.log("error", error);
  }
};

const getReminderUser = async (dsr_date: any) => {
  try {
    const findBranch: any = await Branch.findAll({ attributes: ['id', 'branch_name'], where: { branch_status: branch_status.ACTIVE }, raw: true })
    if (findBranch.length > 0) {
      for (const branch of findBranch) {
        const findDsr = await DsrDetail.findOne({ where: { branch_id: branch.id, dsr_date: dsr_date, dsr_detail_status: dsr_detail_status.ACTIVE }, raw: true })
        if (!findDsr) {
          const findUser: any = await User.findAll({
            attributes: ['id', 'appToken', 'webAppToken'],
            where: {
              branch_id: branch.id,
              user_status: {
                [Op.notIn]: [
                  user_status.CANCELLED,
                  user_status.DELETED,
                  user_status.PENDING,
                ],
              },
              id: {
                [Op.in]: [
                  sequelize.literal(
                    `(SELECT nv_user_roles.user_id from nv_user_roles where nv_user_roles.role_id IN (SELECT id from nv_roles where role_name IN ('${ROLE_CONSTANT.BRANCH_MANAGER}','${ROLE_CONSTANT.HOTEL_MANAGER}')
                ))`,
                  ),
                ],
              }
            }, group: ['id'], raw: true
          })
          if (findUser.length > 0) {
            await createNotification(findUser, 1, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.DSR_PENDING.content(branch.branch_name, moment(dsr_date).format('DD-MM-YYYY')), NOTIFICATIONCONSTANT.DSR_PENDING.heading, REDIRECTION_TYPE.DSR, null, null)
          }
        }
      }
    }
    return findBranch
  } catch (error) {
    console.log("error", error);
  }
};

function getAllDatesInRange(startDate: any, endDate: any, type: any) {
  const dates = [];
  if (type === "year") {
    let currentDate = moment(startDate).startOf("month");
    const lastDate = moment(endDate).endOf("month");

    while (currentDate <= lastDate) {
      dates.push(currentDate.format("MMM-YYYY"));
      currentDate = currentDate.add(1, "month");
    }
    dates.reverse();
  } else {
    let currentDate = moment(endDate);
    const lastDate = moment(startDate);

    while (currentDate >= lastDate) {
      dates.push(currentDate.format("YYYY-MM-DD"));
      currentDate = currentDate.subtract(1, "days");
    }
  }
  return dates;
}

const generateReportNew = async (branch_id: string, start_date: any, end_date: any, dsr_payment_type_category: any, type: any = 'today', time_period: any = null, columns_group: any, report_type: any, organization_id: any) => {
  try {
    const { startDate, endDate }: any =
      report_type == "day"
        ? getDateRangeCondition(type, start_date, end_date)
        : getDateRangeConditionForGeneral(type, start_date, end_date);
    console.log("startDate", startDate, endDate);
    let whereQuery = `((x.start_date >= '${startDate}' AND x.start_date <= '${endDate}') AND (x.end_date >= '${startDate}' AND x.end_date <= '${endDate}')) AND b.organization_id = '${organization_id}'`;

    if (dsr_payment_type_category) {
      whereQuery += ` AND x.payment_type_category_id IN(${dsr_payment_type_category})`;
    }

    let branchLength = 2;
    if (branch_id) {
      whereQuery += ` AND x.branch_id IN(${branch_id})`;
      branchLength = branch_id.split(",").length;
    }
    if (
      type &&
      type === "custom" &&
      branchLength == 1 &&
      report_type == "day"
    ) {
      whereQuery += ` AND x.type IN('dsr')`;
    }
    if (report_type == 'day') {
      whereQuery += ` AND x.type IN('dsr','wsr')`;
    }

    if (
      time_period &&
      time_period !== "none" &&
      (time_period === "daily" || time_period === "weekly")
    ) {
      let type_array: any;
      if (time_period === "daily") {
        type_array = "'dsr'";
      } else if (time_period === "weekly") {
        type_array = "'dsr', 'wsr'";
      }
      whereQuery += ` AND x.type IN(${type_array})`;
    }
    const getReportQuery = `SELECT x.*, b.branch_name, DAYNAME(x.start_date) AS day_name, MONTHNAME(x.start_date) AS month_name, YEAR(x.start_date) AS year, WEEK(x.start_date) AS week_of_year, QUARTER(start_date) AS quarter
      FROM (
        SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id, di.dsr_amount AS amount, di.payment_type_category_id AS payment_type_category_id, 'dsr' AS type, dd.id AS details_id, ptc.payment_type_category_title AS category_name, 1 AS column_order
        FROM nv_dsr_items AS di
        JOIN nv_dsr_details AS dd ON dd.id = di.dsr_detail_id
        JOIN nv_payment_type_category AS ptc ON ptc.id = di.payment_type_category_id
        WHERE di.dsr_item_status = 'active' AND dd.dsr_detail_status = 'active'
        
        UNION ALL

        SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(dd.dsr_amount_total, '$.NoneVat') AS FLOAT),2) AS amount, -1 AS payment_type_category_id, 'dsr' AS type, dd.id AS details_id, 'Non Vatable Amount' AS category_name, 2 AS column_order 
        FROM nv_dsr_details AS dd 
        WHERE dd.dsr_detail_status = 'active'

        UNION ALL

        SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(dd.dsr_amount_total, '$.VAT1') AS FLOAT),2) AS amount, -2 AS payment_type_category_id, 'dsr' AS type, dd.id AS details_id, '0% VAT Amount' AS category_name, 3 AS column_order  
        FROM nv_dsr_details AS dd 
        WHERE dd.dsr_detail_status = 'active'

        UNION ALL

        SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(dd.dsr_amount_total, '$.VAT2') AS FLOAT),2) AS amount, -3 AS payment_type_category_id, 'dsr' AS type, dd.id AS details_id, '20% VAT Amount' AS category_name, 4 AS column_order  
        FROM nv_dsr_details AS dd 
        WHERE dd.dsr_detail_status = 'active'

        UNION ALL

        SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(dd.dsr_amount_total, '$.AmountVAT1') AS FLOAT),2) AS amount, -4 AS payment_type_category_id, 'dsr' AS type, dd.id AS details_id, 'Total VAT Amount' AS category_name, 5 AS column_order  
        FROM nv_dsr_details AS dd 
        WHERE dd.dsr_detail_status = 'active'

        UNION ALL

        SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(dd.dsr_amount_total, '$.AmountVAT2') AS FLOAT),2) AS amount, -5 AS payment_type_category_id, 'dsr' AS type, dd.id AS details_id, 'Total without VAT' AS category_name, 6 AS column_order  
        FROM nv_dsr_details AS dd 
        WHERE dd.dsr_detail_status = 'active'

        UNION ALL

        SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(dd.dsr_amount_total, '$.TotalIncome') AS FLOAT),2) AS amount, -6 AS payment_type_category_id, 'dsr' AS type, dd.id AS details_id, 'Total of All income' AS category_name, 7 AS column_order  
        FROM nv_dsr_details AS dd 
        WHERE dd.dsr_detail_status = 'active'
        
        UNION ALL
        
        SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id, wi.wsr_amount AS amount, wi.payment_type_category_id AS payment_type_category_id, 'wsr' AS type, wd.id AS details_id, ptc.payment_type_category_title AS category_name, 1 AS column_order    
        FROM nv_wsr_items AS wi
        JOIN nv_wsr_details AS wd ON wd.id = wi.wsr_detail_id
        JOIN nv_payment_type_category AS ptc ON ptc.id = wi.payment_type_category_id
        WHERE wi.wsr_item_status = 'active' AND wd.wsr_detail_status = 'active'

        UNION ALL
        
        SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(wd.wsr_amount_total, '$.NoneVat') AS FLOAT),2) AS amount, -1 AS payment_type_category_id, 'wsr' AS type, wd.id AS details_id, 'Non Vatable Amount' AS category_name, 2 AS column_order    
        FROM nv_wsr_details AS wd
        WHERE wd.wsr_detail_status = 'active'

        UNION ALL
        
        SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(wd.wsr_amount_total, '$.VAT1') AS FLOAT),2) AS amount, -2 AS payment_type_category_id, 'wsr' AS type, wd.id AS details_id, '0% VAT Amount' AS category_name, 3 AS column_order    
        FROM nv_wsr_details AS wd
        WHERE wd.wsr_detail_status = 'active'

        UNION ALL
        
        SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(wd.wsr_amount_total, '$.VAT2') AS FLOAT),2) AS amount, -3 AS payment_type_category_id, 'wsr' AS type, wd.id AS details_id, '20% VAT Amount' AS category_name, 4 AS column_order    
        FROM nv_wsr_details AS wd
        WHERE wd.wsr_detail_status = 'active'

        UNION ALL
        
        SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(wd.wsr_amount_total, '$.AmountVAT1') AS FLOAT),2) AS amount, -4 AS payment_type_category_id, 'wsr' AS type, wd.id AS details_id, 'Total VAT Amount' AS category_name, 5 AS column_order    
        FROM nv_wsr_details AS wd
        WHERE wd.wsr_detail_status = 'active'

        UNION ALL
        
        SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(wd.wsr_amount_total, '$.AmountVAT2') AS FLOAT),2) AS amount, -5 AS payment_type_category_id, 'wsr' AS type, wd.id AS details_id, 'Total without VAT' AS category_name, 6 AS column_order    
        FROM nv_wsr_details AS wd
        WHERE wd.wsr_detail_status = 'active'

        UNION ALL
        
        SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(wd.wsr_amount_total, '$.TotalIncome') AS FLOAT),2) AS amount, -6 AS payment_type_category_id, 'wsr' AS type, wd.id AS details_id, 'Total of All income' AS category_name, 7 AS column_order    
        FROM nv_wsr_details AS wd
        WHERE wd.wsr_detail_status = 'active'
        
        UNION ALL

        SELECT DATE_FORMAT(concat(ed.expense_year, '-', ed.expense_month, '-01'), '%Y-%m-01') AS start_date, LAST_DAY(concat(ed.expense_year, '-', ed.expense_month, '-01')) AS end_date, ed.branch_id AS branch_id, ei.expense_amount AS amount, ei.payment_type_category_id AS payment_type_category_id, 'expense' AS type, ed.id AS details_id, ptc.payment_type_category_title AS category_name, 1 AS column_order
        FROM nv_expense_items AS ei
        JOIN nv_expense_details AS ed ON ed.id = ei.expense_detail_id
        JOIN nv_payment_type_category AS ptc ON ptc.id = ei.payment_type_category_id
        WHERE ei.expense_item_status = 'active' AND ed.expense_detail_status = 'active'
      ) x
      JOIN nv_branches AS b ON b.id = x.branch_id
      WHERE ${whereQuery}
      ORDER BY x.column_order ASC, x.payment_type_category_id ASC`;

    console.log("getReportQuery", getReportQuery);
    let getReportData: any = await sequelize.query(getReportQuery, {
      type: QueryTypes.SELECT,
    });
    console.log("getReportData", getReportData);
    if (getReportData.length == 0 && dsr_payment_type_category) {
      getReportData = await generateDummyReport(
        dsr_payment_type_category,
        branch_id,
        startDate,
        endDate,
        organization_id,
      );
    }

    let branchNames = new Map();
    const categoryNames = new Map();
    let branches = new Set();
    const categories = new Set();

    if (time_period && time_period !== "none") {
      const formattedDateComponents = await generateDateComponents(
        startDate,
        endDate,
        time_period,
      );

      branchNames = new Map(Object.entries(formattedDateComponents));
      branches = new Set(Object.keys(formattedDateComponents));
    } else if (type === "custom" && branchLength == 1) {
      const formattedDateComponents = await generateDateComponents(
        startDate,
        endDate,
        report_type == "day" ? "dates" : "months",
      );

      branchNames = new Map(Object.entries(formattedDateComponents));
      branches = new Set(Object.keys(formattedDateComponents));
    }
    const branchData: any = {};

    getReportData.forEach((row: any) => {
      const {
        start_date,
        branch_id,
        payment_type_category_id,
        branch_name,
        category_name,
        amount,
        day_name,
        month_name,
        year,
        week_of_year,
        quarter,
      } = row;

      let rowKey =
        type === "custom" && branchLength == 1
          ? report_type == "day"
            ? start_date
            : month_name
          : branch_id;

      if (
        (!time_period || time_period == "none") &&
        (type != "custom" || branchLength > 1)
      ) {
        branchNames.set(rowKey, branch_name);
        branches.add(rowKey);
      } else {
        if (time_period == "daily") {
          rowKey = day_name;
        } else if (time_period == "monthly") {
          rowKey = month_name;
        } else if (time_period == "yearly") {
          rowKey = year;
        } else if (time_period == "weekly") {
          rowKey = week_of_year;
        } else if (time_period == "quarterly") {
          rowKey = quarter;
        }
      }
      console.log("rowKey", rowKey);
      categoryNames.set(payment_type_category_id, category_name);
      categories.add(payment_type_category_id);

      branchData[rowKey] = branchData[rowKey] || { Total: 0 };
      branchData[rowKey][payment_type_category_id] =
        (branchData[rowKey][payment_type_category_id] || 0) + amount;
      branchData[rowKey].Total += amount;
    });

    const column: { [key: string]: any } = {};
    const isMultipleBranches = branches.size > 1;
    let columnsGroup: any = [];

    if (!time_period || time_period == "none") {
      column["col1"] =
        isMultipleBranches && (type != "custom" || branchLength > 1)
          ? "Branch"
          : report_type == "day"
            ? "Date"
            : "Month";
    } else {
      if (time_period == "daily") {
        column["col1"] = "Day";
      } else if (time_period == "monthly") {
        column["col1"] = "Month";
      } else if (time_period == "yearly") {
        column["col1"] = "Year";
      } else if (time_period == "weekly") {
        column["col1"] = "Week";
      } else if (time_period == "quarterly") {
        column["col1"] = "Quarter";
      }
    }
    columnsGroup.push({
      id: 1,
      content: column.col1,
      parent_id: null,
      group_type: 0,
      payment_type_title: column.col1,
      payment_type_category_id: 0,
      type: "text",
      key: "col1",
      has_field_currency: 0,
    });
    let idx = 2;

    for (const categoryId of categories) {
      column[`col${idx}`] = categoryNames.get(categoryId);

      const paymentTypeDetails = await sequelize.query(
        `SELECT pt.has_field_currency,ptc.payment_type_id AS payment_type_id,pt.payment_type_title AS payment_type_title
         FROM nv_payment_type_category AS ptc 
         JOIN nv_payment_type AS pt ON pt.id = ptc.payment_type_id 
         WHERE ptc.id = ${categoryId}`,
        { type: QueryTypes.SELECT },
      );

      let has_field_currency = paymentTypeDetails?.[0]?.has_field_currency || 0;
      const payment_type_id =
        paymentTypeDetails?.[0]?.payment_type_id || categoryId;
      const payment_type_title =
        paymentTypeDetails?.[0]?.payment_type_title || "";
      if (typeof categoryId === "number" && categoryId < 0) {
        has_field_currency = 1;
      }

      columnsGroup.push({
        id: idx,
        content: categoryNames.get(categoryId),
        parent_id: null,
        type: "text",
        key: `col${idx}`,
        group_type: payment_type_id,
        payment_type_category_id: categoryId,
        payment_type_title: payment_type_title,
        has_field_currency: has_field_currency,
      });

      idx++;
    }

    const total: any = { col1: "Total" };

    const groupedColumns = groupingData(columnsGroup);
    let totalObjects: any = [];
    let columnsGroupRemove: any = columnsGroup;
    const newGroupedColumns: any = [];
    if (columns_group) {
      if (compareObjects(columns_group, groupedColumns)) {
        columnsGroup = columns_group;
      } else {
        let totalCols = columnsGroup.length;
        columns_group.forEach((column: any) => {
          let newObject: any = {};
          newObject = column;
          if (column.type == "text") {
            const newFindObject = findAllByData(
              "payment_type_category_id",
              column.payment_type_category_id,
              columnsGroupRemove,
            );
            if (newFindObject) {
              newGroupedColumns.push(newFindObject);
              columnsGroupRemove = removeByKey(
                "payment_type_category_id",
                column.payment_type_category_id,
                columnsGroupRemove,
              );
            }
          } else if (column.type == "total") {
            totalCols++;
            newObject.id = totalCols;
            newObject.key = "col" + totalCols;
            const SelectedData: any = [];
            const SelectedIds: any = [];
            const columns: any = [];
            column.columns.forEach((c_column: any) => {
              const oldFindObject = findObjectByKey(
                "key",
                c_column,
                columns_group,
              );
              if (oldFindObject) {
                const newFindObject = findAllByData(
                  "payment_type_category_id",
                  oldFindObject.payment_type_category_id,
                  columnsGroup,
                );
                if (newFindObject) {
                  SelectedData.push(newFindObject.content);
                  SelectedIds.push(newFindObject.id);
                  columns.push(newFindObject.key);
                }
              }
            });
            newObject.SelectedData = SelectedData;
            newObject.SelectedIds = SelectedIds;
            newObject.columns = columns;
            newGroupedColumns.push(newObject);
          } else {
            const newChildColumns: any = [];
            totalCols++;
            newObject.id = totalCols;
            column.children.forEach((c_column: any) => {
              if (c_column.type == "total") {
                const newTotalObject = c_column;
                totalCols++;
                newTotalObject.id = totalCols;
                newTotalObject.key = "col" + totalCols;
                const SelectedData: any = [];
                const SelectedIds: any = [];
                const columns: any = [];
                c_column.columns.forEach((cCol: any) => {
                  const oldFindObject = findObjectByKey(
                    "key",
                    cCol,
                    columns_group,
                  );
                  if (oldFindObject) {
                    const newFindObject = findAllByData(
                      "payment_type_category_id",
                      oldFindObject.payment_type_category_id,
                      columnsGroup,
                    );
                    if (newFindObject) {
                      SelectedData.push(newFindObject.content);
                      SelectedIds.push(newFindObject.id);
                      columns.push(newFindObject.key);
                    }
                  }
                });
                newTotalObject.SelectedData = SelectedData;
                newTotalObject.SelectedIds = SelectedIds;
                newTotalObject.columns = columns;
                newChildColumns.push(newTotalObject);
              } else {
                const newFindObject = findAllByData(
                  "payment_type_category_id",
                  c_column.payment_type_category_id,
                  columnsGroupRemove,
                );
                if (newFindObject) {
                  newFindObject.parent_id = newObject.id;
                  newChildColumns.push(newFindObject);
                  columnsGroupRemove = removeByKey(
                    "payment_type_category_id",
                    c_column.payment_type_category_id,
                    columnsGroupRemove,
                  );
                }
              }
            });
            if (newChildColumns.length > 0) {
              newObject.children = newChildColumns;
              newGroupedColumns.push(newObject);
            }
          }
        });
        columnsGroupRemove = groupingData(columnsGroupRemove, totalCols);
        newGroupedColumns.push(...columnsGroupRemove);
        columnsGroup = newGroupedColumns;
      }

      totalObjects = await sortByKey(await findTotals(columns_group), "order");
    } else {
      columnsGroup = groupedColumns;
    }

    totalObjects.forEach((total_row: any) => {
      column[total_row.key] = total_row.content;
      total[total_row.key] = 0;
    });

    const data = Array.from(branches).map((branchId: any) => {
      const row: any = {};
      row.col1 =
        !time_period || time_period == "none"
          ? isMultipleBranches
            ? branchNames.get(branchId)
            : report_type == "day"
              ? getColumnValue(type, start_date)
              : getColumnValueForGeneral(type, start_date, end_date)
          : branchNames.get(branchId);

      Array.from(categories).forEach((categoryId: any, idx) => {
        row[`col${idx + 2}`] = (
          branchData[branchId] && branchData[branchId][categoryId]
            ? branchData[branchId][categoryId]
            : 0
        ).toFixed(2);
      });

      // row[`col${categories.size + 2}`] = branchData[branchId].Total.toFixed(2);
      totalObjects.forEach(
        ({ key, columns }: { key: string; columns: string[] }) => {
          const columnSum = columns.reduce(
            (sum, column) => sum + Number(row[column] || 0),
            0,
          );
          row[key] = columnSum.toFixed(2);
          total[key] += columnSum;
        },
      );

      return row;
    });

    Array.from(categories).forEach((categoryId: any, idx: number) => {
      const categoryTotal: any = Array.from(branches).reduce(
        (sum, branchId: any) =>
          sum +
          (branchData[branchId] && branchData[branchId][categoryId]
            ? branchData[branchId][categoryId]
            : 0),
        0,
      );

      total[`col${idx + 2}`] = categoryTotal.toFixed(2);
    });

    // Format total values in totalObjects
    totalObjects.forEach(({ key }: { key: string }) => {
      total[key] = Number(total[key]).toFixed(2);
    });

    const reportData = {
      column: column,
      columns_group: columnsGroup,
      data: data,
      total: total,
    };

    return reportData;
  } catch (e: any) {
    console.log(e);
  }
};

function findObjectByKey(key: any, value: any, data: any) {
  for (const item of data) {
    if (item[key] === value) {
      return item;
    }
    if (item.children) {
      const found: any = findObjectByKey(key, value, item.children);
      if (found) return found;
    }
  }
  return null; // Return null if not found
}

function groupingData(columnsGroup: any, length: any = 1) {
  const groupedColumns: any = [];
  const categoryMap: any = {};
  let totalCols = length;
  columnsGroup.forEach((column: any) => {
    const { group_type } = column;

    // Initialize category group if not already present
    if (!categoryMap[group_type]) {
      categoryMap[group_type] = [];
    }
    categoryMap[group_type].push(column);
    totalCols++;
  });

  // Now process each category type to decide whether to create a group
  Object.keys(categoryMap).forEach((group_type) => {
    const categoryColumns = categoryMap[group_type];
    if (categoryColumns.length > 1) {
      const group = {
        id: groupedColumns.length + totalCols, // Incrementing id, assuming these IDs are being sequentially assigned
        content: categoryColumns[0].payment_type_title,
        parent_id: null,
        type: "Group",
        group_type: group_type,
        payment_type_title: categoryColumns[0].payment_type_title,
        children: categoryColumns.map((column: any) => ({
          ...column,
          parent_id: groupedColumns.length + totalCols, // Assign parent_id to the new group
        })),
      };
      groupedColumns.push(group);
    } else {
      // If there's only one item in the category, don't create a group, just add the column directly
      groupedColumns.push(...categoryColumns);
    }
  });
  return groupedColumns;
}
const removeByKey = (key: any, value: any, data: any) => {
  return data.filter((item: any) => item[key] !== value);
};

const findAllByData = (key: any, value: any, data: any) => {
  return data.find((item: any) => item[key] === value);
};
function deepEqual(obj1: any, obj2: any) {
  if (
    typeof obj1 !== "object" ||
    typeof obj2 !== "object" ||
    obj1 === null ||
    obj2 === null
  ) {
    return obj1 === obj2;
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    if (!keys2.includes(key) || !deepEqual(obj1[key], obj2[key])) {
      return false;
    }
  }

  return true;
}

function compareObjects(arr1: any, arr2: any) {
  if (arr1.length !== arr2.length) return false;

  for (let i = 0; i < arr1.length; i++) {
    if (!deepEqual(arr1[i], arr2[i])) {
      return false;
    }
  }

  return true;
}

const generateDateComponents = async (
  startDate: string,
  endDate: string,
  time_period: string,
  filterValue: any = null,
) => {
  const dateComponents: any = {
    daily: {
      Sunday: "Sunday",
      Monday: "Monday",
      Tuesday: "Tuesday",
      Wednesday: "Wednesday",
      Thursday: "Thursday",
      Friday: "Friday",
      Saturday: "Saturday",
    },
    weekly: {},
    quarterly: {
      1: "Quarter 1",
      2: "Quarter 2",
      3: "Quarter 3",
      4: "Quarter 4",
    },
    monthly: {
      January: "January",
      February: "February",
      March: "March",
      April: "April",
      May: "May",
      June: "June",
      July: "July",
      August: "August",
      September: "September",
      October: "October",
      November: "November",
      December: "December",
    },
    yearly: {},
    dates: {},
    months: {},
  };

  for (let index = 1; index <= 53; index++) {
    dateComponents.weekly[index] = `Week ${index}`;
  }

  const currentDate = new Date(startDate);
  while (currentDate <= new Date(endDate)) {
    const year = currentDate.getFullYear();
    const monthName = currentDate.toLocaleString("en-US", { month: "long" });

    if (time_period === "yearly") {
      // Adding to years based on the year
      if (!dateComponents.yearly[year]) {
        dateComponents.yearly[year] = `Year ${year}`;
      }
    } else if (time_period === "dates") {
      // Adding to dates
      dateComponents.dates[moment(currentDate).format("YYYY-MM-DD")] =
        `${moment(currentDate).format("DD-MM-YYYY")}`;
    } else if (time_period === "months") {
      // Adding to months based on the month name
      if (!dateComponents.months[monthName]) {
        dateComponents.months[monthName] = monthName;
      }
    }

    // Move to next date
    currentDate.setDate(currentDate.getDate() + 1);
  }

  // Get the required data based on time_period
  const periodData = dateComponents[time_period] || {};

  const value: any = filterValue ? filterValue.split(",") : "";
  // Filter the data based on the `value` keys, if provided
  if (value && Array.isArray(value) && value.length > 0) {
    return Object.keys(periodData)
      .filter((key) => value.includes(key))
      .reduce((filtered: any, key) => {
        filtered[key] = periodData[key];
        return filtered;
      }, {});
  }

  // Return all records if no `value` is passed
  return periodData;
};
// Recursive function to retrieve objects with type: "total"
const findTotals = async (data: any) => {
  const totals: any = [];

  data.forEach(async (item: any) => {
    if (item.type === "total") {
      item.order = Number(item.key.replace("col", ""));
      totals.push(item);
    }

    // Check for nested child arrays
    if (item.children && Array.isArray(item.children)) {
      totals.push(...(await findTotals(item.children)));
    }
  });

  return totals;
};
const sortByKey = async (
  array: any,
  key: string,
  ascending: boolean = true,
) => {
  return array.sort((a: any, b: any) => {
    if (a[key] < b[key]) return ascending ? -1 : 1;
    if (a[key] > b[key]) return ascending ? 1 : -1;
    return 0;
  });
};

// Helper function to retrieve branch name
const getBranchName = async (branchId: any) => {
  const branchData = await sequelize.query(
    `SELECT branch_name FROM nv_branches WHERE id = ${branchId} `,
    {
      type: QueryTypes.SELECT,
    },
  );
  return branchData[0]?.branch_name || "Unknown Branch";
};

// Helper function to retrieve category name
const getPaymentTypeCategoryName = async (payment_category_type_id: any) => {
  if (payment_category_type_id < 0) {
    if (payment_category_type_id == -1) {
      return `Non Vatable Amount`;
    }
    if (payment_category_type_id == -2) {
      return `0% VAT Amount`;
    }
    if (payment_category_type_id == -3) {
      return `20% VAT Amount`;
    }
    if (payment_category_type_id == -4) {
      return `Total VAT Amount`;
    }
    if (payment_category_type_id == -5) {
      return `Total without VAT`;
    }
    if (payment_category_type_id == -6) {
      return `Total of All income`;
    }
  }
  const categoryData = await sequelize.query(`SELECT payment_type_category_title FROM nv_payment_type_category WHERE id = ${payment_category_type_id} `, {
    type: QueryTypes.SELECT,
  });
  return categoryData[0]?.payment_type_category_title || 'Unknown Category';
};

// Function to get date range condition based on type
function getDateRangeCondition(
  type: string,
  startDate?: string,
  endDate?: string,
): string {
  const currentDate = moment().format("YYYY-MM-DD");
  let start_date: string;
  let end_date: string;
  switch (type) {
    case "today":
      start_date = end_date = currentDate;
      break;

    case "yesterday":
      start_date = end_date = moment().subtract(1, "days").format("YYYY-MM-DD");
      break;

    case "this_week":
      start_date = moment().startOf("isoWeek").format("YYYY-MM-DD");
      end_date = moment().endOf("isoWeek").format("YYYY-MM-DD");
      break;

    case "this_month":
      start_date = moment().startOf("month").format("YYYY-MM-DD");
      end_date = moment().endOf("month").format("YYYY-MM-DD");
      break;

    case "this_quarter":
      start_date = moment().startOf("quarter").format("YYYY-MM-DD");
      end_date = moment().endOf("quarter").format("YYYY-MM-DD");
      break;

    case "last_week":
      start_date = moment()
        .subtract(1, "weeks")
        .startOf("isoWeek")
        .format("YYYY-MM-DD");
      end_date = moment()
        .subtract(1, "weeks")
        .endOf("isoWeek")
        .format("YYYY-MM-DD");
      break;

    case "last_month":
      start_date = moment()
        .subtract(1, "months")
        .startOf("month")
        .format("YYYY-MM-DD");
      end_date = moment()
        .subtract(1, "months")
        .endOf("month")
        .format("YYYY-MM-DD");
      break;

    case "last_7_days":
      start_date = moment().subtract(7, "days").format("YYYY-MM-DD");
      end_date = currentDate;
      break;

    case "last_14_days":
      start_date = moment().subtract(14, "days").format("YYYY-MM-DD");
      end_date = currentDate;
      break;

    case "last_30_days":
      start_date = moment().subtract(30, "days").format("YYYY-MM-DD");
      end_date = currentDate;
      break;

    case "last_60_days":
      start_date = moment().subtract(60, "days").format("YYYY-MM-DD");
      end_date = currentDate;
      break;

    case "last_90_days":
      start_date = moment().subtract(90, "days").format("YYYY-MM-DD");
      end_date = currentDate;
      break;

    case "custom":
      start_date = startDate ? startDate : currentDate;
      end_date = endDate ? endDate : currentDate;
      break;

    default:
      // throw new Error("Invalid filter type.");
      start_date = startDate ? startDate : currentDate;
      end_date = endDate ? endDate : currentDate;
  }

  // return `((x.start_date >= '${start_date}' AND x.start_date <= '${end_date}') AND (x.end_date >= '${start_date}' AND x.end_date <= '${end_date}'))`

  return { startDate: start_date, endDate: end_date } as any;
}

function getColumnValue(type: string, startDate?: any) {
  let columnValue: any = "";
  switch (type) {
    case "today": {
      columnValue = `${moment().format("DD-MM-YYYY")}`;
      break;
    }

    case "yesterday": {
      columnValue = `${moment().subtract(1, "days").format("YYYY-MM-DD")}`;
      break;
    }

    case "this_week": {
      columnValue = `${moment().startOf("isoWeek").format("DD-MM-YYYY")} To ${moment().endOf("isoWeek").format("DD-MM-YYYY")}`;
      break;
    }

    case "this_month": {
      // Get the month as a one-based index (human-readable)
      columnValue = `${moment().startOf("month").format("MMMM")}`;
      break;
    }

    case "this_quarter": {
      columnValue = `${moment().startOf("quarter").format("MMMM")} To ${moment().endOf("quarter").format("MMMM")}`;
      break;
    }

    case "last_week": {
      columnValue = `${moment().subtract(1, "weeks").startOf("isoWeek")} To ${moment().subtract(1, "weeks").endOf("isoWeek").format("DD-MM-YYYY")}`;
      break;
    }

    case "last_month": {
      columnValue = `${moment().subtract(1, "months").startOf("month").format("MMMM")}`; // Output: "November"
      break;
    }

    case "last_7_days": {
      columnValue = `${moment().subtract(7, "days").format("DD-MM-YYYY")} To ${moment().format("DD-MM-YYYY")}`;
      break;
    }

    case "last_14_days": {
      columnValue = `${moment().subtract(14, "days").format("DD-MM-YYYY")} To ${moment().format("DD-MM-YYYY")}`;
      break;
    }

    case "last_30_days": {
      columnValue = `${moment().subtract(30, "days").format("DD-MM-YYYY")} To ${moment().format("DD-MM-YYYY")}`;
      break;
    }

    case "last_60_days": {
      columnValue = `${moment().subtract(60, "days").format("DD-MM-YYYY")} To ${moment().format("DD-MM-YYYY")}`;
      break;
    }

    case "last_90_days": {
      columnValue = `${moment().subtract(90, "days").format("DD-MM-YYYY")} To ${moment().format("DD-MM-YYYY")}`;
      break;
    }

    case "custom": {
      // Replace 'customStartDate' and 'customEndDate' with actual custom dates from user input
      columnValue = `${moment(startDate).format("DD-MM-YYYY")}`;
      break;
    }

    default:
      columnValue = `${moment().format("DD-MM-YYYY")}`;
  }

  return columnValue;
}

function getColumnValueForGeneral(
  type: string,
  startDate?: string,
  endDate?: string,
): string {
  const currentDate = moment().format("YYYY-MM-DD");
  let columnValue: string;

  switch (type) {
    case "current_month":
      columnValue = `${moment().format("MMMM YYYY")}`; // Current month's name with year
      break;

    case "previous_month":
      columnValue = `${moment().subtract(1, "months").format("MMMM YYYY")}`; // Previous month's name with year
      break;

    case "1st_quarter":
      columnValue = `${moment().startOf("year").format("MMMM YYYY")} To ${moment().startOf("year").add(3, "months").subtract(1, "days").format("MMMM YYYY")}`;
      break;

    case "2nd_quarter":
      columnValue = `${moment().startOf("year").add(3, "months").format("MMMM YYYY")} To ${moment().startOf("year").add(6, "months").subtract(1, "days").format("MMMM YYYY")}`;
      break;

    case "3rd_quarter":
      columnValue = `${moment().startOf("year").add(6, "months").format("MMMM YYYY")} To ${moment().startOf("year").add(9, "months").subtract(1, "days").format("MMMM YYYY")}`;
      break;

    case "4th_quarter":
      columnValue = `${moment().startOf("year").add(9, "months").format("MMMM YYYY")} To ${moment().endOf("year").format("MMMM YYYY")}`;
      break;

    case "last_4_months":
      columnValue = `${moment().subtract(4, "months").format("MMMM YYYY")} To ${moment().format("MMMM YYYY")}`;
      break;

    case "current_year":
      columnValue = `${moment().startOf("year").format("YYYY")}`;
      break;

    case "previous_year":
      columnValue = `${moment().subtract(1, "year").format("YYYY")}`;
      break;

    case "last_2_years":
      columnValue = `${moment().subtract(2, "years").format("YYYY")} To ${moment().subtract(1, "year").format("YYYY")}`;
      break;

    case "custom": {
      // Using month name with year for custom range
      const customStart = startDate
        ? moment(startDate).format("MMMM YYYY")
        : moment().format("MMMM YYYY");
      const customEnd = endDate
        ? moment(endDate).format("MMMM YYYY")
        : moment().format("MMMM YYYY");
      columnValue = `${customStart} To ${customEnd}`;
      break;
    }

    default:
      columnValue = `${currentDate} To ${currentDate}`;
  }

  return columnValue;
}

function getDateRangeConditionForGeneral(
  type: string,
  startDate?: string,
  endDate?: string,
): { startDate: string; endDate: string } {
  const currentDate = moment().format("YYYY-MM-DD");
  let start_date: string;
  let end_date: string;

  switch (type) {
    case "current_month":
      start_date = moment().startOf("month").format("YYYY-MM-DD");
      end_date = moment().endOf("month").format("YYYY-MM-DD");
      break;

    case "previous_month":
      start_date = moment()
        .subtract(1, "months")
        .startOf("month")
        .format("YYYY-MM-DD");
      end_date = moment()
        .subtract(1, "months")
        .endOf("month")
        .format("YYYY-MM-DD");
      break;

    case "1st_quarter":
      start_date = moment().startOf("year").format("YYYY-MM-DD");
      end_date = moment()
        .startOf("year")
        .add(3, "months")
        .subtract(1, "days")
        .format("YYYY-MM-DD");
      break;

    case "2nd_quarter":
      start_date = moment()
        .startOf("year")
        .add(3, "months")
        .format("YYYY-MM-DD");
      end_date = moment()
        .startOf("year")
        .add(6, "months")
        .subtract(1, "days")
        .format("YYYY-MM-DD");
      break;

    case "3rd_quarter":
      start_date = moment()
        .startOf("year")
        .add(6, "months")
        .format("YYYY-MM-DD");
      end_date = moment()
        .startOf("year")
        .add(9, "months")
        .subtract(1, "days")
        .format("YYYY-MM-DD");
      break;

    case "4th_quarter":
      start_date = moment()
        .startOf("year")
        .add(9, "months")
        .format("YYYY-MM-DD");
      end_date = moment().endOf("year").format("YYYY-MM-DD");
      break;

    case "last_4_months":
      start_date = moment()
        .subtract(4, "months")
        .startOf("month")
        .format("YYYY-MM-DD");
      end_date = moment().endOf("month").format("YYYY-MM-DD");
      break;

    case "current_year":
      start_date = moment().startOf("year").format("YYYY-MM-DD");
      end_date = moment().endOf("year").format("YYYY-MM-DD");
      break;

    case "previous_year":
      start_date = moment()
        .subtract(1, "year")
        .startOf("year")
        .format("YYYY-MM-DD");
      end_date = moment()
        .subtract(1, "year")
        .endOf("year")
        .format("YYYY-MM-DD");
      break;

    case "last_2_years":
      start_date = moment()
        .subtract(2, "years")
        .startOf("year")
        .format("YYYY-MM-DD");
      end_date = moment()
        .subtract(1, "years")
        .endOf("year")
        .format("YYYY-MM-DD");
      break;

    case "custom":
      start_date = startDate ? startDate : currentDate;
      end_date = endDate ? endDate : currentDate;
      break;

    default:
      start_date = startDate ? startDate : currentDate;
      end_date = endDate ? endDate : currentDate;
  }

  return { startDate: start_date, endDate: end_date };
}

// function determineCol1(data: any, filter: any) {
//   // Extract min and max dates from the data
//   const minStartDate = moment.min(data.map((d: any) => moment(d.start_date)));
//   const maxEndDate = moment.max(data.map((d: any) => moment(d.end_date)));
//   console.log(minStartDate, maxEndDate);
//   // Define filters with corresponding conditions
//   const filters: any = {
//     today: moment().isSame(minStartDate, "day") && moment().isSame(maxEndDate, "day"),
//     yesterday: moment().subtract(1, "day").isSame(minStartDate, "day") && moment().subtract(1, "day").isSame(maxEndDate, "day"),
//     this_week: moment().isSame(minStartDate, "week") && moment().isSame(maxEndDate, "week"),
//     last_week: moment().subtract(1, "week").isSame(minStartDate, "week") && moment().subtract(1, "week").isSame(maxEndDate, "week"),
//     this_month: moment().isSame(minStartDate, "month") && moment().isSame(maxEndDate, "month"),
//     last_month: moment().subtract(1, "month").isSame(minStartDate, "month") && moment().subtract(1, "month").isSame(maxEndDate, "month"),
//     last_7_days: moment().subtract(7, "days").isBefore(minStartDate) && moment().isAfter(maxEndDate),
//     last_14_days: moment().subtract(14, "days").isBefore(minStartDate) && moment().isAfter(maxEndDate),
//     last_30_days: moment().subtract(30, "days").isBefore(minStartDate) && moment().isAfter(maxEndDate),
//     last_60_days: moment().subtract(60, "days").isBefore(minStartDate) && moment().isAfter(maxEndDate),
//     last_90_days: moment().subtract(90, "days").isBefore(minStartDate) && moment().isAfter(maxEndDate),
//     custom: true // Always match custom filter
//   };
//   console.log(moment().isSame(minStartDate, "week") && moment().isSame(maxEndDate, "week"))
//   // Determine which filter matches
//   const matchedFilter = Object.keys(filters).find(key => filters[key]) || "custom";
//   console.log(matchedFilter);
//   // Set col1 based on the filter
//   return matchedFilter.charAt(0).toUpperCase() + matchedFilter.slice(1).replace("_", " ")

// }


const generateReport = async (branch_id: string, start_date: any, end_date: any, dsr_payment_type: string, for_report: any = 0, type: any = 'day', organization_id: any = null) => {
  try {
    let getReportDetails: any = [];
    let whereQuery = "";
    if (branch_id) {
      whereQuery += ` AND dd.branch_id IN(${branch_id})`;
    }
    if (start_date) {
      whereQuery += ` AND dd.dsr_date >= DATE("${start_date}")`;
    }
    if (end_date) {
      whereQuery += ` AND dd.dsr_date <= DATE("${end_date}")`;
    }
    let dsrPaymentWhere = "";
    if (dsr_payment_type) {
      dsrPaymentWhere = `AND dsr_payment_type_id IN(${dsr_payment_type})`;
    }

    let getReportQuery = "";
    if (type === "year") {
      getReportQuery = `
    SELECT
    DATE_FORMAT(dsr_date, '%b-%Y') AS dsr_date,
    branch_id,
    branch_name,
    branch_color,
    SUM(dsr_amount) AS dsr_amount,
    SUM(cash_amount) AS cash_amount,
    SUM(voucher_amount) AS voucher_amount,
    SUM(card_amount) AS card_amount,
    SUM(bank_amount) AS bank_amount
        FROM(
        SELECT 
        dsr_date,
        branch_id,
        b.branch_name,
        b.branch_color,
        (SELECT SUM(dsr_amount) FROM nv_dsr_items WHERE dsr_detail_id = dd.id AND dsr_item_status = 'active' ${dsrPaymentWhere}) AS dsr_amount,
          (SELECT SUM(dsr_amount) FROM nv_dsr_items WHERE dsr_detail_id = dd.id AND dsr_payment_type_id = 1 AND dsr_item_status = 'active' ${dsrPaymentWhere} ) AS cash_amount,
            (SELECT SUM(dsr_amount) FROM nv_dsr_items WHERE dsr_detail_id = dd.id AND dsr_payment_type_id = 2 AND dsr_item_status = 'active' ${dsrPaymentWhere} ) AS voucher_amount,
              (SELECT SUM(dsr_amount) FROM nv_dsr_items WHERE dsr_detail_id = dd.id AND dsr_payment_type_id = 3 AND dsr_item_status = 'active' ${dsrPaymentWhere} ) AS card_amount,
                (SELECT SUM(dsr_amount) FROM nv_dsr_items WHERE dsr_detail_id = dd.id AND dsr_payment_type_id = 4 AND dsr_item_status = 'active' ${dsrPaymentWhere} ) AS bank_amount
                  
              FROM nv_dsr_details AS dd 
              INNER JOIN nv_branches AS b ON b.id = dd.branch_id AND b.organization_id = ${organization_id}
              WHERE dd.dsr_detail_status = 'active'
              ${whereQuery}
              ORDER BY dd.dsr_date DESC
            ) X
            GROUP BY DATE_FORMAT(dsr_date, '%b-%Y'), branch_id;`;
    } else {
      getReportQuery = `
        SELECT
        dsr_date,
        branch_id,
        b.branch_name,
        b.branch_color,
        (SELECT SUM(dsr_amount) FROM nv_dsr_items WHERE dsr_detail_id = dd.id AND dsr_item_status = 'active' ${dsrPaymentWhere} ) AS dsr_amount,
          (SELECT SUM(dsr_amount) FROM nv_dsr_items WHERE dsr_detail_id = dd.id AND dsr_payment_type_id = 1 AND dsr_item_status = 'active' ${dsrPaymentWhere} ) AS cash_amount,
            (SELECT SUM(dsr_amount) FROM nv_dsr_items WHERE dsr_detail_id = dd.id AND dsr_payment_type_id = 2 AND dsr_item_status = 'active' ${dsrPaymentWhere} ) AS voucher_amount,
              (SELECT SUM(dsr_amount) FROM nv_dsr_items WHERE dsr_detail_id = dd.id AND dsr_payment_type_id = 3 AND dsr_item_status = 'active' ${dsrPaymentWhere} ) AS card_amount,
                (SELECT SUM(dsr_amount) FROM nv_dsr_items WHERE dsr_detail_id = dd.id AND dsr_payment_type_id = 4 AND dsr_item_status = 'active' ${dsrPaymentWhere} ) AS bank_amount
                FROM nv_dsr_details AS dd
                INNER JOIN nv_branches AS b ON b.id = dd.branch_id AND b.organization_id = ${organization_id}
                WHERE dd.dsr_detail_status = 'active'
                ${whereQuery}
                ORDER BY dd.dsr_date DESC;`;
    }

    // Execute recursive query to find child roles
    const getReportData: any = await sequelize.query(getReportQuery, {
      type: QueryTypes.SELECT,
    });
    getReportDetails = getReportData;

    // Extract unique dates
    // const dates = [...new Set(getReportDetails.map((row: any) => row.dsr_date))];
    const allDates = getAllDatesInRange(start_date, end_date, type);

    // Extract unique branch names
    const branchDetailsMap = new Map<string, string>();

    getReportDetails.forEach((row: any) => {
      if (!row.branch_color || row.branch_color.trim() === "") {
        row.branch_color = getRandomColor();
      }
      if (!branchDetailsMap.has(row.branch_name)) {
        branchDetailsMap.set(row.branch_name, row.branch_color);
      }
    });

    const branches_name = [...branchDetailsMap.keys()];
    const branches_color = [...branchDetailsMap.values()];

    // const branches_name = [...new Set(getReportDetails.map((row: any) => row.branch_name))]
    // const branches_color = [...new Set(getReportDetails.map((row: any) => row.branch_color))]
    const branches = [
      ...new Set(getReportDetails.map((row: any) => row.branch_id)),
    ];
    // Define columns dynamically
    const columns: any = { col1: "Date" };
    const graphColumn: any = {
      col1: { name: "Date", color: global.config.DSR_DATE_COLOR },
    };

    branches.forEach((branch, index) => {
      columns[`col${index + 2}`] = branches_name[index];
      graphColumn[`col${index + 2}`] = {
        name: branches_name[index],
        color: branches_color[index],
      };
    });

    // Add static columns at the end
    const lastColumns = ["Total"];
    lastColumns.forEach((col, index) => {
      columns[`col${branches.length + 2 + index}`] = col;
      graphColumn[`col${branches.length + 2 + index}`] = {
        name: col,
        color: global.config.DSR_TOTAL_COLOR,
      };
    });

    // Initialize totals
    const totals: any = { col1: "" };
    branches.forEach((branch, index) => {
      totals[`col${index + 2}`] = 0;
    });
    totals[`col${branches.length + 2 + lastColumns.indexOf("Total")}`] = 0;

    // Define data structure
    const formattedData: any = [];
    let max_amount: any = 0;

    // Populate formatted data
    if (getReportDetails.length > 0) {
      allDates.forEach((date: any) => {
        const formattedRow: any = {
          col1: type === "year" ? date : moment(date).format("DD-MM-YYYY"),
        };
        let rowTotal = 0;

        branches.forEach((branch, index) => {
          const colKey = `col${index + 2} `;
          const branchData = getReportDetails.find(
            (row: any) => row.dsr_date === date && row.branch_id === branch,
          );

          const amount =
            branchData && branchData.dsr_amount
              ? Number(branchData.dsr_amount.toFixed(2))
              : 0;

          const allAmount = {
            total_amount:
              branchData && branchData.dsr_amount
                ? Number(branchData.dsr_amount.toFixed(2))
                : 0,
            cash_amount:
              branchData && branchData.cash_amount
                ? Number(branchData.cash_amount.toFixed(2))
                : 0,
            voucher_amount:
              branchData && branchData.voucher_amount
                ? Number(branchData.voucher_amount.toFixed(2))
                : 0,
            card_amount:
              branchData && branchData.card_amount
                ? Number(branchData.card_amount.toFixed(2))
                : 0,
            bank_amount:
              branchData && branchData.bank_amount
                ? Number(branchData.bank_amount.toFixed(2))
                : 0,
          };
          formattedRow[colKey] = for_report ? amount : allAmount;

          // Update totals
          totals[colKey] += amount;
          rowTotal += amount;
        });
        // Set static columns
        formattedRow[`col${branches.length + 2}`] = Number(rowTotal.toFixed(2)); // Total

        // Update overall total

        totals[`col${branches.length + 2}`] += rowTotal;
        totals[`col${branches.length + 2}`] = Number(
          totals[`col${branches.length + 2} `].toFixed(2),
        );

        // Find Max amount
        max_amount =
          rowTotal > totals[`col${branches.length + 2}`]
            ? rowTotal
            : totals[`col${branches.length + 2} `];
        max_amount = Number(max_amount.toFixed(2));

        formattedData.push(formattedRow);
      });
    }

    // Define the final output structure
    const reportData = {
      column: columns,
      graphColumn,
      data: formattedData,
      total: totals,
      max_amount,
    };
    return reportData;
  } catch (e: any) {
    console.log(e);
  }
};

// Function to add spaces before capital letters
const addSpacesBeforeCapitals = (str: string) => {
  return str.replace(/([A-Z])/g, " $1").trim();
};

function formatUserAgentData(userAgentString: any, deviceType: any) {
  const parser = new UAParser(userAgentString);
  const result = parser.getResult();
  // Determine if it's a mobile app
  const isMobileApp = deviceType == "android" || deviceType === "ios";
  return `${userAgentString}
(${isMobileApp ? "App/Environment" : "Browser"}: ${result.browser.name || "Unknown"} (Version: ${result.browser.version || "Unknown"}),
  Operating System: ${result.os.name || "Unknown"} (Version: ${result.os.version || "Unknown"}),
Device: ${result.device.model || "Unknown"},
  Device Type: ${result.device.type || "Unknown"},
  Device Vendor: ${result.device.vendor || "Unknown"})`;
}

const sendInvitation = async (
  user_ids: any,
  request_user_id: any,
  username: any = null,
  auth_password: any = null,
) => {
  try {
    const findPendingUser = await User.findAll({
      where: {
        id: { [Op.in]: user_ids },
        user_status: user_status.PENDING,
      },
      raw: true,
    });
    if (findPendingUser.length > 0) {
      for (const pendingUser of findPendingUser) {
        const findInvitedUser = await UserInvite.findOne({
          where: {
            user_id: pendingUser.id,
            invitation_status: {
              [Op.in]: [invitation_status.REINVITED, invitation_status.INVITED],
            },
          },
        });
        if (findInvitedUser) {
          if (findInvitedUser.invitation_status == invitation_status.INVITED) {
            await UserInvite.update(
              {
                invitation_status: invitation_status.REINVITED,
                action_by: request_user_id,
                updated_by: request_user_id,
              },
              { where: { id: findInvitedUser.id } },
            );
          } else {
            await UserInvite.update(
              {
                invitation_status: invitation_status.REINVITED,
                action_by: request_user_id,
                updated_by: request_user_id,
              },
              { where: { id: findInvitedUser.id } },
            );
          }
        } else {
          await UserInvite.create({
            user_id: pendingUser.id,
            invitation_status: invitation_status.INVITED,
            created_by: request_user_id,
            updated_by: request_user_id,
            action_by: request_user_id,
          } as any);
        }
        const randomPassword = await GeneratePassword();
        const password = await encrypt(randomPassword);
        await User.update(
          { user_password: password, updated_by: request_user_id },
          { where: { id: pendingUser.id } },
        );
        const getRole = await UserRole.findAll({
          where: { user_id: pendingUser.id },
        });
        let findName: string[] = [];
        if (getRole) {
          const role_ids = getRole.map((role) => role.role_id);
          findName = await roleNameEnhanced(role_ids, pendingUser.organization_id);
        }
        const templateData = {
          name: `${pendingUser.user_first_name} ${pendingUser.user_last_name}`,
          email: pendingUser.user_email,
          password: auth_password ? auth_password : randomPassword,
          role: findName.join(", "),
          username: username ? username : pendingUser.user_email,
          mail_type: "send_invitation",
          ORGANIZATION_LOGO: await getOrganizationLogo(
            pendingUser.organization_id,
          ),
          LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
          ADDRESS: EMAIL_ADDRESS.ADDRESS,
          PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
          EMAIL: EMAIL_ADDRESS.EMAIL,
          ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
          smtpConfig: "INFO",
          organization: await getOrgName(pendingUser.organization_id)
        };
        await sendEmailNotification(templateData);
      }
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.log("error", error);
  }
};

export const GeneratePassword = () => {
  const length = 8;
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const numbers = "0123456789";
  const specialCharacters = "@$!%*?&";
  const allCharacters = lowercase + uppercase + numbers + specialCharacters;

  let password = "";
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password +=
    specialCharacters[Math.floor(Math.random() * specialCharacters.length)];

  for (let i = password.length; i < length; i++) {
    password += allCharacters[Math.floor(Math.random() * allCharacters.length)];
  }
  return password
    .split("")
    .sort(() => 0.5 - Math.random())
    .join("");
};

export const addMail = async (mailDetail: any) => {
  try {
    await Mail.create(mailDetail);
  } catch (error) {
    console.log("error", error);
  }
};

export const otpAttemptValidation = async (
  user_email: any,
  mail_subject: any,
  attempt: any,
) => {
  try {
    const findMail: any = await Mail.findAll({
      attributes: ["createdAt"],
      where: {
        mail_to: user_email,
        mail_subject: mail_subject,
      },
      order: [["createdAt", "DESC"]],
      raw: true,
      nest: true,
    });
    // Check if more than 5 OTPs were sent in the current day
    const startOfDay = moment().startOf("day");
    const todayAttempts = findMail.filter((mail: any) => {
      return moment(mail.createdAt).isAfter(startOfDay);
    });

    if (todayAttempts.length >= attempt) {
      return "TOO_MANY_ATTEMPTS_TODAY";
    }

    // Check if the last OTP was sent less than 3 minutes ago
    if (findMail.length > 0) {
      const lastAttempt = findMail[0];
      const threeMinutesAgo = moment().subtract(1, "minutes");

      if (moment(lastAttempt.createdAt).isAfter(threeMinutesAgo)) {
        return "WAIT_BEFORE_RESEND";
      }
    }
    return true;
  } catch (error) {
    console.log("error", error);
  }
};

function getRandomColor() {
  const GraphColors = [
    "#FF0000", // Red
    "#00FF00", // Green
    "#0000FF", // Blue
    "#FF00FF", // Magenta
    "#00FFFF", // Cyan
    "#FFA500", // Orange
    "#800080", // Purple
    "#FF6347", // Tomato
    "#4682B4", // SteelBlue
    "#32CD32", // LimeGreen
    "#FFD700", // Gold
    "#DC143C", // Crimson
    "#00CED1", // DarkTurquoise
    "#FF1493", // DeepPink
    "#7B68EE", // MediumSlateBlue
    "#ADFF2F", // GreenYellow
    "#FF4500", // OrangeRed
    "#1E90FF", // DodgerBlue
    "#8A2BE2", // BlueViolet
  ];
  return GraphColors[Math.floor(Math.random() * GraphColors.length)];
}

const addFooterToPDF = async (
  file: any,
  destination_path: any,
  user_id: any,
) => {
  try {
    const findVerifiedUser: any = await User.findOne({
      where: { id: user_id },
      attributes: ['id', 'user_signature', 'organization_id', 'user_first_name', 'user_middle_name', 'user_last_name']
    });

    if (!findVerifiedUser) {
      throw new Error("User not found.");
    }

    let signatureImagePath: any = [];
    const findGeneralSetting = await getGeneralSettingObj(findVerifiedUser.organization_id);
    if (findVerifiedUser.user_signature) {
      try {
        console.log('User signature ID:', findVerifiedUser.user_signature);
        console.log('Organization ID:', findVerifiedUser.organization_id);

        if (!isNaN(findVerifiedUser.user_signature)) {
          const signatureItem = await Item.findOne({
            where: {
              id: Number(findVerifiedUser.user_signature),
              item_organization_id: findVerifiedUser.organization_id,
            },
            attributes: ['item_location', 'item_mime_type']
          });

          console.log('Signature item found:', signatureItem ? 'yes' : 'no');
          console.log('Signature item location:', signatureItem?.item_location);

          if (!signatureItem) {
            signatureImagePath.push({ name: findGeneralSetting?.name, type: 'name' });
            console.log('No signature item found for the given ID');
          } else if (!signatureItem.item_location) {
            signatureImagePath.push({ name: findGeneralSetting?.name, type: 'name' });
            console.log('Signature item found but has no location');
          } else {
            signatureImagePath.push({ name: signatureItem.item_location, type: 'link' });
            console.log('Successfully added signature path:', signatureItem.item_location);
          }
        }
      } catch (error) {
        signatureImagePath.push({ name: findGeneralSetting?.name, type: 'name' });
        console.error('Error fetching signature item:', error);
      }
    } else if (findVerifiedUser.branch_id) {
      const findBranchSetting = await Branch.findOne({
        attributes: ['branch_sign'],
        where: { id: findVerifiedUser.branch_id, branch_status: branch_status.ACTIVE }, raw: true
      });
      if (findBranchSetting && findBranchSetting?.branch_sign) {
        const signatureItem = await Item.findOne({
          where: {
            id: Number(findBranchSetting?.branch_sign),
            item_organization_id: findVerifiedUser.organization_id,
          },
          attributes: ['item_location', 'item_mime_type']
        });

        console.log('Signature item found:', signatureItem ? 'yes' : 'no');
        console.log('Signature item location:', signatureItem?.item_location);

        if (!signatureItem) {
          console.log('No signature item found for the given ID');
          signatureImagePath.push({ name: findGeneralSetting?.name, type: 'name' });
        } else if (!signatureItem.item_location) {
          signatureImagePath.push({ name: findGeneralSetting?.name, type: 'name' });
          console.log('Signature item found but has no location');
        } else {
          signatureImagePath.push({ name: signatureItem.item_location, type: 'link' });
          console.log('Successfully added signature path:', signatureItem.item_location);
        }
      }
    } else {
      signatureImagePath.push({ name: findGeneralSetting?.name, type: 'name' });
      console.log('No user_signature found for user', user_id);
    }

    console.log("signatureImagePath", signatureImagePath[0], file)
    if (!signatureImagePath[0] || !file.path) {
      throw new Error("Invalid file path or signature image path.");
    }

    signatureImagePath = signatureImagePath[0]

    let fileArray: any;

    const imageMimetypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/bmp",
    ];

    // Get signature image from S3
    let signatureImageBytes: any;
    try {
      const bucketName = process.env.NODE_ENV || "development";
      if (signatureImagePath.type === 'link') {
        // Handle link type
        const s3Response: any = await s3.send(
          new GetObjectCommand({
            Bucket: bucketName,
            Key: signatureImagePath.name,
          })
        );
        // signatureImageBytes = await s3Response.Body!.transformToByteArray();
        // Read the stream
        const chunks: Uint8Array[] = [];
        for await (const chunk of s3Response.Body) {
          chunks.push(chunk);
        }
        signatureImageBytes = Buffer.concat(chunks);
      } else {
        console.log("Using signature image path:", signatureImagePath?.name);
        signatureImageBytes = signatureImagePath?.name
      }
    } catch (s3Error) {
      console.error("Error retrieving signature from S3:", s3Error);
      throw new Error("Failed to retrieve signature image from S3.");
    }

    let docsBytes: any;
    try {
      const bucketName = process.env.NODE_ENV || "development";
      const s3Response = await s3.send(
        new GetObjectCommand({
          Bucket: bucketName,
          Key: file.path,
        })
      );
      docsBytes = await s3Response.Body!.transformToByteArray();
    } catch (s3Err) {
      console.error("Error retrieving signature from S3:", s3Err);
    }

    if (imageMimetypes.includes(file.mimetype)) {
      fileArray = [
        "",
        signatureImageBytes,
        docsBytes
      ];
    } else if (file.mimetype == "application/pdf") {
      fileArray = [
        docsBytes,
        signatureImageBytes,
        "",
      ];
    } else {
      throw new Error("Unsupported file type.");
    }

    const [existingPdfBytes, signatureBytes, ImageBytes] =
      await Promise.all(fileArray);

    let pdfDoc: any;
    if (imageMimetypes.includes(file.mimetype)) {
      pdfDoc = await PDFDocument.create();
    } else if (file.mimetype == "application/pdf") {
      pdfDoc = await PDFDocument.load(existingPdfBytes);
    }

    const signatureImage = signatureImagePath.type === 'link' ? await pdfDoc.embedPng(signatureBytes) : signatureBytes?.toString();
    console.log("signatureImage", signatureImage)
    const signatureDimensions = { width: 80, height: 40 };
    const footerMargin = 20;

    const newPage = pdfDoc.addPage();
    const { width: newPageWidth, height: newPageHeight } = newPage.getSize();

    if (imageMimetypes.includes(file.mimetype)) {
      // const imageMetadata = await sharp(file.path).metadata();
      // const image = await pdfDoc.embedJpg(ImageBytes); // You can adjust this based on the image type.
      const imageBuffer = await sharp(ImageBytes).jpeg().toBuffer();
      const image = await pdfDoc.embedJpg(imageBuffer);
      newPage.drawImage(image, {
        x: 20,
        y: footerMargin + 100,
        width: newPageWidth - 50,
        height: newPageHeight - 150,
      });
    }
    const employeeName = [];

    if (findVerifiedUser?.user_first_name) {
      employeeName.push(findVerifiedUser.user_first_name);
    }
    if (findVerifiedUser?.user_middle_name) {
      employeeName.push(findVerifiedUser.user_middle_name);
    }
    if (findVerifiedUser?.user_last_name) {
      employeeName.push(findVerifiedUser.user_last_name);
    }

    const textSettings = [
      { text: "Employer Print Name:", x: 80, y: footerMargin + 70 },
      { text: "Employer Sign:", x: 220, y: footerMargin + 70 },
      { text: "Date:", x: 380, y: footerMargin + 70 },
      { text: employeeName.join(" "), x: 80, y: footerMargin + 20 },
      { text: moment().format("DD-MM-YYYY"), x: 380, y: footerMargin + 20 },
      { text: "___________________", x: 80, y: footerMargin + 20 },
      { text: "___________________", x: 220, y: footerMargin + 20 },
      { text: "___________________", x: 380, y: footerMargin + 20 },
    ];

    textSettings.forEach(({ text, x, y }) => {
      newPage.drawText(text, {
        x,
        y,
        size: 10,
        color: rgb(0, 0, 0),
      });
    });

    if (signatureImagePath.type === 'link') {
      console.log("Drawing image for signature", signatureImage)
      newPage.drawImage(signatureImage, {
        x: 220,
        y: footerMargin + 20,
        width: signatureDimensions.width,
        height: signatureDimensions.height,
      });
    } else {
      console.log("Drawing text for signature", signatureImage);
      newPage.drawText(signatureImage, {
        x: 220,
        y: footerMargin + 20,
        size: 10,
        color: rgb(0, 0, 0),
      });
    }


    const pdfBytes = await pdfDoc.save();
    // fs.unlinkSync(file.path);
    console.log("file.path", file.path)
    await deleteFileFromBucket(process.env.NODE_ENV!, file.path)
    console.log("file.path", destination_path)
    const fPath = path.resolve(__dirname, '..', 'uploads', 'temp', destination_path)
    console.log("fPath", fPath)
    await fs.writeFileSync(fPath, pdfBytes);
    console.log("Footer added to PDF successfully!");
    return {
      size: await fs.statSync(fPath).size,
      fPath: fPath,
    };
  } catch (err) {
    console.error("Error adding footer to PDF:", err);
    return {
      data: err
    }
  }
};

const getHash = function (originalFile: any, file: any, mimeType?: string) {
  //** create hash using crc32 */
  const fileHash = crc32(originalFile);
  const Magic = mmm.Magic;
  const magic = new Magic(mmm.MAGIC_MIME_TYPE);
  let hashObject = {};
  if (mimeType) {
    const hash = fileHash + file.size + mimeType;
    /*** SHA1 Logic for re hashing         */
    const reHashing = CryptoJS.SHA1(hash); // hash = 10b5557058836image/png

    /** Generate final hash code using CryptoJs */
    const cryptoHash = CryptoJS.enc.Hex.stringify(reHashing);
    return {
      status: true,
      hash: cryptoHash,
      actualMimeType: mimeType,
    };
  } else {
    return new Promise((resolve, reject) =>
      magic.detectFile(file.path, (err: any, File_mimeType: any) => {
        if (err) {
          hashObject = { status: false, message: err };
          return reject(hashObject);
        } else {
          //============ crc[32|64](hash) + bytes(fileSize) + MimeType ===============;
          const hash = fileHash + file.size + File_mimeType;
          /*** SHA1 Logic for re hashing         */
          const reHashing = CryptoJS.SHA1(hash); // hash = 10b5557058836image/png

          /** Generate final hash code using CryptoJs */
          const cryptoHash = CryptoJS.enc.Hex.stringify(reHashing);
          hashObject = {
            status: true,
            hash: cryptoHash,
            actualMimeType: File_mimeType,
          };
          return resolve(hashObject);
        }
      }),
    );
  }
};

const ReadingFile = (path: string) => {
  return new Promise(function (resolve, reject) {
    let FileObject: any;
    const readStream = fs.createReadStream(path, "utf-8");
    readStream.on("error", (error: any) => {
      console.log(error);
      FileObject = { status: false, data: error };
      reject(FileObject);
    });
    const chunk: any = [];
    readStream.on("data", (data: any) => chunk.push(data));
    readStream.on("end", () => {
      console.log("Reading complete");
      FileObject = { status: true, data: chunk };
      resolve(FileObject);
    });
  });
};

const formatContentLength = async (length: number) => {
  if (length < 1024) {
    return {
      length: length,
      unit: "B",
    };
  } else if (length < 1024 * 1024) {
    return {
      length: (length / 1024).toFixed(2),
      unit: "KB",
    };
  } else if (length < 1024 * 1024 * 1024) {
    return {
      length: (length / (1024 * 1024)).toFixed(2),
      unit: "MB",
    };
  } else {
    return {
      length: (length / (1024 * 1024 * 1024)).toFixed(2),
      unit: "GB",
    };
  }
};

const propagateBranchesAndDepartmentsToAllParents = async (
  categoryId: number,
  newBranches: number[],
  newDepartments: number[],
  req: any,
) => {
  try {
    let currentCategory = await DocumentCategory.findOne({ where: { id: categoryId, organization_id: req.user.organization_id }, raw: true });
    const allCategoriesToUpdate = [];

    while (currentCategory) {
      allCategoriesToUpdate.push(currentCategory);

      // Move to the parent category
      if (currentCategory.parent_id) {
        currentCategory = await DocumentCategory.findOne({ where: { id: currentCategory.parent_id, organization_id: req.user.organization_id }, raw: true });
      } else {
        currentCategory = null;
      }
    }

    for (const category of allCategoriesToUpdate.reverse()) {
      // Handle branches
      const existingBranches = await DocumentCategoryBranch.findAll({
        where: { category_id: category.id },
        attributes: ["branch_id"],
        raw: true,
      });
      const existingBranchIds = existingBranches.map(
        (branch) => branch.branch_id,
      );
      const branchesToAssign = newBranches.filter(
        (branchId) => !existingBranchIds.includes(branchId),
      );

      if (branchesToAssign.length > 0) {
        const branchPromises = branchesToAssign.map(async (branchId) => {
          const existingEntry = await DocumentCategoryBranch.findOne({
            where: {
              branch_id: branchId,
              category_id: category.id,
            },
            raw: true,
          });

          if (!existingEntry) {
            return DocumentCategoryBranch.create({
              branch_id: branchId,
              category_id: category.id,
              created_by: req.user.id,
              updated_by: req.user.id,
              document_category_branch_status:
                category.category_status || "active",
            } as any);
          }
        });

        await Promise.all(branchPromises);
      }

      // Handle departments
      const existingDepartments = await DocumentCategoryDepartment.findAll({
        where: { category_id: category.id },
        attributes: ["department_id"],
        raw: true,
      });
      const existingDepartmentIds = existingDepartments.map(
        (dept) => dept.department_id,
      );
      const departmentsToAssign = newDepartments.filter(
        (departmentId) => !existingDepartmentIds.includes(departmentId),
      );

      if (departmentsToAssign.length > 0) {
        const departmentPromises = departmentsToAssign.map(
          async (departmentId) => {
            const existingEntry = await DocumentCategoryDepartment.findOne({
              where: {
                department_id: departmentId,
                category_id: category.id,
              },
              raw: true,
            });

            if (!existingEntry) {
              return DocumentCategoryDepartment.create({
                department_id: departmentId,
                category_id: category.id,
                created_by: req.user.id,
                updated_by: req.user.id,
                document_category_department_status:
                  category.category_status || "active",
              } as any);
            }
          },
        );

        await Promise.all(departmentPromises);
      }
    }
  } catch (error) {
    console.log(error);
    return false;
  }
};

const updateBranchesAndDepartmentsInAllParents = async (
  categoryId: number,
  newBranches: number[],
  newDepartments: number[],
  req: any,
) => {
  try {
    let currentCategory: any = await DocumentCategory.findOne({ where: { id: categoryId, organization_id: req.user.organization_id } });
    const allCategoriesToUpdate = [];

    while (currentCategory) {
      allCategoriesToUpdate.push(currentCategory);

      // Move to the parent category
      if (currentCategory.parent_id) {
        currentCategory = await DocumentCategory.findOne({ where: { id: currentCategory.parent_id, organization_id: req.user.organization_id } });
      } else {
        currentCategory = null;
      }
    }
    let removeBranchIds: any = []
    let removeDepartmentIds: any = []
    for (const category of allCategoriesToUpdate.reverse()) {
      // Handle branches
      const existingBranches = await DocumentCategoryBranch.findAll({
        where: {
          category_id: category.id,
          document_category_branch_status:
            document_category_branch_status.ACTIVE,
        },
        attributes: ["branch_id"],
        raw: true,
      });
      newBranches = newBranches.map(Number);

      const existingBranchIds = existingBranches.map(
        (branch) => branch.branch_id,
      );
      const branchesToAssign = newBranches.filter(
        (branchId) => !existingBranchIds.includes(branchId),
      );
      removeBranchIds = existingBranchIds.filter(
        (id) => !newBranches.includes(id),
      );

      if (branchesToAssign.length > 0) {
        const branchPromises = branchesToAssign.map(async (branchId) => {
          const existingEntry = await DocumentCategoryBranch.findOne({
            where: {
              branch_id: branchId,
              category_id: category.id,
            },
          });

          if (!existingEntry) {
            return DocumentCategoryBranch.create({
              branch_id: branchId,
              category_id: category.id,
              created_by: req.user.id,
              updated_by: req.user.id,
              document_category_branch_status:
                category.category_status || "active",
            } as any);
          } else {
            return DocumentCategoryBranch.update(
              {
                branch_id: branchId,
                category_id: category.id,
                created_by: req.user.id,
                updated_by: req.user.id,
                document_category_branch_status:
                  category.category_status || "active",
              },
              {
                where: {
                  branch_id: existingEntry.branch_id,
                  category_id: existingEntry.category_id,
                },
              },
            );
          }
        });

        await Promise.all(branchPromises);
      }

      // Handle departments
      const existingDepartments = await DocumentCategoryDepartment.findAll({
        where: {
          category_id: category.id,
          document_category_department_status:
            document_category_department_status.ACTIVE,
        },
        attributes: ["department_id"],
        raw: true,
      });
      newDepartments = newDepartments.map(Number);
      const existingDepartmentIds = existingDepartments.map(
        (dept) => dept.department_id,
      );
      const departmentsToAssign = newDepartments.filter(
        (departmentId) => !existingDepartmentIds.includes(departmentId),
      );
      removeDepartmentIds = existingDepartmentIds.filter(
        (id) => !newDepartments.includes(id),
      );

      if (departmentsToAssign.length > 0) {
        const departmentPromises = departmentsToAssign.map(
          async (departmentId) => {
            const existingEntry = await DocumentCategoryDepartment.findOne({
              where: {
                department_id: departmentId,
                category_id: category.id,
              },
            });

            if (!existingEntry) {
              return DocumentCategoryDepartment.create({
                department_id: departmentId,
                category_id: category.id,
                created_by: req.user.id,
                updated_by: req.user.id,
                document_category_department_status:
                  category.category_status || "active",
              } as any);
            } else {
              return DocumentCategoryDepartment.update(
                {
                  department_id: departmentId,
                  category_id: category.id,
                  created_by: req.user.id,
                  updated_by: req.user.id,
                  document_category_department_status:
                    category.category_status || "active",
                },
                {
                  where: {
                    category_id: existingEntry.category_id,
                    department_id: existingEntry.department_id,
                  },
                },
              );
            }
          },
        );

        await Promise.all(departmentPromises);
      }
    }
    if (removeBranchIds?.length > 0) {
      await DocumentCategoryBranch.update({ document_category_branch_status: document_category_branch_status.INACTIVE }, { where: { category_id: categoryId, branch_id: { [Op.in]: removeBranchIds } } })
      await HealthSafetyCategoryItem.setHeaders(req).update(
        {
          status: status.INACTIVE,
          updated_by: req.user.id,
        },
        {
          where: {
            category_id: categoryId,
            branch_id: { [Op.in]: removeBranchIds },
          },
        },
      );
    }
    if (removeDepartmentIds.length > 0) {
      await DocumentCategoryDepartment.update(
        {
          document_category_department_status:
            document_category_department_status.INACTIVE,
          created_by: req.user.id,
          updated_by: req.user.id,
        },
        {
          where: {
            category_id: categoryId,
            department_id: { [Op.in]: removeDepartmentIds },
          },
        },
      );
    }
  } catch (error) {
    console.log(error);
    return false;
  }
};

const reminderContractExpireSoon = async () => {
  try {
    const currentDate = moment().utc();
    const firstTryStart = currentDate.clone().add(14, "days").startOf("day");
    const firstTryEnd = currentDate.clone().add(15, "days").endOf("day");
    const secondTryStart = currentDate.clone().add(7, "days").startOf("day");
    const secondTryEnd = currentDate.clone().add(8, "days").endOf("day");

    const contractWhere = {
      expire_date: {
        [Op.or]: [
          { [Op.between]: [firstTryStart.toDate(), firstTryEnd.toDate()] },
          { [Op.between]: [secondTryStart.toDate(), secondTryEnd.toDate()] },
        ],
      },
      contract_status: contract_status.ACTIVE,
    };

    const getUserList: any = await User.findAll({
      attributes: ['id', 'user_first_name', 'user_middle_name', 'user_last_name', 'user_email', 'organization_id', 'appToken', 'webAppToken', 'branch_id'],
      include: {
        model: UserEmploymentContract,
        as: "user_contract",
        attributes: ["id", "contract_status", "expire_date", "is_confirm_sign"],
        where: contractWhere,
        order: [["createdAt", "desc"]],
      },
      where: {
        user_status: {
          [Op.in]: [user_status.ACTIVE, user_status.COMPLETED],
        },
      },
      raw: true,
      nest: true,
    });
    if (getUserList.length > 0) {
      let expiringUsers = [];
      for (const user of getUserList) {
        const day = moment(user?.user_contract?.expire_date, 'day').diff(currentDate, 'days') + 1;
        await createNotification(getUserList, 1, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.ONBOARDING_FORM_EXPIRING_REMIND.content(day), NOTIFICATIONCONSTANT.ONBOARDING_FORM_EXPIRING_REMIND.heading(user?.user_first_name), REDIRECTION_TYPE.ONBOARDING)
        const employeeName = [];

        if (user?.user_first_name) {
          employeeName.push(user.user_first_name);
        }
        if (user?.user_middle_name) {
          employeeName.push(user.user_middle_name);
        }
        if (user?.user_last_name) {
          employeeName.push(user.user_last_name);
        }
        const templateData = {
          name: employeeName.join(" "),
          expire_date: moment(user?.user_contract?.expire_date).format(
            "DD/MM/YYYY",
          ),
          days: day,
          branch_id: user.branch_id,
          email: user.user_email,
          mail_type: "onboarding_expiring",
          ORGANIZATION_LOGO: await getOrganizationLogo(user.organization_id),
          LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
          ADDRESS: EMAIL_ADDRESS.ADDRESS,
          PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
          EMAIL: EMAIL_ADDRESS.EMAIL,
          ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
          organization_id: user.organization_id,
          smtpConfig: "INFO",
        };
        expiringUsers.push(templateData);
        await sendEmailNotification(templateData);
      }
      if (expiringUsers.length) {
        expiringUsers = _.groupBy(expiringUsers, "branch_id");
        for (const branch_id in expiringUsers) {
          const findBranch = await Branch.findOne({
            where: {
              id: branch_id,
              organization_id: expiringUsers[branch_id][0].organization_id,
            },
            raw: true,
          });
          const user_list = expiringUsers[branch_id]?.map((template: any) => {
            return `<tr> 
                      <td class="user-td" style = "padding: 8px; border-bottom: 1px solid #ddd;">
                          <p style="margin: 0;"> ${template?.name} </p>
                      </td>
                      <td class="user-td" style = "padding: 8px; border-bottom: 1px solid #ddd;">
                          <p style="margin: 0;" > ${template?.expire_date} </p>
                      </td>
                      <td class="user-td" style = "padding: 8px; border-bottom: 1px solid #ddd;">
                          <p style="margin: 0;" > ${template?.days} </p>
                      </td>
                  </tr>`;
          }).join('');
          const findBranchManager = await User.findAll({
            attributes: ['id', 'user_first_name', 'user_middle_name', 'user_last_name', 'user_email'],
            where: {
              branch_id: branch_id,
              organization_id: expiringUsers[branch_id][0].organization_id,
            },
            include: [
              {
                model: UserRole,
                as: "user_roles",
                include: [
                  {
                    model: Role,
                    as: "role",
                    where: {
                      role_name: {
                        [Op.in]: [
                          ROLE_CONSTANT.BRANCH_MANAGER,
                          ROLE_CONSTANT.HOTEL_MANAGER,
                        ],
                      },
                    },
                  },
                ],
              },
            ],
            raw: true,
            nest: true,
          });
          if (findBranchManager.length) {
            for (const bm_user of findBranchManager) {
              const templateData = {
                user_list,
                name: bm_user.user_first_name,
                branch_name: findBranch?.branch_name,
                email:
                  process.env.NEXT_NODE_ENV !== "staging"
                    ? "<EMAIL>"
                    : bm_user.user_email,
                mail_type: "onboarding_expiring_staff",
                ORGANIZATION_LOGO: await getOrganizationLogo(
                  findBranch?.organization_id,
                ),
                LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
                ADDRESS: EMAIL_ADDRESS.ADDRESS,
                PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
                EMAIL: EMAIL_ADDRESS.EMAIL,
                ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
                smtpConfig: "INFO",
              };
              await sendEmailNotification(templateData);
            }
          }
        }
      }
    }
  } catch (error) {
    console.log(error);
    return false;
  }
};

const findAllChildren = async (
  parentIds: any[],
  step: boolean = true,
): Promise<any[]> => {
  try {
    const whereObj: any = {
      parent_id: parentIds,
      category_status: category_status.ACTIVE,
    };

    const children = await DocumentCategory.findAll({
      where: whereObj,
    });
    if (step) {
      if (children.length > 0) {
        const childIds = children.map((child: any) => child.id);
        const furtherChildren = await findAllChildren(childIds, true);
        return [...childIds, ...furtherChildren];
      } else {
        return [];
      }
    } else {
      return children.map((child: any) => child.id);
    }
  } catch (error) {
    console.log("error", error);
    return [];
  }
};

const findAllChildrenWithType = async (
  parentIds: any[],
  step: boolean = true,
  type: boolean = false,
): Promise<any[]> => {
  try {
    const whereObj: any = {
      parent_id: parentIds,
      category_status: category_status.ACTIVE,
    };

    const children = await DocumentCategory.findAll({
      where: whereObj,
    });
    if (step) {
      if (children.length > 0) {
        const childIds = children.map((child: any) => child.id);
        const furtherChildren = await findAllChildrenWithType(
          childIds,
          true,
          true,
        );
        if (type) {
          return [
            ...children.map((child: any) => ({
              id: child.id,
              category_type: child.category_type,
            })),
            ...furtherChildren,
          ];
        } else {
          return [...childIds, ...furtherChildren];
        }
      } else {
        return [];
      }
    } else {
      if (type) {
        return children.map((child: any) => ({
          id: child.id,
          category_type: child.category_type,
        }));
      } else {
        return children.map((child: any) => child.id);
      }
    }
  } catch (error) {
    console.log("error", error);
    return [];
  }
};

const fetchCategoriesRecursively = async (
  login_user_id: any = null,
  parent_id: any = null,
  depth: number = 10,
  category_type: any = null,
  level: number = 1,
  page: any = null,
  size: any = null,
  searchObj: any = null,
  search_type: any = null,
  category_ids: any = [],
  organization_id: any = null,
) => {
  const { limit, offset } = getPagination(page, size);
  const whereObj: any = {
    organization_id: organization_id,
    category_status: { [Op.not]: category_status.DELETED },
  };
  if (category_ids.length > 0) {
    whereObj.id = { [Op.in]: category_ids };
  }
  const categoryObj: any = {
    where: whereObj,
    attributes: [
      "id",
      "category_name",
      "category_image",
      "is_external_link",
      [
        sequelize.literal(`(
              SELECT IF((item_location IS NOT NULL AND item_location != ''), CONCAT('${global.config.API_BASE_URL}', item_location), '') FROM nv_items WHERE nv_items.id = DocumentCategory.category_image
        )`),
        "category_image_url",
      ],
      "category_status",
      "category_type",
      "dashboard_view",
      "category_use",
      "parent_id",
      "updatedAt",
      "category_order",
      [
        sequelize.literal(`(
                SELECT CASE
            WHEN EXISTS(
                  SELECT 1
              FROM nv_health_safety_category_item
              WHERE nv_health_safety_category_item.category_id = DocumentCategory.id
                AND nv_health_safety_category_item.status = 'active'
                ${searchObj.branches
            ? `AND EXISTS (
                  SELECT 1
                  FROM nv_document_category_branch
                  WHERE nv_health_safety_category_item.branch_id IN (${String(searchObj.branches).split(",").map(Number)})
                    AND nv_health_safety_category_item.category_id = DocumentCategory.id
                    AND nv_health_safety_category_item.status = 'active'
                )`
            : ""
          }
                )
            THEN true
            ELSE false
          END
              )`),
        "is_health_and_safety",
      ],
      [
        sequelize.literal(
          `(SELECT COUNT(*) FROM nv_health_safety_category_item WHERE category_id = DocumentCategory.id AND nv_health_safety_category_item.status = 'active')`,
        ),
        "assign_branch_count",
      ],
    ],
    order: [['category_order', 'ASC']]
  }

  if (login_user_id) {
    categoryObj.attributes.push([
      sequelize.literal(`(
                SELECT CASE
          WHEN EXISTS(
                  SELECT 1
            FROM nv_document_category_item_track
            WHERE nv_document_category_item_track.user_id = ${login_user_id}
              AND nv_document_category_item_track.category_id = DocumentCategory.id
              AND nv_document_category_item_track.document_category_item_track_status = 'completed'
                )
          THEN true
          ELSE false
        END
              )`),
      "is_tracked_by_user",
    ]);
  }

  if (search_type != "all") {
    categoryObj.where.parent_id = parent_id;
  }
  if (category_type) categoryObj.where.category_type = category_type;

  if (page && size) {
    categoryObj.limit = Number(limit);
    categoryObj.offset = Number(offset);
  }

  if (typeof searchObj !== "undefined" && searchObj) {
    if (typeof searchObj.search !== "undefined" && searchObj.search) {
      categoryObj.where.category_name = { [Op.like]: `%${searchObj.search}%` };
    }

    if (
      typeof searchObj.category_use !== "undefined" &&
      searchObj.category_use
    ) {
      categoryObj.where.category_use = searchObj.category_use;
    }

    if (
      typeof searchObj.category_status !== "undefined" &&
      searchObj.category_status
    ) {
      categoryObj.where.category_status = searchObj.category_status;
    }

    if (typeof searchObj.departments !== "undefined" && searchObj.departments) {
      if (!categoryObj.where[Op.and]) {
        categoryObj.where[Op.and] = []; // Initialize if it doesn't exist
      }
      categoryObj.where[Op.and].push(
        sequelize.literal(
          `(SELECT department_id FROM nv_document_category_deparment WHERE department_id IN(${String(searchObj.departments).split(",").map(Number)}) AND category_id = DocumentCategory.id AND document_category_department_status = 'active' LIMIT 1)`,
        ),
      );
    }

    if (typeof searchObj.branches !== "undefined" && searchObj.branches) {
      if (!categoryObj.where[Op.and]) {
        categoryObj.where[Op.and] = []; // Initialize if it doesn't exist
      }
      categoryObj.where[Op.and].push(
        sequelize.literal(
          `(SELECT branch_id FROM nv_document_category_branch WHERE branch_id IN(${String(searchObj.branches).split(",").map(Number)}) AND category_id = DocumentCategory.id AND document_category_branch_status = 'active' LIMIT 1)`,
        ),
      );
    }

    if (typeof searchObj.branches !== "undefined" && searchObj.dashboard_view) {
      categoryObj.where.dashboard_view = true;
    }
  }
  const { count, rows: categories } =
    await DocumentCategory.findAndCountAll(categoryObj);

  let main_count = 0;
  if (level == 1) {
    main_count = count;
  }

  const result = await Promise.all(
    categories.map(async (category: any) => {
      let children: any = [];
      if (depth > level) {
        children = await fetchCategoriesRecursively(
          login_user_id,
          category.id,
          depth,
          category_type,
          level + 1,
          null,
          null,
          searchObj,
          null,
          [],
          organization_id,
        );
      }
      return {
        ...category.get({ plain: true }), // Convert Sequelize instance to plain object
        ...(await getItemTrackDetails(login_user_id, category.id, searchObj)),
        // categoryItemDetails: await getCategoryItemDetails(category.id),
        children: children.length > 0 ? children : [], // Attach children if any
      };
    }),
  );

  if (level == 1) {
    const { total_pages } = getPaginatedItems(size, page, main_count || 0);

    return {
      data: result,
      count: count,
      page: parseInt(page),
      size: parseInt(size),
      total_pages,
    };
  }
  return result;
};

const getItemTrackDetails = async (login_user_id: any = null, category_id: any = null, searchObj: any = null) => {
  const findLoginUser = await User.findOne({ attributes: ['organization_id'], where: { id: login_user_id }, raw: true });
  let departmentWhere = '';
  if (typeof searchObj.departments !== 'undefined' && searchObj.departments) {
    departmentWhere =
      `AND (SELECT department_id FROM nv_document_category_deparment WHERE department_id IN(${String(searchObj.departments).split(",").map(Number)}) AND category_id = id AND document_category_department_status = 'active' LIMIT 1)`

  }

  let branchWhere = "";
  if (typeof searchObj.branches !== "undefined" && searchObj.branches) {
    branchWhere = `AND (SELECT branch_id FROM nv_document_category_branch WHERE branch_id IN(${String(searchObj.branches).split(",").map(Number)}) AND category_id = id AND document_category_branch_status = 'active' LIMIT 1)`;
  }

  const getChildCategoryQuery = `
    WITH RECURSIVE ChildRoles AS(
                SELECT id, category_name, parent_id, category_type, category_status
      FROM nv_document_category
      WHERE id = :activeCategoryId AND category_status != 'deleted' AND organization_id = '${findLoginUser?.organization_id}'
      UNION ALL
      SELECT r.id, r.category_name, r.parent_id, r.category_type, r.category_status
      FROM nv_document_category r
      INNER JOIN ChildRoles cr ON r.parent_id = cr.id
      WHERE r.category_status != 'deleted' AND r.organization_id = '${findLoginUser?.organization_id}'
              )
    SELECT GROUP_CONCAT(id SEPARATOR ',') AS category_ids
    FROM ChildRoles
    WHERE id != :activeCategoryId AND category_type = 'file' AND category_status != 'deleted' ${departmentWhere} ${branchWhere}`;

  // Execute recursive query to find child roles
  const getChildCategory = await sequelize.query(getChildCategoryQuery, {
    replacements: { activeCategoryId: category_id },
    type: QueryTypes.SELECT,
  });

  const total_items =
    getChildCategory && getChildCategory[0]?.category_ids
      ? getChildCategory[0]?.category_ids.split(",")
      : [];

  const categoryIdsAsNumbers = total_items?.map((id: string) => Number(id));
  const total_track = await DocumentCategoryItemTrack.count({
    where: {
      category_id: { [Op.in]: categoryIdsAsNumbers },
      user_id: login_user_id,
      document_category_item_track_status:
        document_category_item_track_status.COMPLETED,
    },
  });

  // Size of items

  // const getItemSize: any = await DocumentCategoryItem.findOne({
  //   attributes: [
  //     [Sequelize.fn('SUM', Sequelize.col('document_item.item_size')), 'item_size']
  //   ],
  //   include: [
  //     {
  //       model: Item,
  //       as: "document_item",
  //       attributes: []
  //     }
  //   ],
  //   where: {
  //     document_category_item_status: document_category_item_status.ACTIVE,
  //     item_id: {
  //       [Op.ne]: null as any
  //     },
  //     category_id: {
  //       [Op.in]: category_type === 'folder' ? categoryIdsAsNumbers : [category_id]
  //     }
  //   },
  //   raw: true
  // })

  const getAllChildCategoryQuery = `
  WITH RECURSIVE ChildRoles AS(
  SELECT id, category_name, parent_id, category_type, category_status
    FROM nv_document_category
    WHERE id = :activeCategoryId AND category_status != 'deleted' AND organization_id = :organizationId
    UNION ALL
    SELECT r.id, r.category_name, r.parent_id, r.category_type, r.category_status
    FROM nv_document_category r
    INNER JOIN ChildRoles cr ON r.parent_id = cr.id
    WHERE r.category_status != 'deleted' AND r.organization_id = :organizationId
)
  SELECT GROUP_CONCAT(id SEPARATOR ',') AS category_ids
  FROM ChildRoles
  WHERE category_status != 'deleted' ${departmentWhere} ${branchWhere}
`;

  // Execute recursive query to find child roles
  const getallChildCategory = await sequelize.query(getAllChildCategoryQuery, {
    replacements: { activeCategoryId: category_id, organizationId: findLoginUser?.organization_id },
    type: QueryTypes.SELECT,
  });

  const total_all_items =
    getallChildCategory && getallChildCategory[0]?.category_ids
      ? getallChildCategory[0]?.category_ids.split(",")
      : [];

  const allcategoryIdsAsNumbers = total_all_items?.map((id: string) =>
    Number(id),
  );

  const categoryObj: any = {
    where: {
      category_status: { [Op.not]: category_status.DELETED },
      id: {
        [Op.in]: allcategoryIdsAsNumbers
      },
      organization_id: findLoginUser?.organization_id
    },
    attributes: [
      [
        Sequelize.fn(
          "SUM",
          sequelize.literal(`(
  (
    SELECT item_size FROM nv_items WHERE nv_items.id = DocumentCategory.category_image
) +
  (
    SELECT IF(SUM(document_item.item_size) IS NOT NULL, SUM(document_item.item_size), 0) AS item_size
          FROM nv_document_category_item AS DocumentCategoryItem
          LEFT OUTER JOIN nv_items AS document_item ON DocumentCategoryItem.item_id = document_item.id
          WHERE DocumentCategoryItem.document_category_item_status = 'active'
          AND DocumentCategoryItem.item_id IS NOT NULL
          AND DocumentCategoryItem.category_id = DocumentCategory.id
        )
      )`),
        ),
        "item_size",
      ],
    ],
    order: [["category_order", "ASC"]],
    raw: true,
  };

  const getItemSize: any = await DocumentCategory.findOne(categoryObj);

  const getlength = await formatContentLength(
    getItemSize && getItemSize.item_size ? getItemSize.item_size : 0,
  );
  const size = getlength.length + " " + getlength.unit;

  return {
    total_items: total_items.length,
    total_track: total_track,
    size: size,
  };
};

const getSizeInBytes = (sizeStr: any) => {
  // Match the numeric part and the unit
  const match = sizeStr.match(/([\d.]+)\s*(B|KB|MB|GB)/);
  if (!match) return 0;

  const size = parseFloat(match[1]);
  const unit = match[2];

  // Convert to bytes
  switch (unit) {
    case "B":
      return size;
    case "KB":
      return size * 1024;
    case "MB":
      return size * 1024 * 1024;
    case "GB":
      return size * 1024 * 1024 * 1024;
    default:
      return 0;
  }
};

const calculateTotalSize = async (categories: any) => {
  const totalSizeInBytes = categories.reduce((total: any, category: any) => {
    return total + getSizeInBytes(category.size);
  }, 0);

  return formatContentLength(totalSizeInBytes);
};

const restoreTrackCategoryOfUser = async (
  user_id: any,
  login_user_id: any = null,
) => {
  try {
    const getUserDetail: any = await User.findOne({
      where: { id: user_id },
    });

    const findCategoryTrack = await HealthSafetyCategoryItem.findAll({
      where: {
        branch_id: getUserDetail.branch_id,
        status: status.ACTIVE,
      },
    });

    if (findCategoryTrack.length > 0) {
      const category_id = findCategoryTrack.map((item) => item.category_id);

      const whereQry = ` id IN(${category_id})`;

      const getChildCategoryQuery = `
        WITH RECURSIVE ChildRoles AS(
      SELECT id, category_name, parent_id, category_type, category_status
          FROM nv_document_category
          WHERE ${whereQry} AND category_status != 'deleted'
          UNION ALL
          SELECT r.id, r.category_name, r.parent_id, r.category_type, r.category_status
          FROM nv_document_category r
          INNER JOIN ChildRoles cr ON r.parent_id = cr.id
          WHERE r.category_status != 'deleted'
    )
        SELECT GROUP_CONCAT(id SEPARATOR ',') AS category_ids
        FROM ChildRoles
        WHERE category_type = 'file' AND category_status != 'deleted'
  `;

      // Execute recursive query to find child roles
      const getChildCategory = await sequelize.query(getChildCategoryQuery, {
        type: QueryTypes.SELECT,
      });

      const total_items =
        getChildCategory && getChildCategory[0]?.category_ids
          ? getChildCategory[0]?.category_ids.split(",")
          : [];

      const categoryIdsAsNumbers = total_items?.map((id: string) => Number(id));
      for (const cid of categoryIdsAsNumbers) {
        await DocumentCategoryItemTrack.update(
          {
            document_category_item_track_status:
              document_category_item_track_status.PENDING,
            updated_by: login_user_id,
          },
          {
            where: {
              category_id: cid,
              user_id: user_id,
            },
            silent: true,
          },
        );
      }
    }
    return true;
  } catch (e: any) {
    console.log("Exception", e);
  }
};

const reminderProbationUserSoon = async () => {
  try {
    const currentDate = moment().utc();

    // Fetch user meta records where contract type is "Probation" and probation_length is defined and greater than 0
    const getUserList: any = await User.findAll({
      attributes: ['id', 'user_first_name', 'user_middle_name', 'user_last_name', 'user_email', 'organization_id', 'appToken', 'webAppToken', 'branch_id'],
      include: {
        model: UserMeta,
        as: "user_meta",
        attributes: ["user_id", "start_date", "probation_length"],
        where: {
          probation_length: {
            [Op.gt]: 0, // Ensures probation_length is greater than 0
          },
        },
      },
      where: {
        user_status: {
          [Op.in]: [user_status.ACTIVE, user_status.COMPLETED],
        },
      },
      raw: true,
      nest: true,
    });

    if (getUserList.length > 0) {
      let expiringUsers = [];

      for (const user of getUserList) {
        const startDate = moment(user?.user_meta?.start_date);
        const probationDays = user?.user_meta?.probation_length;

        // Calculate the probation end date
        const probationEndDate = startDate.clone().add(probationDays, "days");
        // Calculate the three notification windows
        const firstTryStart = probationEndDate
          .clone()
          .subtract(1, "month")
          .startOf("day");
        const firstTryEnd = probationEndDate
          .clone()
          .subtract(1, "month")
          .endOf("day");

        const secondTryStart = probationEndDate
          .clone()
          .subtract(15, "days")
          .startOf("day");
        const secondTryEnd = probationEndDate
          .clone()
          .subtract(14, "days")
          .endOf("day");

        const thirdTryStart = probationEndDate
          .clone()
          .subtract(7, "days")
          .startOf("day");
        const thirdTryEnd = probationEndDate
          .clone()
          .subtract(6, "days")
          .endOf("day");
        // Check if current date falls within any of the three notification windows
        if (
          (currentDate.isSameOrAfter(firstTryStart) &&
            currentDate.isSameOrBefore(firstTryEnd)) ||
          (currentDate.isSameOrAfter(secondTryStart) &&
            currentDate.isSameOrBefore(secondTryEnd)) ||
          (currentDate.isSameOrAfter(thirdTryStart) &&
            currentDate.isSameOrBefore(thirdTryEnd))
        ) {
          const daysUntilEnd = probationEndDate.diff(currentDate, "days") + 2;
          // Send notifications
          await createNotification(
            [user],
            1,
            NOTIFICATION_TYPE.INDIVIDUAL,
            NOTIFICATIONCONSTANT.PROBATION_OVER_REMIND.content(daysUntilEnd),
            NOTIFICATIONCONSTANT.PROBATION_OVER_REMIND.heading(user?.user_first_name),
            REDIRECTION_TYPE.ONBOARDING
          );

          const employeeName = [];
          if (user?.user_first_name) employeeName.push(user.user_first_name);
          if (user?.user_middle_name) employeeName.push(user.user_middle_name);
          if (user?.user_last_name) employeeName.push(user.user_last_name);

          const templateData = {
            name: employeeName.join(" "),
            expire_date: probationEndDate.format("DD/MM/YYYY"),
            days: daysUntilEnd,
            branch_id: user.branch_id,
            email: user.user_email,
            mail_type: "probation_expiring",
            ORGANIZATION_LOGO: await getOrganizationLogo(user.organization_id),
            LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
            ADDRESS: EMAIL_ADDRESS.ADDRESS,
            PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
            EMAIL: EMAIL_ADDRESS.EMAIL,
            smtpConfig: "INFO",
          };

          expiringUsers.push(templateData);

          // Send expiration emails to the user
          await sendEmailNotification(templateData);
        }
      }

      // Group users by branch and send branch-level notifications
      if (expiringUsers.length) {
        expiringUsers = _.groupBy(expiringUsers, "branch_id");

        for (const branch_id in expiringUsers) {
          const findBranch = await Branch.findOne({
            where: {
              id: branch_id,
              organization_id: expiringUsers[branch_id][0].organization_id,
            },
            raw: true,
          });
          const user_list = expiringUsers[branch_id]?.map((template: any) => {
            return `<tr> 
                      <td class="user-td" style="padding: 8px; border-bottom: 1px solid #ddd;">
                          <p style="margin: 0;"> ${template?.name} </p>
                      </td>
                      <td class="user-td" style = "padding: 8px; border-bottom: 1px solid #ddd;">
                          <p style="margin: 0;"> ${template?.expire_date} </p>
                      </td>
                      <td class="user-td" style = "padding: 8px; border-bottom: 1px solid #ddd;">
                          <p style="margin: 0;"> ${template?.days} </p>
                      </td>
                    </tr>`;
          }).join('');

          const findBranchManager = await User.findAll({
            attributes: ['id', 'user_first_name', 'user_middle_name', 'user_last_name', 'user_email'],
            where: {
              branch_id,
              organization_id: expiringUsers[branch_id][0].organization_id,
            },
            include: [
              {
                model: UserRole,
                as: "user_roles",
                include: [
                  {
                    model: Role,
                    as: "role",
                    where: {
                      role_name: {
                        [Op.in]: [
                          ROLE_CONSTANT.BRANCH_MANAGER,
                          ROLE_CONSTANT.HOTEL_MANAGER,
                        ],
                      },
                    },
                  },
                ],
              },
            ],
            raw: true,
            nest: true,
          });

          // Send notifications to branch managers
          if (findBranchManager.length) {
            for (const bm_user of findBranchManager) {
              const templateData = {
                user_list,
                name: bm_user.user_first_name,
                branch_name: findBranch?.branch_name,
                email: bm_user.user_email,
                mail_type: "probation_expiring_staff",
                ORGANIZATION_LOGO: await getOrganizationLogo(
                  findBranch?.organization_id,
                ),
                LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
                ADDRESS: EMAIL_ADDRESS.ADDRESS,
                PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
                EMAIL: EMAIL_ADDRESS.EMAIL,
                smtpConfig: "INFO",
              };

              await sendEmailNotification(templateData);
            }
          }
        }
      }
    }
  } catch (error) {
    console.log(error);
    return false;
  }
};

const regenerateEmploymentContractFuncation = async (
  user_id: any,
  req: any,
  notify: any = false,
  isRemove: any,
) => {
  try {
    const findEmploymentContract: any = await UserEmploymentContract.findOne({ where: { user_id: user_id }, order: [['id', 'desc']], raw: true })
    if (findEmploymentContract) {
      await generateContractBeforeOnboard(user_id, req, notify, isRemove, findEmploymentContract)
    } else {
      await generateContractBeforeOnboard(user_id, req, notify, isRemove)
    }
    return true
  } catch (e: any) {
    console.log("Exception", e);
  }
};

function getWeekCount(startDate: any, endDate: any) {
  // Convert startDate and endDate to moment objects
  const start = moment(startDate);
  const end = moment(endDate);

  // Calculate the difference in weeks
  const weekCount = end.diff(start, "weeks", true);

  // Round up to account for partial weeks
  return Math.ceil(weekCount);
}

function getFirstAndLastDates(
  giveMonth: number,
  giveYear: number,
  monthsAhead: number = 1,
) {
  // Create the starting date from the given month and year using moment
  const givenDate = moment([giveYear, giveMonth - 1]); // Months are zero-indexed in moment too

  // First and last dates of the current month
  const firstDateOfCurrentMonth = givenDate
    .startOf("month")
    .format("YYYY-MM-DD");
  const lastDateOfCurrentMonth = givenDate.endOf("month").format("YYYY-MM-DD");

  // Add monthsAhead to the given date and get the first and last dates of the target month
  const targetDate = givenDate.clone().add(monthsAhead, "months");
  const firstDateOfTargetMonth = targetDate
    .startOf("month")
    .format("YYYY-MM-DD");
  const lastDateOfTargetMonth = targetDate.endOf("month").format("YYYY-MM-DD");

  return {
    firstDateOfCurrentMonth,
    lastDateOfCurrentMonth,
    firstDateOfTargetMonth,
    lastDateOfTargetMonth,
  };
}

function isDateInRange(givenDate: any, startDate: any, endDate: any) {
  // Ensure the input dates are valid Date objects
  const date = new Date(givenDate);
  const start = new Date(startDate);
  const end = new Date(endDate);

  // Check if the given date is between the start and end date (inclusive)
  return date >= start && date <= end;
}

const getReminderUserOnWeekEnd = async (wsr_end_date: any) => {
  try {
    // Find the start date of the week (Monday as start of the week)
    const wsr_start_date = wsr_end_date.clone().startOf('isoWeek');
    const findBranch: any = await Branch.findAll({ attributes: ['id', 'branch_name'], where: { branch_status: branch_status.ACTIVE }, raw: true })
    if (findBranch.length > 0) {
      for (const branch of findBranch) {
        const findDsr = await WsrDetail.findOne({
          where: {
            branch_id: branch.id,
            wsr_end_date: wsr_end_date,
            wsr_detail_status: wsr_detail_status.ACTIVE,
          },
        });
        if (!findDsr) {
          const findUser: any = await User.findAll({
            attributes: ['id', 'appToken', 'webAppToken'],
            where: {
              branch_id: branch.id,
              user_status: {
                [Op.notIn]: [
                  user_status.CANCELLED,
                  user_status.DELETED,
                  user_status.PENDING,
                ],
              },
              id: {
                [Op.in]: [
                  sequelize.literal(
                    `(SELECT nv_user_roles.user_id from nv_user_roles where nv_user_roles.role_id IN(SELECT id from nv_roles where role_name IN('${ROLE_CONSTANT.BRANCH_MANAGER}', '${ROLE_CONSTANT.HOTEL_MANAGER}')))`,
                  ),
                ],
              },
            },
            group: ["id"],
          });
          if (findUser.length > 0) {
            await createNotification(findUser, 1, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.WSR_PENDING.content(branch?.branch_name, getWeekCount(wsr_start_date, wsr_end_date), moment(wsr_start_date).format("DD/MM/YYYY"), moment(wsr_end_date).format("DD/MM/YYYY")), NOTIFICATIONCONSTANT.WSR_PENDING.heading, REDIRECTION_TYPE.WSR, null, null)
          }
        }
      }
    }
    return findBranch
  } catch (error) {
    console.log("error", error);
  }
};

const getReminderUserOnLastWeek = async (current_date: any) => {
  try {
    // Find the start date of the week (Monday as start of the week)
    // Get the start and end dates of the previous week
    const lastWeekStartDate = current_date.clone().subtract(1, 'weeks').startOf('isoWeek');
    const lastWeekEndDate = current_date.clone().subtract(1, 'weeks').endOf('isoWeek');
    const findBranch: any = await Branch.findAll({ attributes: ['id', 'branch_name'], where: { branch_status: branch_status.ACTIVE }, raw: true })
    if (findBranch.length > 0) {
      for (const branch of findBranch) {
        const findDsr = await WsrDetail.findOne({ where: { branch_id: branch.id, wsr_start_date: lastWeekStartDate, wsr_end_date: lastWeekEndDate, wsr_detail_status: wsr_detail_status.ACTIVE }, raw: true })
        if (!findDsr) {
          const findUser: any = await User.findAll({
            attributes: ['id', 'appToken', 'webAppToken'],
            where: {
              branch_id: branch.id,
              user_status: {
                [Op.notIn]: [
                  user_status.CANCELLED,
                  user_status.DELETED,
                  user_status.PENDING,
                ],
              },
              id: {
                [Op.in]: [
                  sequelize.literal(
                    `(SELECT nv_user_roles.user_id from nv_user_roles where nv_user_roles.role_id IN(SELECT id from nv_roles where role_name IN('${ROLE_CONSTANT.BRANCH_MANAGER}', '${ROLE_CONSTANT.HOTEL_MANAGER}')
            ))`,
                  ),
                ],
              },
            },
            group: ["id"],
          });
          if (findUser.length > 0) {
            await createNotification(findUser, 1, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.WSR_PENDING.content(branch?.branch_name, getWeekCount(lastWeekStartDate, lastWeekEndDate), moment(lastWeekStartDate).format("DD/MM/YYYY"), moment(lastWeekEndDate).format("DD/MM/YYYY")), NOTIFICATIONCONSTANT.WSR_PENDING.heading, REDIRECTION_TYPE.WSR, null, null)
          }
        }
      }
    }
    return findBranch
  } catch (error) {
    console.log("error", error);
  }
};

const getReminderUserForExpenses = async (current_date: any) => {
  try {
    // Calculate previous month and adjust the year if needed
    const previousMonthDate = current_date.clone().subtract(1, "month");
    const currentMonth = previousMonthDate.month() + 1; // month is zero-indexed in moment.js
    const currentYear = previousMonthDate.year();

    // Find all active branches
    const findBranch: any = await Branch.findAll({
      attributes: ['id', 'branch_name'],
      where: { branch_status: branch_status.ACTIVE }, raw: true
    });

    if (findBranch.length > 0) {
      for (const branch of findBranch) {
        // Check if there's any expense detail for the current month/year in this branch
        const expenseDetail = await ExpenseDetail.findOne({
          where: {
            branch_id: branch.id,
            expense_month: currentMonth,
            expense_year: currentYear,
          }, raw: true
        });
        // If no expense record is found for the current month/year, proceed to find the users for this branch
        if (!expenseDetail) {
          const findUser: any = await User.findAll({
            attributes: ['id', 'appToken', 'webAppToken'],
            where: {
              branch_id: branch.id,
              user_status: {
                [Op.notIn]: [
                  user_status.CANCELLED,
                  user_status.DELETED,
                  user_status.PENDING,
                ],
              },
              id: {
                [Op.in]: [
                  sequelize.literal(
                    `(SELECT nv_user_roles.user_id FROM nv_user_roles WHERE nv_user_roles.role_id IN
              (SELECT id FROM nv_roles WHERE role_name IN('${ROLE_CONSTANT.BRANCH_MANAGER}', '${ROLE_CONSTANT.HOTEL_MANAGER}')))`,
                  ),
                ],
              },
            },
            group: ["id"],
          });
          // If eligible users are found, send reminder notifications
          if (findUser.length > 0) {
            await createNotification(
              findUser,
              1,
              NOTIFICATION_TYPE.INDIVIDUAL,
              NOTIFICATIONCONSTANT.EXPENSE_PENDING.content(branch?.branch_name, currentMonth, currentYear),
              NOTIFICATIONCONSTANT.EXPENSE_PENDING.heading,
              REDIRECTION_TYPE.EXPENSE,
              null,
              null,
            );
          }
        }
      }
    }

    return findBranch;
  } catch (error) {
    console.log("error", error);
  }
};

const getGeoDetails = async (place_code: any) => {
  try {
    if (place_code) {
      const geoDetails = await Geo.findOne({ where: { place_code } });
      if (geoDetails) {
        return {
          place_code: geoDetails.place_code,
          place_name: geoDetails.place_name,
          geo_type: geoDetails.geo_type,
        };
      }
      return {};
    }
    return {};
  } catch (error) {
    return {};
  }
};

const generateEmploymentNumber = async (organization_id: any) => {
  // Get the user with the highest employment number that is not null
  const lastUser = await User.findOne({
    where: { id: { [Op.not]: 1 }, organization_id: organization_id },
    order: [["employment_number", "DESC"]],
  });

  // If no user found or all employment numbers are null, start from 0
  const lastEmploymentNumber = lastUser
    ? parseInt(lastUser.employment_number, 10)
    : 0;

  // Increment by 1 to generate the next employment number
  const newEmploymentNumber = lastEmploymentNumber + 1;

  // Format the new number as a 6-digit string (e.g., 000001)
  return newEmploymentNumber.toString().padStart(6, "0");
};

const getReportFilter = async (report_filter_type: any) => {
  try {
    // Fetch all active report filters from the ReportFilter model
    // - `attributes`: Specifies the fields to retrieve ('key' and 'value')
    // - `where`: Filters records where `report_filter_status` is 'ACTIVE'
    // - `raw`: Returns plain JavaScript objects instead of Sequelize instances
    // - `nest`: Ensures nested key-value structure in the returned data
    return await ReportFilter.findAll({
      attributes: ["key", "value"],
      where: {
        report_filter_status: report_filter_status.ACTIVE,
        report_filter_type: report_filter_type,
      },
      raw: true,
      nest: true,
    });
  } catch (error) {
    // Log the error for debugging purposes
    console.log("error", error);
    // Return an empty array if an error occurs
    return [];
  }
};

const getReportCategoryFilter = async (
  organization_id: any = null,
  include_inactive: any = "false",
  report_filter_type: any = null,
  branch_id: any = null,
) => {
  try {
    const whereObj: any = {
      organization_id: organization_id,
      payment_type_status: payment_type_status.ACTIVE,
    };

    // if (report_filter_type == 'today' || report_filter_type == 'yesterday' || report_filter_type == 'custom') {
    //   whereObj.has_weekly_use = false
    //   if (report_filter_type != 'custom') {
    //     whereObj.payment_type_usage = { [Op.not]: payment_type_usage.EXPENSE }
    //   }
    // }

    // if (report_filter_type == 'last_7_days' || report_filter_type == 'last_14_day' || report_filter_type == 'this_week') {
    //   whereObj.has_weekly_use = false,
    //     whereObj.payment_type_usage = { [Op.not]: payment_type_usage.EXPENSE }
    // }
    const paymentTypeBranchObj: any = {
      payment_type_category_status: payment_type_category_status.ACTIVE,
    };
    if (include_inactive == "true") {
      whereObj.payment_type_status = { [Op.not]: payment_type_status.DELETED };
      paymentTypeBranchObj.payment_type_category_status = {
        [Op.not]: payment_type_category_status.DELETED,
      };
    }

    if (report_filter_type == "dsr") {
      whereObj.payment_type_usage = { [Op.not]: payment_type_usage.EXPENSE };
    }

    if (branch_id) {
      paymentTypeBranchObj.id = {
        [Op.in]: sequelize.literal(
          `(SELECT payment_type_category_id FROM nv_payment_type_category_branch WHERE branch_id = ${branch_id} AND payment_type_category_branch_status = '${payment_type_category_branch_status.ACTIVE}')`,
        ),
      };
    }


    if (branch_id) {
      paymentTypeBranchObj.id = { [Op.in]: sequelize.literal(`(SELECT payment_type_category_id FROM nv_payment_type_category_branch WHERE branch_id = ${branch_id} AND payment_type_category_branch_status = '${payment_type_category_branch_status.ACTIVE}')`) }
    }


    // Fetch all active report filters from the Report Filter Category model
    const getCateoryFilter: any = await PaymentType.findAll({
      attributes: [
        "id",
        "payment_type_title",
        "has_weekly_use",
        "has_include_amount",
        "has_field_currency",
        "payment_type_usage",
      ],
      where: whereObj,
      include: [
        {
          model: PaymentTypeCategory,
          as: "payment_type_category",
          attributes: [
            ["id", "payment_type_category_id"],
            "payment_type_id",
            "payment_type_category_title",
          ],
          where: paymentTypeBranchObj,
        },
      ],
    });

    getCateoryFilter.push({
      id: -1,
      payment_type_title: "VAT",
      has_weekly_use: false,
      has_include_amount: true,
      has_field_currency: true,
      payment_type_usage: "income",
      payment_type_category: [
        {
          payment_type_category_id: -1,
          payment_type_id: -1,
          payment_type_category_title: "Non Vatable Amount",
        },
        {
          payment_type_category_id: -2,
          payment_type_id: -1,
          payment_type_category_title: "0% VAT Amount",
        },
        {
          payment_type_category_id: -3,
          payment_type_id: -1,
          payment_type_category_title: "20% VAT Amount",
        },
        {
          payment_type_category_id: -4,
          payment_type_id: -1,
          payment_type_category_title: "Total VAT Amount",
        },
        {
          payment_type_category_id: -5,
          payment_type_id: -1,
          payment_type_category_title: "Total without VAT",
        },
        {
          payment_type_category_id: -6,
          payment_type_id: -1,
          payment_type_category_title: "Total of All income",
        },
      ],
    });

    return getCateoryFilter;
  } catch (error) {
    // Log the error for debugging purposes
    console.log("error", error);
    // Return an empty array if an error occurs
    return [];
  }
};

const isRecordReferencedInAnyTable = async (
  parentTable: any,
  parentId: any,
) => {
  try {
    // Query to fetch all tables that reference the parent table via foreign key
    const results: any = await sequelize.query(
      `
      SELECT TABLE_NAME, COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE REFERENCED_TABLE_NAME = :parentTable
        AND REFERENCED_COLUMN_NAME = 'id'`,
      {
        replacements: { parentTable },
        type: sequelize.QueryTypes.SELECT,
      },
    );

    // Check if `results` is an array and iterate through each constraint
    if (Array.isArray(results)) {
      for (const { TABLE_NAME, COLUMN_NAME } of results) {
        const countQuery = `SELECT COUNT(*) AS count FROM ${TABLE_NAME} WHERE ${COLUMN_NAME} = :parentId`;
        const [countResult]: any[] = await sequelize.query(countQuery, {
          replacements: { parentId },
          type: sequelize.QueryTypes.SELECT,
        });

        if (countResult?.count > 0) {
          return true; // Record is referenced in at least one table
        }
      }
    }

    return false; // No references found in any tables
  } catch (error) {
    console.error("Error checking foreign key references:", error);
    throw error; // Re-throw the error to handle it at a higher level if necessary
  }
};

const getDashboardModel = async () => {
  try {
    const xaxisArray = [
      { key: "daily", value: "Daily" },
      { key: "weekly", value: "Weekly" },
      { key: "monthly", value: "Monthly" },
      { key: "quarterly", value: "Quarterly" },
      { key: "yearly", value: "Yearly" },
      { key: "time", value: "Duration" },
      { key: "branch", value: "Branch" },
    ];
    const yaxisArray = [
      { key: "dsr", value: "DSR" },
      { key: "wsr", value: "WSR" },
      { key: "expense", value: "Expense" },
    ];
    const yaxisPieArray = [
      { key: "dsr", value: "DSR" },
      { key: "wsr", value: "WSR" },
      { key: "expense", value: "Expense" },
      { key: "user", value: "User" },
    ];
    const modelObj = [
      { id: 1, key: "line_chart", value: "Line Chart" },
      { id: 2, key: "bar_chart", value: "Bar Chart" },
      { id: 3, key: "pie_chart", value: "Pie Chart" },
      { id: 4, key: "number", value: "Number" },
    ];

    const dashboardObj = {
      model_list: modelObj,
      xaxis_list: xaxisArray,
      yaxis_list: yaxisArray,
      yaxis_pie_chart: yaxisPieArray,
    };
    return dashboardObj;
  } catch (error) {
    console.error("Error:", error);
    throw error; // Re-throw the error to handle it at a higher level if necessary
  }
};

const getReportDashboard = async (
  dashboard_filter_type: any = null,
  organization_id: any = null,
) => {
  try {
    const whereObj: any = {
      organization_id: organization_id,
      payment_type_status: { [Op.not]: payment_type_status.DELETED },
    };

    dashboard_filter_type = dashboard_filter_type.split(",");

    if (dashboard_filter_type.length > 0) {
      const whereOrObj: any = [];
      for (const filter of dashboard_filter_type) {
        if (filter == "dsr") {
          whereOrObj.push({
            payment_type_usage: { [Op.not]: payment_type_usage.EXPENSE },
            has_weekly_use: false,
          });
        }
        if (filter == "wsr") {
          whereOrObj.push({
            payment_type_usage: { [Op.not]: payment_type_usage.EXPENSE },
            has_weekly_use: true,
          });
        }
        if (filter == "expense") {
          whereOrObj.push({ payment_type_usage: payment_type_usage.EXPENSE });
        }
      }
      whereObj[Op.or] = whereOrObj;
    }

    // Fetch all active report filters from the Report Filter Category model
    const getCateoryFilter: any = await PaymentType.findAll({
      attributes: [
        "id",
        "payment_type_title",
        "has_weekly_use",
        "has_include_amount",
        "has_field_currency",
        "payment_type_usage",
        "payment_type_status",
      ],
      where: whereObj,
      include: [
        {
          model: PaymentTypeCategory,
          as: "payment_type_category",
          attributes: [
            ["id", "payment_type_category_id"],
            "payment_type_id",
            "payment_type_category_title",
            "payment_type_category_status",
          ],
          where: {
            payment_type_category_status: {
              [Op.not]: payment_type_category_status.DELETED,
            },
          },
        },
      ],
    });
    return getCateoryFilter;
  } catch (error) {
    // Log the error for debugging purposes
    console.log("error", error);
    // Return an empty array if an error occurs
    return [];
  }
};

const generateDashboard = async (branch_id: string, start_date: any, end_date: any, type: any = 'last_month', model_id: any, filter_time_period: any, organization_id: any) => {
  try {
    let time_period = '';
    let xaxis_value = '';
    const findDashboardModel: any = await DashboardModel.findOne({ attributes: ['id', 'xaxis_list', 'xaxis_value', 'yaxis_list', 'yaxis_value', 'model_type'], where: { id: model_id, model_status: model_status.ACTIVE }, raw: true })
    let { startDate, endDate }: any = getDateRangeCondition(type, start_date, end_date);
    if (findDashboardModel.xaxis_list == 'time' && filter_time_period === 'yearly') {
      // Define the current year
      const currentYear = moment().year();

      // Calculate the startDate and endDate based on the determined range
      startDate = moment(`${currentYear - 9}-01-01`, "YYYY-MM-DD")
        .startOf("year")
        .format("YYYY-MM-DD");
      endDate = moment(`${currentYear}-12-31`, "YYYY-MM-DD")
        .endOf("year")
        .format("YYYY-MM-DD");
    }

    if (findDashboardModel.xaxis_list === "yearly") {
      // Define the current year
      const currentYear = moment().year();
      let startYear: number, endYear: number;
      if (findDashboardModel.xaxis_value) {
        // Parse xaxis_value to get the range of years
        const years = findDashboardModel.xaxis_value.split(",").map(Number);
        startYear = Math.min(...years);
        endYear = Math.max(...years);
      } else {
        // When xaxis_value is not provided, take the last five years
        startYear = currentYear - 4;
        endYear = currentYear;
      }
      // Calculate the startDate and endDate based on the determined range
      startDate = moment(`${startYear}-01-01`, "YYYY-MM-DD")
        .startOf("year")
        .format("YYYY-MM-DD");
      endDate = moment(`${endYear}-12-31`, "YYYY-MM-DD")
        .endOf("year")
        .format("YYYY-MM-DD");
    }

    let whereQuery = `((x.start_date >= '${startDate}' AND x.start_date <= '${endDate}') AND (x.end_date >= '${startDate}' AND x.end_date <= '${endDate}'))`;

    if (branch_id) {
      whereQuery += ` AND x.branch_id IN(${branch_id.split(",")})`;
    }

    const branchLength = 2;
    if (findDashboardModel) {
      if (findDashboardModel.yaxis_value) {
        whereQuery += ` AND x.payment_type_category_id IN(${findDashboardModel.yaxis_value})`;
      } else {
        whereQuery += ` AND x.payment_type_category_id IN(-6)`;
      }

      if (findDashboardModel.yaxis_list) {
        whereQuery += ` AND x.type IN(${findDashboardModel.yaxis_list
          .split(",")
          .map((item: any) => `'${item}'`)
          .join(",")})`;
      }

      time_period =
        findDashboardModel.xaxis_list !== "branch"
          ? findDashboardModel.xaxis_list
          : "";
      if (findDashboardModel.xaxis_value) {
        xaxis_value = findDashboardModel.xaxis_value;
        if (findDashboardModel.xaxis_list === "branch") {
          whereQuery += ` AND x.branch_id IN(${findDashboardModel.xaxis_value})`;
          // branchLength = findDashboardModel.xaxis_value.split(',').length
        } else if (findDashboardModel.xaxis_list === "daily") {
          whereQuery += ` AND DAYNAME(x.start_date) IN(${findDashboardModel.xaxis_value
            .split(",")
            .map((item: any) => `'${item}'`)
            .join(",")})`;
        } else if (findDashboardModel.xaxis_list === "weekly") {
          whereQuery += ` AND WEEK(x.start_date) IN(${findDashboardModel.xaxis_value})`;
        } else if (findDashboardModel.xaxis_list === "monthly") {
          whereQuery += ` AND MONTHNAME(x.start_date) IN(${findDashboardModel.xaxis_value
            .split(",")
            .map((item: any) => `'${item}'`)
            .join(",")})`;
        } else if (findDashboardModel.xaxis_list === "quarterly") {
          whereQuery += ` AND QUARTER(start_date) IN(${findDashboardModel.xaxis_value})`;
        } else if (findDashboardModel.xaxis_list === "yearly") {
          whereQuery += ` AND YEAR(x.start_date) IN(${findDashboardModel.xaxis_value})`;
        }
      }
    }
    if (findDashboardModel?.xaxis_list == "time" && filter_time_period) {
      time_period = filter_time_period;

      // Determine start and end dates for the time period
      const [start, end] =
        filter_time_period === "yearly"
          ? [
            moment()
              .subtract(4, "years")
              .startOf("year")
              .format("YYYY-MM-DD"),
            moment().endOf("year").format("YYYY-MM-DD"),
          ]
          : [startDate, endDate];

      // Generate time period components
      const timePeriodObj = await generateDateComponents(
        start,
        end,
        filter_time_period,
      );

      // Build the timeKeyString and mapping only if valid keys exist
      const timeKeyString = Object.keys(timePeriodObj)
        .map((key) => `'${key}'`)
        .join(", ");

      if (timeKeyString) {
        const timePeriodMapping: Record<string, string> = {
          daily: `DAYNAME(x.start_date)`,
          weekly: `WEEK(x.start_date)`,
          monthly: `MONTHNAME(x.start_date)`,
          quarterly: `QUARTER(x.start_date)`,
          yearly: `YEAR(x.start_date)`,
        };

        const columnName = timePeriodMapping[filter_time_period];
        if (columnName) {
          whereQuery += ` AND ${columnName} IN(${timeKeyString})`;
        }
      }
    } else if (
      findDashboardModel?.xaxis_list === "time" &&
      !filter_time_period
    ) {
      // For 'time', we directly use 'monthly' as the filter
      const timePeriodObj = await generateDateComponents(
        startDate,
        endDate,
        "monthly",
      );
      const timeKeyString = Object.keys(timePeriodObj)
        .map((key) => `'${key}'`)
        .join(", ");

      if (timeKeyString) {
        whereQuery += ` AND MONTHNAME(x.start_date) IN(${timeKeyString})`;
        time_period = "monthly";
      }
    }

    if (
      time_period &&
      time_period !== "none" &&
      (time_period === "daily" || time_period === "weekly")
    ) {
      let type_array: any;
      if (time_period === "daily") {
        type_array = "'dsr'";
      } else if (time_period === "weekly") {
        type_array = "'dsr', 'wsr'";
      }
      whereQuery += ` AND x.type IN(${type_array})`;
    }

    const getReportQuery = `SELECT x.*, b.branch_name, DAYNAME(x.start_date) AS day_name, MONTHNAME(x.start_date) AS month_name, YEAR(x.start_date) AS year, WEEK(x.start_date) AS week_of_year, QUARTER(start_date) AS quarter
      FROM (
        SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id, di.dsr_amount AS amount, di.payment_type_category_id AS payment_type_category_id, 'dsr' AS type, dd.id AS details_id, ptc.payment_type_category_title AS category_name, 1 AS column_order
        FROM nv_dsr_items AS di
        JOIN nv_dsr_details AS dd ON dd.id = di.dsr_detail_id
        JOIN nv_payment_type_category AS ptc ON ptc.id = di.payment_type_category_id
        WHERE di.dsr_item_status = 'active' AND dd.dsr_detail_status = 'active'

        UNION ALL

        SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(dd.dsr_amount_total, '$.NoneVat') AS FLOAT),2) AS amount, -1 AS payment_type_category_id, 'dsr' AS type, dd.id AS details_id, 'Non Vatable Amount' AS category_name, 2 AS column_order
        FROM nv_dsr_details AS dd
        WHERE dd.dsr_detail_status = 'active'

        UNION ALL

        SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(dd.dsr_amount_total, '$.VAT1') AS FLOAT),2) AS amount, -2 AS payment_type_category_id, 'dsr' AS type, dd.id AS details_id, '0% VAT Amount' AS category_name, 3 AS column_order
        FROM nv_dsr_details AS dd
        WHERE dd.dsr_detail_status = 'active'

        UNION ALL

        SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(dd.dsr_amount_total, '$.VAT2') AS FLOAT),2) AS amount, -3 AS payment_type_category_id, 'dsr' AS type, dd.id AS details_id, '20% VAT Amount' AS category_name, 4 AS column_order
        FROM nv_dsr_details AS dd
        WHERE dd.dsr_detail_status = 'active'

        UNION ALL

        SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(dd.dsr_amount_total, '$.AmountVAT1') AS FLOAT),2) AS amount, -4 AS payment_type_category_id, 'dsr' AS type, dd.id AS details_id, 'Total VAT Amount' AS category_name, 5 AS column_order
        FROM nv_dsr_details AS dd
        WHERE dd.dsr_detail_status = 'active'

        UNION ALL

        SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(dd.dsr_amount_total, '$.AmountVAT2') AS FLOAT),2) AS amount, -5 AS payment_type_category_id, 'dsr' AS type, dd.id AS details_id, 'Total without VAT' AS category_name, 6 AS column_order
        FROM nv_dsr_details AS dd
        WHERE dd.dsr_detail_status = 'active'

        UNION ALL

        SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(dd.dsr_amount_total, '$.TotalIncome') AS FLOAT),2) AS amount, -6 AS payment_type_category_id, 'dsr' AS type, dd.id AS details_id, 'Total of All income' AS category_name, 7 AS column_order
        FROM nv_dsr_details AS dd
        WHERE dd.dsr_detail_status = 'active'

        UNION ALL

        SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id, wi.wsr_amount AS amount, wi.payment_type_category_id AS payment_type_category_id, 'wsr' AS type, wd.id AS details_id, ptc.payment_type_category_title AS category_name, 1 AS column_order
        FROM nv_wsr_items AS wi
        JOIN nv_wsr_details AS wd ON wd.id = wi.wsr_detail_id
        JOIN nv_payment_type_category AS ptc ON ptc.id = wi.payment_type_category_id
        WHERE wi.wsr_item_status = 'active' AND wd.wsr_detail_status = 'active'

        UNION ALL

        SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(wd.wsr_amount_total, '$.NoneVat') AS FLOAT),2) AS amount, -1 AS payment_type_category_id, 'wsr' AS type, wd.id AS details_id, 'Non Vatable Amount' AS category_name, 2 AS column_order
        FROM nv_wsr_details AS wd
        WHERE wd.wsr_detail_status = 'active'

        UNION ALL

        SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(wd.wsr_amount_total, '$.VAT1') AS FLOAT),2) AS amount, -2 AS payment_type_category_id, 'wsr' AS type, wd.id AS details_id, '0% VAT Amount' AS category_name, 3 AS column_order
        FROM nv_wsr_details AS wd
        WHERE wd.wsr_detail_status = 'active'

        UNION ALL

        SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(wd.wsr_amount_total, '$.VAT2') AS FLOAT),2) AS amount, -3 AS payment_type_category_id, 'wsr' AS type, wd.id AS details_id, '20% VAT Amount' AS category_name, 4 AS column_order
        FROM nv_wsr_details AS wd
        WHERE wd.wsr_detail_status = 'active'

        UNION ALL

        SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(wd.wsr_amount_total, '$.AmountVAT1') AS FLOAT),2) AS amount, -4 AS payment_type_category_id, 'wsr' AS type, wd.id AS details_id, 'Total VAT Amount' AS category_name, 5 AS column_order
        FROM nv_wsr_details AS wd
        WHERE wd.wsr_detail_status = 'active'

        UNION ALL

        SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(wd.wsr_amount_total, '$.AmountVAT2') AS FLOAT),2) AS amount, -5 AS payment_type_category_id, 'wsr' AS type, wd.id AS details_id, 'Total without VAT' AS category_name, 6 AS column_order
        FROM nv_wsr_details AS wd
        WHERE wd.wsr_detail_status = 'active'

        UNION ALL

        SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id, ROUND(CAST(JSON_EXTRACT(wd.wsr_amount_total, '$.TotalIncome') AS FLOAT),2) AS amount, -6 AS payment_type_category_id, 'wsr' AS type, wd.id AS details_id, 'Total of All income' AS category_name, 7 AS column_order
        FROM nv_wsr_details AS wd
        WHERE wd.wsr_detail_status = 'active'

        UNION ALL

        SELECT DATE_FORMAT(concat(ed.expense_year, '-', ed.expense_month, '-01'), '%Y-%m-01') AS start_date, LAST_DAY(concat(ed.expense_year, '-', ed.expense_month, '-01')) AS end_date, ed.branch_id AS branch_id, ei.expense_amount AS amount, ei.payment_type_category_id AS payment_type_category_id, 'expense' AS type, ed.id AS details_id, ptc.payment_type_category_title AS category_name, 1 AS column_order
        FROM nv_expense_items AS ei
        JOIN nv_expense_details AS ed ON ed.id = ei.expense_detail_id
        JOIN nv_payment_type_category AS ptc ON ptc.id = ei.payment_type_category_id
        WHERE ei.expense_item_status = 'active' AND ed.expense_detail_status = 'active'

        UNION ALL

        SELECT DATE_FORMAT(concat(ed.expense_year, '-', ed.expense_month, '-01'), '%Y-%m-01') AS start_date, LAST_DAY(concat(ed.expense_year, '-', ed.expense_month, '-01')) AS end_date, ed.branch_id AS branch_id, (SELECT SUM(ei.expense_amount) FROM nv_expense_items AS ei WHERE ei.expense_detail_id = ed.id AND ei.expense_item_status = 'active') AS amount, -6 AS payment_type_category_id, 'expense' AS type, ed.id AS details_id, 'Total of All income' AS category_name, 7 AS column_order
        FROM nv_expense_details AS ed
        WHERE ed.expense_detail_status = 'active'

      ) x
      JOIN nv_branches AS b ON b.id = x.branch_id AND b.organization_id = '${organization_id}'
      WHERE ${whereQuery}
      ORDER BY x.column_order ASC, x.payment_type_category_id ASC`;

    const getReportData: any = await sequelize.query(getReportQuery, {
      type: QueryTypes.SELECT,
    });
    let data: any = [];
    const columnsGroup: any = [];
    if (getReportData.length > 0) {
      let branchNames = new Map();
      const categoryNames = new Map();
      let branches = new Set();
      const categories = new Set();

      if (time_period && time_period !== "none") {
        const formattedDateComponents = await generateDateComponents(
          startDate,
          endDate,
          time_period,
          xaxis_value,
        );

        branchNames = new Map(Object.entries(formattedDateComponents));
        branches = new Set(Object.keys(formattedDateComponents));
      }
      const branchData: any = {};

      getReportData.forEach((row: any) => {
        const {
          branch_id,
          payment_type_category_id,
          branch_name,
          category_name,
          amount,
          day_name,
          month_name,
          year,
          week_of_year,
          quarter,
        } = row;

        let rowKey = branch_id;

        if (
          (!time_period || time_period == "none") &&
          (type != "custom" || branchLength > 1)
        ) {
          branchNames.set(rowKey, branch_name);
          branches.add(rowKey);
        } else {
          if (time_period == "daily") {
            rowKey = day_name;
          } else if (time_period == "monthly") {
            rowKey = month_name;
          } else if (time_period == "yearly") {
            rowKey = year;
          } else if (time_period == "weekly") {
            rowKey = week_of_year;
          } else if (time_period == "quarterly") {
            rowKey = quarter;
          }
        }
        categoryNames.set(payment_type_category_id, category_name);
        categories.add(payment_type_category_id);

        branchData[rowKey] = branchData[rowKey] || { Total: 0 };
        branchData[rowKey][payment_type_category_id] =
          (branchData[rowKey][payment_type_category_id] || 0) + amount;
        branchData[rowKey].Total += amount;
      });

      const column: { [key: string]: any } = {};

      if (!time_period || time_period == "none") {
        column["col1"] = "Branch";
      } else {
        if (time_period == "daily") {
          column["col1"] = "Day";
        } else if (time_period == "monthly") {
          column["col1"] = "Month";
        } else if (time_period == "yearly") {
          column["col1"] = "Year";
        } else if (time_period == "weekly") {
          column["col1"] = "Week";
        } else if (time_period == "quarterly") {
          column["col1"] = "Quarter";
        }
      }

      let idx = 2;
      if (findDashboardModel?.model_type == 'pie_chart') {
        const findBranchColor = await Branch.findAll({ attributes: ['branch_name', 'branch_color'], where: { id: { [Op.in]: Array.from(branches) }, organization_id: organization_id } as any, raw: true, nest: true })
        const filsArray = findBranchColor.length > 0 ? findBranchColor.map((item: any) => { return { branch_name: item.branch_name, branch_color: item.branch_color } }) : [];
        columnsGroup.push({
          type: "pie",
          xKey: "col1",
          yKey: `col${idx}`,
          yName: "Total of all income",
          fills: filsArray,
        });
      } else {
        for (const categoryId of categories) {
          column[`col${idx}`] = categoryNames.get(categoryId);
          const columnType =
            findDashboardModel?.model_type == "line_chart"
              ? "line"
              : findDashboardModel?.model_type == "bar_chart"
                ? "bar"
                : findDashboardModel?.model_type == "pie_chart"
                  ? "pie"
                  : "number";
          let filsArray: any = [];
          if (findDashboardModel?.model_type == 'pie_chart' && findDashboardModel.xaxis_list == 'branch') {
            const findBranchColor = await Branch.findAll({ attributes: ['branch_name', 'branch_color'], where: { id: { [Op.in]: Array.from(branches) }, organization_id: organization_id } as any, raw: true, nest: true })
            filsArray = findBranchColor.length > 0 ? findBranchColor.map((item: any) => { return { branch_name: item.branch_name, branch_color: item.branch_color } }) : [];
          }
          columnsGroup.push({
            type: columnType,
            xKey: "col1",
            yKey: `col${idx}`,
            yName: categoryNames.get(categoryId),
            fills: filsArray,
          });
          idx++;
        }
      }
      data = Array.from(branches).map((branchId: any) => {
        const row: any = {};
        row.col1 = branchNames.get(branchId);
        if (findDashboardModel?.model_type == "pie_chart") {
          row[`col2`] = parseFloat(branchData[branchId].Total.toFixed(2));
        } else {
          Array.from(categories).forEach((categoryId: any, idx) => {
            row[`col${idx + 2}`] = parseFloat(
              (branchData[branchId] && branchData[branchId][categoryId]
                ? branchData[branchId][categoryId]
                : 0
              ).toFixed(2),
            );
          });
        }
        return row;
      });
    }

    if (findDashboardModel.yaxis_list === 'user') {
      const whereObj: any = { branch_status: branch_status.ACTIVE, organization_id: organization_id }
      if (findDashboardModel.xaxis_value) {
        whereObj.id = { [Op.in]: findDashboardModel.xaxis_value.split(",") };
      }
      const findBranch = await Branch.findAll({
        where: whereObj,
        raw: true
      });

      if (findBranch.length > 0) {
        for (const branch of findBranch) {
          const findBranchUserCount = await User.count({
            where: {
              branch_id: branch.id,
              organization_id: organization_id,
              user_status: {
                [Op.not]: [user_status.DELETED, user_status.CANCELLED],
              },
            },
          });

          // Use branch's color and name directly from the array
          columnsGroup.push({
            branch_name: branch.branch_name,
            branch_color: branch.branch_color,
          });

          // Populate the data array with user counts
          data.push({
            col1: branch.branch_name,
            col2: findBranchUserCount,
          });
        }

        const result = {
          series: [
            {
              type: "pie",
              xKey: "col1",
              yKey: "col2",
              yName: "Total of User",
              fills: columnsGroup,
            },
          ],
          data: data,
        };
        return result;
      }
    }

    if (findDashboardModel.model_type == 'number') {
      let totalCount: any
      if (findDashboardModel.xaxis_value == 'branch') {
        // Query to count branches created in the given range
        const branchCount = await Branch.count({
          where: {
            branch_status: branch_status.ACTIVE,
            organization_id: organization_id
          } as any
        });
        totalCount = branchCount;
      } else if (findDashboardModel.xaxis_value == "user") {
        // Query to count User  created in the given range
        const getUserId = await getSuperAdminUserIdEnhanced(organization_id)
        const whereObj: any = {
          organization_id: organization_id,
          user_status: { [Op.notIn]: [user_status.DELETED, user_status.CANCELLED] },
          id: { [Op.not]: getUserId }
        }
        const UserCount = await User.count({
          where: whereObj,
        });
        totalCount = UserCount;
      } else if (findDashboardModel.xaxis_value == "department") {
        // Query to count department created in the given range
        const departmentCount = await Department.count({
          where: {
            department_status: department_status.ACTIVE,
            organization_id: organization_id
          } as any
        });
        totalCount = departmentCount
      } else if (findDashboardModel.xaxis_value == 'dsr') {
        totalCount = await DsrDetail.count({
          include: [{
            model: Branch,
            as: "dsr_branch",
            where: {
              organization_id: organization_id
            }
          }]
        })
      } else if (findDashboardModel.xaxis_value == 'wsr') {
        totalCount = await WsrDetail.count({
          include: [{
            model: Branch,
            as: "wsr_branch",
            where: {
              organization_id: organization_id
            }
          }]
        })
      } else if (findDashboardModel.xaxis_value == 'expense') {
        totalCount = await ExpenseDetail.count({
          include: [{
            model: Branch,
            as: "expense_branch",
            where: {
              organization_id: organization_id
            }
          }]
        })
      } else if (findDashboardModel.xaxis_value == 'category') {
        totalCount = await PaymentType.count({ where: { payment_type_status: payment_type_status.ACTIVE, organization_id: organization_id } })
      } else {
        totalCount = 0;
      }
      return {
        total: totalCount
      }
    } else {
      const reportData = {
        series: columnsGroup.length > 0 ? columnsGroup : [],
        data: data.length > 0 ? data : [],
      };
      return reportData;
    }
  } catch (e: any) {
    console.log(e);
  }
};

const generateDummyReport = async (
  payment_category_type_id: any,
  branch_id: any = "",
  start_date: any,
  end_date: any,
  organization_id: any,
) => {
  try {
    payment_category_type_id = payment_category_type_id
      ? payment_category_type_id.split(",")
      : [];
    branch_id = branch_id ? branch_id.split(",") : [];
    if (branch_id == "") {
      // Get all branches
      const branchList = await Branch.findAll({ attributes: ['id', 'branch_name', 'branch_color'], where: { branch_status: branch_status.ACTIVE, organization_id: organization_id }, raw: true })
      branch_id = branchList.length > 0 ? branchList.map((branch: any) => { return branch.id }) : [];
    }
    const dummyArray = [];
    const date = moment(start_date);
    // Process original payment_category_type_id records
    if (payment_category_type_id.length > 0) {
      for (const category of payment_category_type_id) {

        if (branch_id.length > 0) {
          for (const branch of branch_id) {
            const obj = {
              start_date,
              end_date,
              branch_id: parseInt(branch),
              payment_type_category_id: parseInt(category),
              amount: 0,
              branch_name: await getBranchName(parseInt(branch)),
              category_name: await getPaymentTypeCategoryName(
                parseInt(category),
              ),
              day_name: date.format("dddd"),
              month_name: date.format("MMMM"),
              year: date.year(),
              week_of_year: date.format("w"),
              quarter: date.quarter(),
            };
            // console.log("obj", obj);
            dummyArray.push(obj);
          }
        }
      }
    }

    // Now add the extra dynamic columns after processing original records
    // const extraColumns = [
    //   "Non Vatable Amount",
    //   "0% VAT Amount",
    //   "20% VAT Amount",
    //   "Total VAT Amount",
    //   "Total without VAT",
    //   "Total of All income",
    // ];

    // // Use for...of loop to handle async operations
    // for (const [index, columnName] of extraColumns.entries()) {
    //   const payment_type_category_id = -(index + 1);  // Start with -1, -2, ...

    //   for (const branch of branch_id) {
    //     const obj = {
    //       start_date,
    //       end_date,
    //       branch_id: parseInt(branch),
    //       payment_type_category_id,
    //       amount: 0,
    //       branch_name: await getBranchName(parseInt(branch)),
    //       category_name: columnName,
    //       day_name: date.format('dddd'),
    //       month_name: date.format('MMMM'),
    //       year: date.year(),
    //       week_of_year: date.format('w'),
    //       quarter: date.quarter()
    //     };

    //     dummyArray.push(obj);
    //   }
    // }
    return dummyArray;
  } catch (e) {
    console.log(e);
  }
};

/**
 * Returns the financial year period for the given forecast year
 */
function getFinancialDates(
  forecastYear: string,
  startMonthNumber: any,
  endMonthNumber: any,
): { current: FinancialPeriod; previous: FinancialPeriod } {
  const [startYear, endYear] = forecastYear.split("-").map(Number);
  if (isNaN(startYear) || isNaN(endYear)) {
    return {
      current: { startDate: "", endDate: "", startYear: "", endYear: "" },
      previous: { startDate: "", endDate: "", startYear: "", endYear: "" },
    };
  }
  return {
    current: {
      startDate:
        startMonthNumber == 1
          ? `${startYear}-0${startMonthNumber}-01`
          : `${startYear}-${startMonthNumber}-01`, // April 1st of the start year
      endDate:
        startMonthNumber == 1
          ? `${startYear}-${endMonthNumber}-31`
          : `${endYear}-${endMonthNumber}-31`, // March 31st of the end year
      startYear: startYear.toString(),
      endYear:
        startMonthNumber == 1 ? startYear.toString() : endYear.toString(),
    },
    previous: {
      startDate:
        startMonthNumber == 1
          ? `${startYear - 1}-0${startMonthNumber}-01`
          : `${startYear - 1}-${startMonthNumber}-01`,
      endDate:
        startMonthNumber == 1
          ? `${startYear - 1}-${endMonthNumber}-31`
          : `${endYear - 1}-${endMonthNumber}-31`,
      startYear: (startYear - 1).toString(),
      endYear:
        startMonthNumber == 1
          ? (startYear - 1).toString()
          : (endYear - 1).toString(),
    },
  };
}

interface FinancialPeriod {
  startDate: string;
  endDate: string;
  startYear: string;
  endYear: string;
}

/**
 * Returns the start and end dates for a given month and year
 */
function getMonthDates(
  year: number,
  month: number,
): { startDate: string; endDate: string } {
  const monthIndex = new Date(`${month} 1, ${year}`).getMonth(); // Get the month index (0-based)
  // Create date objects and set the time to midnight UTC
  const startDate = new Date(Date.UTC(year, monthIndex, 1));
  const endDate = new Date(Date.UTC(year, monthIndex + 1, 0));
  return {
    startDate: startDate.toISOString().split("T")[0], // Format as YYYY-MM-DD
    endDate: endDate.toISOString().split("T")[0], // Format as YYYY-MM-DD
  };
}

/**
 * Generates an object with the months and years between the given start and end dates
 */
const generateDateMonthsComponents = async (
  startDate: string,
  endDate: string,
) => {
  const dateComponents: any = {
    months: {},
  };

  // Initialize the current date to the start date
  const currentDate = new Date(startDate);
  while (currentDate <= new Date(endDate)) {
    // Get the year and month name of the current date
    const year = currentDate.getFullYear();
    const monthName = currentDate.toLocaleString("en-US", { month: "long" });

    // Adding to months based on the month name
    if (!dateComponents.months[monthName]) {
      dateComponents.months[monthName] = { name: monthName, year: year };
    }

    // Move to next date
    currentDate.setDate(currentDate.getDate() + 1);
  }

  // Get the required data based on time_period
  const periodData = dateComponents["months"] || {};

  // Return all records if no `value` is passed
  return periodData;
};

/**
 * Generates a forecast budget report for a given forecast ID.
 */
const generateForecastBudgetReport = async (
  forecast_id: any = null,
  user_role: any = null,
  format_type: any = null,
  category_type: any = null,
  payment_type_id: any = null,
  forecast_history_id: any = null,
  forecastYear: any = null,
  branchIds: any = null,
  payment_type_category_id: any = null,
  view_type: any = null,
  previous_history_id: any = null,
  forecast_budget_status: any = null,
  organization_id: any = null,
) => {
  try {
    /** get financial month from settings */
    const setting = await Setting.findOne({
      attributes: ["key", "value"],
      where: {
        key: "financial_month",
        setting_status: setting_status.ACTIVE,
        organization_id: organization_id,
      },
      raw: true,
    });
    let startMonthNumber: any = "";
    let endMonthNumber: any = "";
    if (setting) {
      const [startMonthName, endMonthName] = setting.value.split('-').map(month => month.trim().toLowerCase());
      startMonthNumber = await getMonthNumber(startMonthName);
      endMonthNumber = await getMonthNumber(endMonthName);
    }
    const whereForecastObject: any = {
      forecast_status: { [Op.not]: forecast_status.DELETED },
      id: forecast_id,
    };
    if (forecast_budget_status == forecast_status.DELETED) {
      whereForecastObject.forecast_status = forecast_status.DELETED;
    }
    // Fetch forecast budget details
    const getForecastBudgetDetails: any = await Forecast.findOne({
      where: whereForecastObject,
      attributes: ["forecast_year", "branch_id", "forecast_status"],
      raw: true,
    });
    let getForecastBudgetPreviousData: any = [];
    let getBudgetPreviousData: any = null;
    // Get financial dates for the current and previous year
    const forecastFinancialyear = forecast_id
      ? getForecastBudgetDetails.forecast_year
      : forecastYear;
    const financialDates: any = getFinancialDates(
      forecastFinancialyear,
      startMonthNumber,
      endMonthNumber,
    );
    const startDate = financialDates.current.startDate;
    const endDate = financialDates.current.endDate;
    const branch_id = forecast_id
      ? getForecastBudgetDetails.branch_id
      : branchIds;
    const forecastStatus = forecast_id
      ? getForecastBudgetDetails.forecast_status
      : forecast_status.ACTIVE;
    /* Get current year with month name  */
    const formattedDateComponents = await generateDateMonthsComponents(
      startDate,
      endDate,
    );

    /* Get previous year with month name  */
    const formattedPreviousDateComponents = await generateDateMonthsComponents(
      financialDates.previous.startDate,
      financialDates.previous.endDate,
    );
    const previousMonthDatesArray = Object.values(formattedDateComponents).map(
      (data: any) => {
        const { name } = data;
        return forecastStatus == forecast_status.APPROVED
          ? getMonthDates(formattedDateComponents[name].year, name)
          : getMonthDates(formattedPreviousDateComponents[name].year, name);
      },
    );
    const whereQuery: any = await generateWhereClause(previousMonthDatesArray);
    // const getcurrentYear = forecastStatus == forecast_status.APPROVED ? financialDates.current.startYear : financialDates.previous.startYear
    // const getendYear = forecastStatus == forecast_status.APPROVED ? financialDates.current.endYear : financialDates.previous.endYear

    /* Get budget data with category and month data with budget and comparison type */
    const getForecastBudgetData: any = await getforecastBudgetData(
      forecast_id,
      branch_id,
      forecastYear,
      category_type,
      format_type,
      payment_type_id,
      user_role,
      forecast_history_id,
      payment_type_category_id,
      view_type,
      forecast_budget_status,
    );

    /** Get past Year Data */
    const getForecastBudgetPastData = await paymentCategoryData(
      forecast_id,
      whereQuery,
      forecast_history_id,
      branch_id,
    );

    // Initialize variables to hold columns, total amounts, and target amounts
    const columnsGroup: any = {};
    let currentCategory: any = null;

    /** Get previous history data if previous history id available */
    if (previous_history_id) {
      getForecastBudgetPreviousData = await getforecastBudgetData(
        forecast_id,
        branch_id,
        forecastYear,
        category_type,
        format_type,
        payment_type_id,
        user_role,
        previous_history_id,
        payment_type_category_id,
        view_type,
        forecast_budget_status,
      );
    }
    const result: any = [];
    let totalData: any = {};
    const addedHeadersByCategory: Record<string, Set<string>> = {}; // Separate tracking for each category
    for (const [index, element] of getForecastBudgetData.entries()) {
      const past = getForecastBudgetPastData[index];
      Object.assign(element, past);

      let rowData: any = {};
      const subrowData: any = {};
      const combinedData: any = {};
      let totalForecast: any = 0.0;
      let totalFyBudget: any = 0.0;
      /** check if start new header, if yes then store before total income column */
      if (
        currentCategory &&
        currentCategory !== element.forecast_category_type
      ) {
        result.push(totalData);
        totalData = {};
      }

      /** Category wise set blank data for header */
      if (!addedHeadersByCategory[element.forecast_category_type]) {
        addedHeadersByCategory[element.forecast_category_type] = new Set();
      }

      let categorySet = addedHeadersByCategory[element.forecast_category_type];

      /** Ensure each category type is only added once **/
      if (
        element.forecast_category_type &&
        !categorySet.has(element.forecast_category_type)
      ) {
        categorySet.add(element.forecast_category_type);
        result.push({
          col1: element.forecast_category_type,
          type: "mainHeader",
        });
        currentCategory = element.forecast_category_type;
        totalData["col1"] = `Total ${currentCategory}`;
        totalData[`type`] = "mainTotal";
      }

      /** Sub Category wise set blank data for header */
      if (!addedHeadersByCategory[element.payment_type_title]) {
        addedHeadersByCategory[element.payment_type_title] = new Set();
      }

      categorySet = addedHeadersByCategory[element.payment_type_title];

      if (
        element.payment_type_title &&
        !categorySet.has(element.payment_type_title)
      ) {
        categorySet.add(element.payment_type_title);
        // result.push({
        //   col1: element.payment_type_title,
        //   has_field_currency: element.has_field_currency,
        //   payment_type_id: element.payment_type_id,
        //   payment_type_category_id: null,
        // });

        combinedData["col1"] = element.payment_type_title;
        combinedData[`payment_type_id`] = element.payment_type_id;
        combinedData[`payment_type_category_id`] =
          element.forecast_category_status == forecast_category_status.COMBINED
            ? element.payment_type_category_id
            : null;
        combinedData[`has_field_currency`] = element.has_field_currency;
        combinedData[`type`] = "subHeader";
        result.push(combinedData);
      }
      if (
        element.forecast_category_status !== forecast_category_status.COMBINED
      ) {
        if (
          element.payment_type_category_title &&
          !categorySet.has(element.payment_type_category_title)
        ) {
          categorySet.add(element.payment_type_category_title);
          subrowData["col1"] = element.payment_type_category_title;
          subrowData[`payment_type_id`] = element.payment_type_id;
          subrowData[`payment_type_category_id`] =
            element.payment_type_category_id;
          subrowData[`has_field_currency`] = element.has_field_currency;
        }
      }
      // Define the columns group (static definition)
      columnsGroup[1] = {
        id: 1,
        content: "Category",
        parent_id: null,
        type: "text",
        key: "col1",
        has_field_currency: 0,
        group_type: "category",
        category_name: "Category",
        payment_type_id: null,
        payment_type_category_id: null,
      };
      let colIndex = 2;
      let columnIndex = 2;

      /** check if previous data exist if yes then find data and add as previous data key */
      if (getForecastBudgetPreviousData.length > 0) {
        getBudgetPreviousData = _.find(getForecastBudgetPreviousData, {
          payment_type_id: element.payment_type_id,
          payment_type_category_id: element.payment_type_category_id,
        });
      }
      for (const data of Object.values(formattedDateComponents)) {
        const { name }: any = data;
        /** ----------------------------------------------------- forecast/Actual value ----------------------------------------------------------- */
        const forecastColKey = `col${colIndex}`;
        const forecastValue =
          forecastStatus == forecast_status.APPROVED
            ? (totalData[`col${colIndex}`] ?? 0) + element[name]
            : (totalData[`col${colIndex}`] ?? 0) +
            (element[`actual_${name}`] || 0);
        const forecastrawValue =
          forecastStatus == forecast_status.APPROVED
            ? element[name]
            : element[`actual_${name}`] || 0;

        totalData[forecastColKey] = forecastValue;
        rowData[`col${colIndex++}`] =
          forecastStatus == forecast_status.APPROVED
            ? roundAmount(element[name]).toFixed(2)
            : roundAmount(element[`actual_${name}`] || 0).toFixed(2);
        rowData[`forecast_category_type`] = element.forecast_category_type;
        rowData[`forecast_category_status`] = element.forecast_category_status;
        /** Show April to December in the previous year */
        let content =
          forecastStatus == forecast_status.APPROVED
            ? `Budget ${moment(financialDates.current.startYear).format("YY")}-${moment(financialDates.current.endYear).format("YY")}`
            : `Act ${moment().year(formattedPreviousDateComponents[name].year).format("YY")}`;
        /** January to March should also belong to the same financial year */
        // if (["January", "February", "March"].includes(name)) {
        //   content = forecastStatus == forecast_status.APPROVED ? `Budget ${moment(financialDates.current.startYear).format('YY')}-${moment(financialDates.current.endYear).format('YY')}` : `Act ${moment(financialDates.previous.endYear).format('YY')}`; // Same year for Jan-March
        // }
        element.month_name = name;
        columnsGroup[columnIndex] = await buildColumnGroup(
          columnIndex,
          content,
          element,
          0,
          0,
          element.forecast_type !== forecast_type.BUGDET ? "line" : "area",
          element.forecast_type !== forecast_type.BUGDET ? 1 : 0,
          0,
          null,
        );
        totalForecast +=
          forecastStatus == forecast_status.APPROVED
            ? roundAmount(element[name])
            : roundAmount(element[`actual_${name}`] || 0);
        columnIndex++;

        /** --------------------------------------------------------------- Budget ----------------------------------------------------------------------- */
        const budgetColKey = `col${colIndex}`;
        const budgetValue =
          forecastStatus == forecast_status.APPROVED
            ? (totalData[`col${colIndex}`] ?? 0) +
            (element[`actual_${name}`] || 0)
            : (totalData[`col${colIndex}`] ?? 0) + element[name];
        const budgetrawValue =
          forecastStatus == forecast_status.APPROVED
            ? element[`actual_${name}`] || 0
            : element[name];
        totalData[budgetColKey] = budgetValue;

        /** check if previous data exist then add new key for old data and set previous data value */
        getBudgetPreviousData &&
          (rowData[`col${colIndex}_old`] = getBudgetPreviousData[name]
            ? roundAmount(getBudgetPreviousData[name]).toFixed(2)
            : null);

        rowData[`col${colIndex++}`] =
          forecastStatus == forecast_status.APPROVED
            ? roundAmount(element[`actual_${name}`] || 0).toFixed(2)
            : roundAmount(element[name]).toFixed(2);
        content =
          forecastStatus == forecast_status.APPROVED
            ? `Act ${moment(financialDates.current.startYear).format("YY")}-${moment(financialDates.current.endYear).format("YY")}`
            : `Budget`;
        columnsGroup[columnIndex] = await buildColumnGroup(
          columnIndex,
          content,
          element,
          0,
          1,
          "area",
          1,
          0,
          null,
        );
        totalFyBudget +=
          forecastStatus == forecast_status.APPROVED
            ? roundAmount(element[`actual_${name}`] || 0)
            : roundAmount(element[name]);
        columnIndex++;

        /** --------------------------------------------------------- percentage/diffrenece ------------------------------------------------------------- */
        const percentageColKey = `col${colIndex}`;
        const percentage: any =
          budgetrawValue == 0
            ? "0.00"
            : ((budgetrawValue - forecastrawValue) / budgetrawValue) * 100;
        totalData[percentageColKey] =
          budgetValue == 0
            ? "0.00"
            : roundAmount(
              ((budgetValue - forecastValue) / budgetValue) * 100,
            ).toFixed(2);
        rowData[`col${colIndex++}`] = roundAmount(percentage).toFixed(2);
        content = `%`;
        columnsGroup[columnIndex] = await buildColumnGroup(
          columnIndex,
          content,
          element,
          0,
          0,
          "line",
          0,
          0,
          null,
        );
        columnIndex++;
      }
      /** check if column index is last then add total column *
      /** add forecast total group column */
      let content =
        forecastStatus == forecast_status.APPROVED
          ? `Budget ${moment(financialDates.current.startYear).format("YY")}-${moment(financialDates.current.endYear).format("YY")}`
          : `Act ${moment(financialDates.previous.startYear).format("YY")}`;
      const forecastSum = roundAmount(
        (totalData[`col${colIndex}`] ?? 0) + totalForecast,
      );
      totalData[`col${colIndex}`] = roundAmount(
        (totalData[`col${colIndex}`] ?? 0) + totalForecast,
      );
      rowData[`col${colIndex++}`] = totalForecast.toFixed(2);
      element.month_name = "Total";
      columnsGroup[columnIndex] = await buildColumnGroup(
        columnIndex,
        content,
        element,
        0,
        0,
        element.forecast_type !== forecast_type.BUGDET ? "line" : "area",
        element.forecast_type !== forecast_type.BUGDET ? 1 : 0,
        0,
        null,
      );
      columnIndex++;

      /** add fy budget total group column */
      content =
        forecastStatus == forecast_status.APPROVED
          ? `Act ${moment(financialDates.current.startYear).format("YY")}-${moment(financialDates.current.endYear).format("YY")}`
          : `Budget`;
      const budgetSum = roundAmount(
        (totalData[`col${colIndex}`] ?? 0) + totalFyBudget,
      );
      totalData[`col${colIndex}`] = roundAmount(
        (totalData[`col${colIndex}`] ?? 0) + totalFyBudget,
      );
      rowData[`col${colIndex++}`] = totalFyBudget.toFixed(2);
      columnsGroup[columnIndex] = await buildColumnGroup(
        columnIndex,
        content,
        element,
        0,
        1,
        "area",
        1,
        0,
        null,
      );
      columnIndex++;

      /** add fy budget total group column */
      const percentage =
        totalFyBudget == 0
          ? 0.0
          : ((totalFyBudget - totalForecast) / totalFyBudget) * 100;
      content = `% `;
      totalData[`col${colIndex}`] =
        budgetSum == 0
          ? 0.0
          : roundAmount(((budgetSum - forecastSum) / budgetSum) * 100).toFixed(
            2,
          );
      rowData[`col${colIndex++}`] = roundAmount(percentage).toFixed(2) || 0.0;
      columnsGroup[columnIndex] = await buildColumnGroup(
        columnIndex,
        content,
        element,
        0,
        1,
        "area",
        1,
        0,
        null,
      );
      columnIndex++;

      element.month_name = "Target";
      totalData[`col${colIndex}`] = roundAmount(
        (totalData[`col${colIndex}`] ?? 0) + element.bugdet_target_amount,
      );

      /** check if previous data exist then add new key for old data and set previous bugdet_target_amount value */
      getBudgetPreviousData &&
        (rowData[`col${colIndex}_old`] =
          getBudgetPreviousData.bugdet_target_amount
            ? roundAmount(getBudgetPreviousData.bugdet_target_amount).toFixed(2)
            : null);

      rowData[`col${colIndex++}`] = roundAmount(
        element.bugdet_target_amount,
      ).toFixed(2);
      content = `Target`;
      columnsGroup[columnIndex] = await buildColumnGroup(
        columnIndex,
        content,
        element,
        0,
        1,
        "area",
        1,
        0,
        null,
      );
      columnIndex++;
      if (
        element.forecast_category_status === forecast_category_status.COMBINED
      ) {
        // Merge rowData into combinedData
        Object.assign(combinedData, rowData);
        // Remove rowData reference
        rowData = null;
      }
      const combinedRowData = { ...subrowData, ...rowData };
      // Check if combinedRowData is not empty before pushing
      if (Object.keys(combinedRowData).length !== 0) {
        result.push(combinedRowData);
      }
    }
    result.push(totalData);

    const columnsArray: any = Object.values(columnsGroup);
    // const series: any = [];

    const groupedColumns: any = [];
    const categoryMap: any = {};
    let totalCols = 1;
    columnsArray.forEach((column: any) => {
      const { group_type, ...columnData } = column;

      // Initialize category group if not already present
      if (!categoryMap[group_type]) {
        categoryMap[group_type] = [];
      }
      categoryMap[group_type].push(columnData);
      totalCols++;

      // if (format_type && totalCols > 2) {
      //   series.push({
      //     type: column.chart_type,
      //     xKey: columnsArray[0].key,
      //     yKey: column.key,
      //     yName: column.content,
      //     ...column.chart_details
      //   })
      // }
    });

    // Now process each category type to decide whether to create a group
    Object.keys(categoryMap).forEach((group_type) => {
      const categoryColumns = categoryMap[group_type];
      if (categoryColumns.length > 1) {
        const group = {
          id: groupedColumns.length + totalCols, // Incrementing id, assuming these IDs are being sequentially assigned
          content: categoryColumns[0].category_name,
          parent_id: null,
          type: "group",
          group_type: group_type,
          payment_type_id: categoryColumns[0].payment_type_id,
          forecast_category_type: categoryColumns[0].forecast_category_type,
          children: categoryColumns.map((column: any) => ({
            ...column,
            parent_id: groupedColumns.length + totalCols, // Assign parent_id to the new group
          })),
        };
        groupedColumns.push(group);
      } else {
        // If there's only one item in the category, don't create a group, just add the column directly
        groupedColumns.push(...categoryColumns);
      }
    });

    const reportData = {
      columnsArray: columnsArray,
      columns_group: groupedColumns,
      data: result,
    };

    return reportData;
  } catch (e: any) {
    console.log(e);
  }
};

async function buildColumnGroup(
  columnIndex: number,
  content: any,
  element: any,
  is_percentage: number,
  is_editable: number,
  chart_type: any = null,
  is_budget: number,
  is_actual: number,
  chart_details: any = null,
) {
  const paymentTypeData = await PaymentType.findOne({
    where: { id: element.payment_type_id },
    attributes: ["has_field_currency"],
    raw: true,
  });
  // Determine category name and payment type title for report
  const group_type = element.month_name;
  const category_name = element.month_name;

  return {
    id: columnIndex,
    content: content,
    parent_id: null,
    type: "text",
    key: `col${columnIndex}`,
    has_field_currency:
      (paymentTypeData && paymentTypeData.has_field_currency) ||
        element.forecast_category_type === forecast_category_type.INCOME
        ? 1
        : 0,
    // is_percentage: is_percentage,
    // is_editable: is_editable,
    group_type: group_type,
    category_name: category_name,
    // percentage_value: is_percentage ? `col${ columnIndex + 1 } ` : null,
    payment_type_id: element.payment_type_id,
    payment_type_category_id: element.payment_type_category_id,
    // forecast_category_type: element.forecast_category_type,
    // chart_type: chart_type,
    // is_budget: is_budget,
    // is_actual: is_actual,
    // chart_details: chart_details,
  };
}

async function paymentCategoryData(
  forecast_id: any = null,
  whereQry: any,
  forecast_history_id: any = null,
  branch_id: any = null,
) {
  let getPastReportQuery = ``;
  if (forecast_history_id) {
    getPastReportQuery = `
    SELECT
     CASE
        WHEN fbd.forecast_category_type = 'income' THEN 1
        WHEN fbd.forecast_category_type = 'other' THEN 2
        ELSE 3
    END AS category_order,
     ${whereQry}
    FROM nv_forecast_bugdet_data fbdh
    LEFT JOIN nv_forecast_bugdet_data_history AS fbd ON fbd.payment_type_id = fbdh.payment_type_id AND fbd.payment_type_category_id = fbdh.payment_type_category_id AND fbd.forecast_category_type = fbdh.forecast_category_type
    LEFT JOIN nv_payment_type pt ON pt.id = fbd.payment_type_id
    LEFT JOIN nv_payment_type_category pm ON pm.id = fbd.payment_type_category_id
    LEFT JOIN nv_forecast f ON f.id = fbd.forecast_id
    LEFT JOIN (
      SELECT
          x.start_date,
          x.end_date,
          x.branch_id,
          x.amount,
          x.payment_type_category_id,
          x.payment_type_id
      FROM (
          SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id,
                 di.dsr_amount AS amount, di.payment_type_category_id, ptc.payment_type_id
          FROM nv_dsr_items AS di
          JOIN nv_dsr_details AS dd ON dd.id = di.dsr_detail_id
          JOIN nv_payment_type_category AS ptc ON ptc.id = di.payment_type_category_id
          JOIN nv_payment_type AS pt ON pt.id = ptc.payment_type_id
          JOIN nv_payment_type_category_branch AS ptcb
            ON ptcb.payment_type_category_id = di.payment_type_category_id AND ptcb.branch_id = dd.branch_id AND ptcb.parent_id IS NULL
          WHERE di.dsr_item_status = 'active' AND dd.dsr_detail_status = 'active'

          UNION ALL

          SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id,
                 wi.wsr_amount AS amount, wi.payment_type_category_id, ptc.payment_type_id
          FROM nv_wsr_items AS wi
          JOIN nv_wsr_details AS wd ON wd.id = wi.wsr_detail_id
          JOIN nv_payment_type_category AS ptc ON ptc.id = wi.payment_type_category_id
          JOIN nv_payment_type AS pt ON pt.id = ptc.payment_type_id
          JOIN nv_payment_type_category_branch AS ptcb
            ON ptcb.payment_type_category_id = wi.payment_type_category_id AND ptcb.branch_id = wd.branch_id AND ptcb.parent_id IS NULL
          WHERE wi.wsr_item_status = 'active' AND wd.wsr_detail_status = 'active'

          UNION ALL

            SELECT DATE_FORMAT(CONCAT(ed.expense_year, '-', ed.expense_month, '-01'), '%Y-%m-01') AS start_date,
                 LAST_DAY(CONCAT(ed.expense_year, '-', ed.expense_month, '-01')) AS end_date, ed.branch_id AS branch_id,
                 ei.expense_amount AS amount, ei.payment_type_category_id, ptc.payment_type_id
          FROM nv_expense_items AS ei
          JOIN nv_expense_details AS ed ON ed.id = ei.expense_detail_id
          JOIN nv_payment_type_category AS ptc ON ptc.id = ei.payment_type_category_id
          JOIN nv_payment_type AS pt ON pt.id = ptc.payment_type_id
          JOIN nv_payment_type_category_branch AS ptcb
            ON ptcb.payment_type_category_id = ei.payment_type_category_id AND ptcb.branch_id = ed.branch_id AND ptcb.parent_id IS NULL
          WHERE ei.expense_item_status = 'active' AND ed.expense_detail_status = 'active'
        ) x
      ) a ON a.branch_id = f.branch_id AND a.payment_type_id = fbd.payment_type_id
        AND FIND_IN_SET(a.payment_type_category_id, fbd.payment_type_category_id) > 0
      WHERE
         f.branch_id IN (${branch_id}) AND fbd.forecast_history_id = ${forecast_history_id}  AND fbd.forecast_bugdet_data_history_status != '${forecast_budget_data_history_status.INACTIVE}'  AND fbd.forecast_id = ${forecast_id}
      GROUP BY fbd.forecast_category_type, fbd.payment_type_id, fbd.payment_type_category_id
      ORDER BY category_order ASC, fbd.payment_type_id ASC, fbd.payment_type_category_id ASC`;
  } else {
    getPastReportQuery = `
    SELECT
     CASE
        WHEN fbd.forecast_category_type = 'income' THEN 1
        WHEN fbd.forecast_category_type = 'other' THEN 2
        ELSE 3
    END AS category_order,
     ${whereQry}
    FROM nv_forecast_bugdet_data fbd
    LEFT JOIN nv_payment_type pt ON pt.id = fbd.payment_type_id
    LEFT JOIN nv_payment_type_category pm ON pm.id = fbd.payment_type_category_id
    LEFT JOIN nv_forecast f ON f.id = fbd.forecast_id
    LEFT JOIN (
      SELECT
          x.start_date,
          x.end_date,
          x.branch_id,
          x.amount,
          x.payment_type_category_id,
          x.payment_type_id
      FROM (
          SELECT dd.dsr_date AS start_date, dd.dsr_date AS end_date, dd.branch_id AS branch_id,
                 di.dsr_amount AS amount, di.payment_type_category_id, ptc.payment_type_id
          FROM nv_dsr_items AS di
          JOIN nv_dsr_details AS dd ON dd.id = di.dsr_detail_id
          JOIN nv_payment_type_category AS ptc ON ptc.id = di.payment_type_category_id
          JOIN nv_payment_type AS pt ON pt.id = ptc.payment_type_id
          JOIN nv_payment_type_category_branch AS ptcb
            ON ptcb.payment_type_category_id = di.payment_type_category_id AND ptcb.branch_id = dd.branch_id AND ptcb.parent_id IS NULL
          WHERE di.dsr_item_status = 'active' AND dd.dsr_detail_status = 'active'

          UNION ALL

          SELECT wd.wsr_start_date AS start_date, wd.wsr_end_date AS end_date, wd.branch_id AS branch_id,
                 wi.wsr_amount AS amount, wi.payment_type_category_id, ptc.payment_type_id
          FROM nv_wsr_items AS wi
          JOIN nv_wsr_details AS wd ON wd.id = wi.wsr_detail_id
          JOIN nv_payment_type_category AS ptc ON ptc.id = wi.payment_type_category_id
          JOIN nv_payment_type AS pt ON pt.id = ptc.payment_type_id
          JOIN nv_payment_type_category_branch AS ptcb
            ON ptcb.payment_type_category_id = wi.payment_type_category_id AND ptcb.branch_id = wd.branch_id AND ptcb.parent_id IS NULL
          WHERE wi.wsr_item_status = 'active' AND wd.wsr_detail_status = 'active'

          UNION ALL

          SELECT DATE_FORMAT(CONCAT(ed.expense_year, '-', ed.expense_month, '-01'), '%Y-%m-01') AS start_date,
                 LAST_DAY(CONCAT(ed.expense_year, '-', ed.expense_month, '-01')) AS end_date, ed.branch_id AS branch_id,
                 ei.expense_amount AS amount, ei.payment_type_category_id, ptc.payment_type_id
          FROM nv_expense_items AS ei
          JOIN nv_expense_details AS ed ON ed.id = ei.expense_detail_id
          JOIN nv_payment_type_category AS ptc ON ptc.id = ei.payment_type_category_id
          JOIN nv_payment_type AS pt ON pt.id = ptc.payment_type_id
          JOIN nv_payment_type_category_branch AS ptcb
            ON ptcb.payment_type_category_id = ei.payment_type_category_id AND ptcb.branch_id = ed.branch_id AND ptcb.parent_id IS NULL
          WHERE ei.expense_item_status = 'active' AND ed.expense_detail_status = 'active'
        ) x
      ) a ON a.branch_id = f.branch_id AND a.payment_type_id = fbd.payment_type_id
        AND FIND_IN_SET(a.payment_type_category_id, fbd.payment_type_category_id) > 0
      WHERE
          fbd.forecast_bugdet_data_status != '${forecast_budget_data_status.INACTIVE}'
          AND fbd.forecast_id = ${forecast_id}
      GROUP BY fbd.forecast_category_type, fbd.payment_type_id, fbd.payment_type_category_id
      ORDER BY category_order ASC, fbd.payment_type_id ASC, fbd.payment_type_category_id ASC`;
  }
  const forecastBudgetPastData: any = await sequelize.query(getPastReportQuery, {
    type: QueryTypes.SELECT,
  });
  return forecastBudgetPastData;
}

async function generateWhereClause(datesArray: any) {
  const conditions = datesArray.map(
    (date: any) =>
      `SUM(CASE WHEN a.start_date BETWEEN '${date.startDate}' AND '${date.endDate}' OR a.end_date BETWEEN '${date.startDate}' AND '${date.endDate}' THEN a.amount ELSE 0 END) AS actual_${moment(date.startDate, "YYYY-MM-DD").format("MMMM")}`,
  );
  return conditions.join(",\n");
}

async function getforecastBudgetData(
  forecast_id: any = null,
  branch_id: any = null,
  forecastYear: any = null,
  category_type: any = null,
  format_type: any = null,
  payment_type_id: any = null,
  user_role: any = null,
  forecast_history_id: any = null,
  payment_type_category_id: any = null,
  view_type: any = null,
  forecast_budget_status: any = null,
) {
  // const getPaymenCategoryQuery = await paymentCategoryData(branch_id)
  let userCurrentRole: any = "";
  // Construct WHERE query based on category and format type
  let whereQry = "";
  let whereFilterQuery = "";
  let whereHistoryQuery = ``;
  whereQry += forecast_id
    ? ` AND fbd.forecast_id = ${forecast_id}`
    : ` AND f.branch_id IN(${branch_id}) AND f.forecast_year = '${forecastYear}'`;
  whereQry += category_type
    ? ` AND fbd.forecast_category_type = '${category_type}'`
    : "";
  whereQry +=
    format_type === "chart" && payment_type_id
      ? ` AND fbd.payment_type_id = '${payment_type_id}'`
      : format_type === "chart" && !payment_type_id
        ? ` AND fbd.payment_type_id IS NULL`
        : "";
  whereFilterQuery += payment_type_category_id
    ? ` AND fbd.payment_type_category_id IN(${payment_type_category_id})`
    : "";

  if (forecast_history_id) {
    if (forecast_budget_status) {
      whereHistoryQuery = ``;
    } else {
      whereHistoryQuery = ` AND fbd.forecast_bugdet_data_history_status != '${forecast_budget_data_history_status.INACTIVE}' ${whereQry}  AND f.forecast_status != '${forecast_status.DELETED}'`;
    }
  }

  if (forecast_id) {
    /** Get forecats budget last updatd data  userid */
    const getUpdatedUserId: any = await ForecastBugdetData.findOne({
      where: {
        forecast_id: forecast_id,
        forecast_bugdet_data_status: forecast_budget_data_status.DRAFT,
      },
      attributes: ["updated_by"],
      raw: true,
    });

    /** Get role of thet userId */
    if (getUpdatedUserId) {
      const getUser = await User.findOne({
        attributes: ["web_user_active_role_id"],
        where: { id: getUpdatedUserId.updated_by },
        raw: true,
      });
      if (getUser) {
        /** Get it's current role */
        const getCurrentRole = await Role.findOne({
          where: { id: getUser.web_user_active_role_id },
          attributes: ["role_name"],
          raw: true,
        });
        userCurrentRole = getCurrentRole?.role_name;
      }
    }
  }
  let selectWhere = `SUM(fbd.january_amount) AS January,
        SUM(fbd.february_amount) AS February,
        SUM(fbd.march_amount) AS March,
        SUM(fbd.april_amount) AS April,
        SUM(fbd.may_amount) AS May,
        SUM(fbd.june_amount) AS June,
        SUM(fbd.july_amount) AS July,
        SUM(fbd.august_amount) AS August,
        SUM(fbd.september_amount) AS September,
        SUM(fbd.october_amount) AS October,
        SUM(fbd.november_amount) AS November,
        SUM(fbd.december_amount) AS December,`;

  /** Modify condition, if director update anything as a draft, only then can view draft value, same for BM also */
  if (user_role !== userCurrentRole && !forecast_history_id) {
    const forecastStatusField = forecast_history_id
      ? "fbd.forecast_bugdet_data_history_status"
      : "fbd.forecast_bugdet_data_status";
    selectWhere = `SUM(IF(${forecastStatusField} = 'draft', 0, fbd.january_amount)) AS January,
        SUM(IF(${forecastStatusField} = 'draft', 0, fbd.february_amount)) AS February,
        SUM(IF(${forecastStatusField} = 'draft', 0, fbd.march_amount)) AS March,
        SUM(IF(${forecastStatusField} = 'draft', 0, fbd.april_amount)) AS April,
        SUM(IF(${forecastStatusField} = 'draft', 0, fbd.may_amount)) AS May,
        SUM(IF(${forecastStatusField} = 'draft', 0, fbd.june_amount)) AS June,
        SUM(IF(${forecastStatusField} = 'draft', 0, fbd.july_amount)) AS July,
        SUM(IF(${forecastStatusField} = 'draft', 0, fbd.august_amount)) AS August,
        SUM(IF(${forecastStatusField} = 'draft', 0, fbd.september_amount)) AS September,
        SUM(IF(${forecastStatusField} = 'draft', 0, fbd.october_amount)) AS October,
        SUM(IF(${forecastStatusField} = 'draft', 0, fbd.november_amount)) AS November,
        SUM(IF(${forecastStatusField} = 'draft', 0, fbd.december_amount)) AS December,`;
  }

  // Construct the SQL query to fetch forecast budget data
  let getForecastBudgetQuery: any = "";
  if (forecast_history_id) {
    getForecastBudgetQuery = `
      SELECT
        fbd.forecast_category_type,
         ${selectWhere}
        GROUP_CONCAT(fbd.forecast_category_status) AS forecast_category_status,
        pt.payment_type_title,
        CASE
            WHEN fbd.forecast_category_type = 'income' THEN 1
            WHEN fbd.forecast_category_type = 'other' THEN 2
            ELSE 3
        END AS category_order,
        fbd.payment_type_id,
        fbd.payment_type_category_id,
        pm.payment_type_category_title AS payment_type_category_title,
        pt.has_field_currency,
        (CASE
            WHEN ${view_type} = 1 THEN SUM(fbd.bugdet_target_amount)
            ELSE MAX(fbd.bugdet_target_amount)
        END) AS bugdet_target_amount
      FROM nv_forecast_bugdet_data fbdh
      LEFT JOIN nv_forecast_bugdet_data_history AS fbd ON fbd.payment_type_id = fbdh.payment_type_id AND fbd.payment_type_category_id = fbdh.payment_type_category_id AND fbd.forecast_category_type = fbdh.forecast_category_type ${whereQry}
      LEFT JOIN
          nv_payment_type pt
          ON pt.id = fbd.payment_type_id
      LEFT JOIN
          nv_payment_type_category pm
          ON pm.id = fbd.payment_type_category_id
      LEFT JOIN
          nv_forecast f
          ON f.id = fbd.forecast_id
      WHERE f.branch_id IN (${branch_id}) AND fbd.forecast_history_id = ${forecast_history_id} ${whereHistoryQuery}
          GROUP BY fbd.forecast_category_type, fbd.payment_type_id, fbd.payment_type_category_id
          ORDER BY category_order ASC, fbd.payment_type_id ASC, fbd.payment_type_category_id ASC`;
  } else {
    getForecastBudgetQuery = `
    SELECT
        fbd.forecast_category_type,
         ${selectWhere}
        GROUP_CONCAT(fbd.forecast_category_status) AS forecast_category_status,
        pt.payment_type_title,
        CASE
            WHEN fbd.forecast_category_type = 'income' THEN 1
            WHEN fbd.forecast_category_type = 'other' THEN 2
            ELSE 3
        END AS category_order,
        fbd.payment_type_id,
        fbd.payment_type_category_id,
        pm.payment_type_category_title AS payment_type_category_title,
        pt.has_field_currency,
        (CASE
            WHEN ${view_type} = 1 THEN SUM(fbd.bugdet_target_amount)
            ELSE MAX(fbd.bugdet_target_amount)
        END) AS bugdet_target_amount
     FROM
        nv_forecast_bugdet_data fbd
    LEFT JOIN
        nv_payment_type pt
        ON pt.id = fbd.payment_type_id
    LEFT JOIN
        nv_payment_type_category pm
        ON pm.id = fbd.payment_type_category_id
    LEFT JOIN
        nv_forecast f
        ON f.id = fbd.forecast_id
    WHERE
        fbd.forecast_bugdet_data_status != '${forecast_budget_data_status.INACTIVE}'
        ${whereQry} AND f.forecast_status != '${forecast_status.DELETED}' ${whereFilterQuery}
        GROUP BY fbd.forecast_category_type, fbd.payment_type_id, fbd.payment_type_category_id
        ORDER BY category_order ASC, fbd.payment_type_id ASC, fbd.payment_type_category_id ASC`;
  }
  // Execute the query and get forecast budget data
  const forecastBudgetData: any = await sequelize.query(
    getForecastBudgetQuery,
    {
      type: QueryTypes.SELECT,
    },
  );
  return forecastBudgetData;
}

/**
 * Rounds a given amount to a specified decimal length.
 */
function roundAmount(amount: number, length: number = 2) {
  const factor = Math.pow(10, length); // Calculate factor for the desired decimal length
  const scaledAmount = amount * factor; // Scale the amount by the factor

  if (scaledAmount % 1 >= 0.4) {
    // Round up if the decimal part is 0.4 or above
    return Math.ceil(scaledAmount) / factor;
  } else {
    // Round down otherwise
    return Math.floor(scaledAmount) / factor;
  }
}

// Utility function to get categorized leave requests
const getLeaveRequestsByDateRanges = async (
  req: any,
  type: any,
  list_type: any,
  startDate: Date,
  endDate: Date,
  months?: any,
  year?: number,
  search?: any,
) => {
  // Initialize moments based on provided startDate
  const startMoment = moment(startDate);
  const endMoment = moment(endDate);

  // Determine effective year and months based on input
  let effectiveYear = year;
  let effectiveMonths = months;

  // Handle different input combinations
  if (effectiveYear && !effectiveMonths?.length) {
    // Case: Year provided without months - use all months of the year
    effectiveMonths = Array.from({ length: 12 }, (_, i) => i + 1);
  } else if (effectiveMonths?.length && !effectiveYear) {
    // Case: Months provided without year - use current year
    effectiveYear = moment().year();
  }

  // Create month ranges using FINAL effective values
  const monthRanges = effectiveMonths?.map((month: number) => ({
    startOfMonth: moment({ year: effectiveYear, month: month - 1 })
      .startOf("month")
      .toDate(),
    endOfMonth: moment({ year: effectiveYear, month: month - 1 })
      .endOf("month")
      .toDate(),
  }));

  // Calculate date ranges
  const baseConditions = [];
  const baseHolidayConditions = [];
  const customDateRange = {
    [Op.and]: [
      { start_date: { [Op.lte]: endMoment.endOf("day").toDate() } },
      { end_date: { [Op.gte]: startMoment.startOf("day").toDate() } },
    ],
  };
  const customHolidayDateRange = {
    [Op.and]: [
      {
        holiday_policy_start_date: {
          [Op.lte]: endMoment.endOf("day").toDate(),
        },
      },
      {
        holiday_policy_end_date: {
          [Op.gte]: startMoment.startOf("day").toDate(),
        },
      },
    ],
  };

  // Add custom date range filter if dates are valid
  if (startDate && endDate) {
    baseConditions.push(customDateRange);
  }
  // Add custom date range filter if dates are valid
  if (startDate && endDate) {
    baseHolidayConditions.push(customHolidayDateRange);
  }

  // Fetch user details once
  const user = await User.findOne({
    attributes: ["id", "web_user_active_role_id", "user_active_role_id", "user_role_id", "branch_id", "organization_id"],
    where: { id: req.user.id },
    raw: true
  });
  if (!user) return {};

  // Get user role - use MORole system for web platform if user_role_id exists
  let userRole: any = null;
  const platformType = req.headers["platform-type"] as string;

  if (user.user_role_id && platformType === "web") {
    // Use MORole system for web platform
    userRole = await MORole.findOne({
      attributes: ['id', 'role_name'],
      where: {
        id: user.user_role_id,
        organization_id: user.organization_id,
        role_status: 'active'
      },
      raw: true
    });
  } else {
    // Fallback to old Role system
    const roleId = platformType === "web" ? user.web_user_active_role_id : user.user_active_role_id;
    userRole = await Role.findOne({
      attributes: ['id', 'role_name'],
      where: { id: roleId },
      raw: true
    });
  }

  // Base query conditions
  const whereLeaveConditions: any = {
    [Op.or]: [...baseConditions],
    [Op.and]: [],
  };

  const whereHolidayConditions: any = {
    [Op.or]: [...baseHolidayConditions],
    [Op.and]: [
      sequelize.literal(`((
                  SELECT COUNT(*)
                  FROM nv_user_holiday_policy AS uhp
                  WHERE uhp.holiday_policy_id = HolidayPolicy.id
                  AND uhp.user_holiday_policy_status = '${user_holiday_policy_status.ACTIVE}'
              )) > 0`),
    ],
    holiday_policy_status: holiday_policy_status.ACTIVE,
  };

  if (type === "calender") {
    // Modified month/year filtering section
    if (monthRanges?.length > 0) {
      monthRanges.forEach(({ startOfMonth, endOfMonth }: any) => {
        whereLeaveConditions[Op.or].push({
          [Op.and]: [
            { start_date: { [Op.lte]: endOfMonth } },
            { end_date: { [Op.gte]: startOfMonth } },
          ],
        });
        whereHolidayConditions[Op.or].push({
          [Op.and]: [
            { holiday_policy_start_date: { [Op.lte]: endOfMonth } },
            { holiday_policy_end_date: { [Op.gte]: startOfMonth } },
          ],
        });
      });
    }
  }

  // Common conditions
  if (list_type === "own") {
    whereLeaveConditions.from_user_id = req.user.id;

    whereHolidayConditions[Op.and].push(
      sequelize.literal(`((
                  SELECT COUNT(*)
                  FROM nv_user_holiday_policy AS uhp
                  WHERE uhp.holiday_policy_id = HolidayPolicy.id
                  AND uhp.user_holiday_policy_status = '${user_holiday_policy_status.ACTIVE}' AND uhp.user_id = ${req.user.id}
              )) > 0`),
    );
  } else if (list_type === "staff") {
    whereLeaveConditions.from_user_id = { [Op.not]: req.user.id };
  }
  // For list_type === "rota", no user-based filtering is applied - shows all leave requests

  // Handle manager role filtering (only for "staff" type, not for "own" or "rota")
  if (
    list_type === "staff" &&
    userRole &&
    (userRole.role_name == ROLE_CONSTANT.BRANCH_MANAGER ||
      userRole.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER ||
      userRole.role_name == ROLE_CONSTANT.HOTEL_MANAGER ||
      userRole.role_name == ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER ||
      userRole.role_name == ROLE_CONSTANT.AREA_MANAGER)
  ) {

    const findUserBranch = await UserBranch.findAll({
      where: {
        user_id: req.user.id,
      },
    });

    if (
      userRole.role_name == ROLE_CONSTANT.AREA_MANAGER &&
      findUserBranch.length > 0
    ) {
      whereLeaveConditions[Op.and] = [
        Sequelize.where(Sequelize.col("request_from_users.branch_id"), {
          [Op.in]: findUserBranch.map((branch: any) => branch.branch_id),
        }),
      ];
      const findBranchUser = await User.findAll({
        attributes: ["id", "branch_id"],
        where: {
          branch_id: {
            [Op.in]: findUserBranch.map((branch: any) => branch.branch_id),
          },
        },
        raw: true
      });
      if (findBranchUser.length > 0) {
        const findOwnHolidayPolicy = await UserHolidayPolicy.findAll({
          where: {
            user_id: { [Op.in]: findBranchUser.map((user: any) => user.id) },
            user_holiday_policy_status: user_holiday_policy_status.ACTIVE,
          },
        });
        if (findOwnHolidayPolicy.length > 0) {
          whereHolidayConditions.id = {
            [Op.in]: findOwnHolidayPolicy.map(
              (policy: any) => policy.holiday_policy_id,
            ),
          };
        }
      }
    } else {
      whereLeaveConditions[Op.and] = [
        Sequelize.where(Sequelize.col("request_from_users.branch_id"), {
          [Op.eq]: user.branch_id,
        }),
      ];
      const findBranchUser = await User.findAll({
        attributes: ["id"],
        where: { branch_id: user?.branch_id }, raw: true
      });
      if (findBranchUser.length > 0) {
        const findOwnHolidayPolicy = await UserHolidayPolicy.findAll({
          where: {
            user_id: { [Op.in]: findBranchUser.map((user) => user.id) },
            user_holiday_policy_status: user_holiday_policy_status.ACTIVE,
          },
        });
        if (findOwnHolidayPolicy.length > 0) {
          whereHolidayConditions.id = {
            [Op.in]: findOwnHolidayPolicy.map(
              (policy: any) => policy.holiday_policy_id,
            ),
          };
        }
      }
    }

    if (user.user_role_id && platformType === "web") {
      // Use MORole system for recursive role query
      const getChildRolesQuery = `
          WITH RECURSIVE ChildRoles AS (
            SELECT id, role_name, parent_role_id
            FROM mo_roles
            WHERE id = :activeRoleId AND organization_id = :organizationId
            UNION ALL
            SELECT r.id, r.role_name, r.parent_role_id
            FROM mo_roles r
            INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
            WHERE r.organization_id = :organizationId
          )
          SELECT id
          FROM ChildRoles
          WHERE id != :activeRoleId
        `;

      // Execute recursive query to find child roles
      const getChildRoles = await sequelize.query(getChildRolesQuery, {
        replacements: {
          activeRoleId: user.user_role_id,
          organizationId: user.organization_id
        },
        type: QueryTypes.SELECT,
      });

      // Build WHERE clause for user roles based on child roles (MORole system)
      let whereStr = "";
      getChildRoles.forEach((child_role: any, index: number) => {
        if (index > 0) {
          whereStr += " OR ";
        }
        whereStr += `request_from_users.user_role_id = ${child_role.id}`;
      });

      if (whereStr) {
        whereLeaveConditions[Op.and].push(sequelize.literal(`(${whereStr})`));
      }
    } else {
      // Fallback to old Role system for recursive role query
      const getChildRolesQuery = `
          WITH RECURSIVE ChildRoles AS (
            SELECT id, role_name, parent_role_id
            FROM nv_roles
            WHERE id = :activeRoleId
            UNION ALL
            SELECT r.id, r.role_name, r.parent_role_id
            FROM nv_roles r
            INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
          )
          SELECT id
          FROM ChildRoles
          WHERE id != :activeRoleId
        `;

      // Execute recursive query to find child roles
      const getChildRoles = await sequelize.query(getChildRolesQuery, {
        replacements: { activeRoleId: user?.web_user_active_role_id },
        type: QueryTypes.SELECT,
      });

      // Build WHERE clause for user roles based on child roles (old system)
      let whereStr = "";
      getChildRoles.forEach((child_role: any, index: number) => {
        if (index > 0) {
          whereStr += " OR ";
        }
        whereStr += `FIND_IN_SET(${child_role.id}, (SELECT GROUP_CONCAT(role_id) FROM nv_user_roles WHERE user_id = request_from_users.id)) > 0`;
      });

      if (whereStr) {
        whereLeaveConditions[Op.and].push(sequelize.literal(`(${whereStr})`));
      }
    }
  }

  // Search functionality
  if (search) {
    whereLeaveConditions[Op.or] = [
      Sequelize.where(
        Sequelize.fn(
          "concat",
          Sequelize.col("request_from_users.user_first_name"),
          " ",
          Sequelize.col("request_from_users.user_last_name"),
        ),
        { [Op.like]: `%${search}%` },
      ),
      { request_reason: { [Op.like]: `%${search}%` } },
    ];
  }

  // Fetch data
  const [leaveRequests, holidays] = await Promise.all([
    UserRequest.findAll({
      include: [
        {
          model: User,
          as: "request_from_users",
          attributes: [
            "id",
            [
              sequelize.fn(
                "concat",
                sequelize.col("request_from_users.user_first_name"),
                " ",
                sequelize.col("request_from_users.user_last_name"),
              ),
              "user_full_name",
            ],
            "user_email",
            [
              sequelize.literal(
                `IF((request_from_users.user_avatar IS NOT NULL AND request_from_users.user_avatar != ''), CONCAT('${global.config.API_BASE_URL}user_avatars/', request_from_users.user_avatar), '')`,
              ),
              "user_avatar_link",
            ],
            "employment_number",
          ],
          include: [
            {
              model: Branch,
              as: "branch",
              attributes: ["id", "branch_name"],
              where: { branch_status: branch_status.ACTIVE },
              required: false,
            },
            {
              model: Department,
              as: "department",
              attributes: ["department_name"],
              where: { department_status: department_status.ACTIVE },
              required: false,
            },
            {
              model: Role,
              as: "role",
              attributes: ["role_name"],
              where: { role_status: role_status.ACTIVE },
              required: false,
            },
          ],
          where: { organization_id: req.user.organization_id },
          required: true,
        },
        {
          model: User,
          as: "request_approved_users",
          attributes: [
            "id",
            [
              sequelize.fn(
                "concat",
                sequelize.col("request_approved_users.user_first_name"),
                " ",
                sequelize.col("request_approved_users.user_last_name"),
              ),
              "user_full_name",
            ],
            "employment_number",
          ],
        },
        {
          model: MORole,
          as: "leave_role_request",
          attributes: ["id", "role_name"],
        },
        {
          model: LeaveTypeModel,
          as: "leave_request_type_list",
          attributes: ["id", "name", "leave_type_color"],
          required: false,
        },
      ],
      where: whereLeaveConditions,
    }),
    HolidayPolicy.findAll({
      where: whereHolidayConditions,
      include: {
        model: HolidayType,
        as: "holiday_policy",
        where: {
          holiday_type_status: holiday_type_status.ACTIVE,
          organization_id: req.user.organization_id,
        },
      },
    }),
  ]);

  // Post-processing
  const result: any = { calenderLeaves: [], holidayList: [] };
  if (type === "calender") {
    result.calenderLeaves = leaveRequests;
    result.holidayList = holidays;
  } else if (type === "dashboard") {
    // Categorization logic here if needed
  }

  return result;
};

const validateLeavePeriod = async (leaveData: any, leaveRules: any) => {
  const { start_date, end_date } = leaveData;
  const { leave_rule_start_date, leave_rule_end_date } = leaveRules;

  // Parse dates using Moment.js
  const leaveStartDate = moment(start_date, "YYYY-MM-DD", true);
  const leaveEndDate = moment(end_date, "YYYY-MM-DD", true);
  const ruleStartDate = moment(leave_rule_start_date, "YYYY-MM-DD", true);
  const ruleEndDate = moment(leave_rule_end_date, "YYYY-MM-DD", true);

  // Common error message
  const errorMessage = "Leave period exceeds the allowed leave rule period.";

  // Check for invalid dates
  if (
    !leaveStartDate.isValid() ||
    !leaveEndDate.isValid() ||
    !ruleStartDate.isValid() ||
    !ruleEndDate.isValid()
  ) {
    return { isValid: false, message: errorMessage };
  }

  // Validation conditions
  if (leaveStartDate.isAfter(leaveEndDate)) {
    return { isValid: false, message: errorMessage };
  }

  if (
    leaveStartDate.isBefore(ruleStartDate) ||
    leaveEndDate.isAfter(ruleEndDate)
  ) {
    return { isValid: false, message: errorMessage };
  }

  return { isValid: true };
};

const validateBlackoutDates = async (leaveData: any, leaveRules: any) => {
  const { start_date, end_date } = leaveData;
  const { blackout_dates } = leaveRules;

  // Parse leave start and end dates
  const leaveStartDate = moment(start_date, "YYYY-MM-DD", true);
  const leaveEndDate = moment(end_date, "YYYY-MM-DD", true);

  // Check if the dates are valid
  if (!leaveStartDate.isValid() || !leaveEndDate.isValid()) {
    return { isValid: false, message: "Invalid leave dates." };
  }

  // Check if the leave falls within any blackout date range
  const isBlackoutDate = blackout_dates.some((blackoutDate: any) => {
    const blackoutStart = moment(blackoutDate.start, "YYYY-MM-DD", true);
    const blackoutEnd = moment(blackoutDate.end, "YYYY-MM-DD", true);

    // Check if leave start or end falls within the blackout range
    return (
      leaveStartDate.isBetween(blackoutStart, blackoutEnd, null, "[)") ||
      leaveEndDate.isBetween(blackoutStart, blackoutEnd, null, "[)")
    );
  });

  if (isBlackoutDate) {
    return {
      isValid: false,
      message: "Leave request falls within the blackout dates.",
    };
  }

  return { isValid: true, message: "Leave period is valid." };
};

/** Get Super admin user Id */
const getSuperAdminUserId = async (organization_id: any) => {
  const superAdminUser: any = await User.findAll({ where: { user_active_role_id: 1, organization_id: organization_id }, attributes: ['id'], raw: true });
  return superAdminUser.map((user: any) => user.id); // Extract all user IDs
};

/**
 * Get Super Admin user IDs using MORole system
 * @param organization_id - Organization ID to filter users
 * @returns Promise<number[]> - Array of Super Admin user IDs
 */
const getSuperAdminUserIdMO = async (organization_id: any): Promise<number[]> => {
  try {
    // First, find the Super Admin MORole for this organization
    const superAdminRole = await MORole.findOne({
      where: {
        role_name: ROLE_CONSTANT.SUPER_ADMIN,
        organization_id: organization_id,
        role_status: 'active'
      },
      attributes: ['id']
    });

    if (!superAdminRole) {
      console.log("getSuperAdminUserIdMO: Super Admin MORole not found for organization");
      return [];
    }

    // Find all users with this Super Admin role
    const superAdminUsers = await User.findAll({
      attributes: ['id'],
      where: {
        organization_id: organization_id,
        user_role_id: superAdminRole.id
      },
      raw: true
    });

    return superAdminUsers.map((user: any) => user.id);
  } catch (error) {
    console.log("getSuperAdminUserIdMO error:", error);
    return [];
  }
};

/**
 * Enhanced Super Admin user ID getter that combines both old and new role systems
 * @param organization_id - Organization ID to filter users
 * @returns Promise<number[]> - Array of Super Admin user IDs from both systems
 */
const getSuperAdminUserIdEnhanced = async (organization_id: any): Promise<number[]> => {
  try {
    // Get Super Admin users from MORole system
    const moRoleUsers = await getSuperAdminUserIdMO(organization_id);

    // Get Super Admin users from old role system
    const oldRoleUsers = await getSuperAdminUserId(organization_id);

    // Combine and deduplicate user IDs
    const allSuperAdminUsers = [...new Set([...moRoleUsers, ...oldRoleUsers])];

    return allSuperAdminUsers;
  } catch (error) {
    console.log("getSuperAdminUserIdEnhanced error:", error);
    return [];
  }
};

/**
 * Get role names using MORole system (new implementation)
 * @param role_ids - Array of role IDs
 * @param organization_id - Organization ID for filtering
 * @returns Promise<string[]> - Array of role names
 */
const roleNameMO = async (role_ids: any, organization_id: string): Promise<string[]> => {
  try {
    if (!role_ids || !organization_id) {
      console.log("roleNameMO: Missing required parameters");
      return [];
    }

    const roles = await MORole.findAll({
      where: {
        id: { [Op.in]: role_ids },
        organization_id: organization_id,
        role_status: 'active'
      },
      attributes: ["id", "role_name"],
    });

    const roleMap = _.keyBy(roles, "id");
    return role_ids.map((id: string | number) => roleMap[id]?.role_name).filter(Boolean);
  } catch (error) {
    console.log("roleNameMO error:", error);
    return [];
  }
};

/**
 * Enhanced role name function that works with both old and new role systems
 * @param role_ids - Array of role IDs
 * @param organization_id - Organization ID for filtering (optional for old system)
 * @returns Promise<string[]> - Array of role names
 */
const roleNameEnhanced = async (role_ids: any, organization_id?: string): Promise<string[]> => {
  try {
    // If organization_id is provided, try new MORole system first
    if (organization_id) {
      const newSystemResult = await roleNameMO(role_ids, organization_id);
      if (newSystemResult && newSystemResult.length > 0) {
        return newSystemResult;
      }
    }

    // Fallback to old system
    return await roleName(role_ids) || [];
  } catch (error) {
    console.log("roleNameEnhanced error:", error);
    return [];
  }
};

/**
 * Get single role name using MORole system (new implementation)
 * @param role_id - Role ID
 * @param organization_id - Organization ID for filtering
 * @returns Promise<any> - Role data object
 */
const getRoleNameMO = async (role_id: any, organization_id: string): Promise<any> => {
  try {
    if (!role_id || !organization_id) {
      console.log("getRoleNameMO: Missing required parameters");
      return {};
    }

    const roleData = await MORole.findAll({
      where: {
        id: role_id,
        organization_id: organization_id,
        role_status: 'active'
      },
      attributes: ["id", "role_name"],
      raw: true,
      nest: true,
    });

    return roleData ? roleData : {};
  } catch (error) {
    console.log("getRoleNameMO error:", error);
    return {};
  }
};

/**
 * Enhanced get role name function that works with both old and new role systems
 * @param role_id - Role ID
 * @param organization_id - Organization ID for filtering (optional for old system)
 * @returns Promise<any> - Role data object
 */
const getRoleNameEnhanced = async (role_id: any, organization_id?: string): Promise<any> => {
  try {
    // If organization_id is provided, try new MORole system first
    if (organization_id) {
      const newSystemResult = await getRoleNameMO(role_id, organization_id);
      if (newSystemResult && Object.keys(newSystemResult).length > 0) {
        return newSystemResult;
      }
    }

    // Fallback to old system
    return await getRoleName(role_id) || {};
  } catch (error) {
    console.log("getRoleNameEnhanced error:", error);
    return {};
  }
};

/**
 * User create permission check using MORole system (new implementation)
 * @param user_id - User ID
 * @param organization_id - Organization ID for filtering
 * @returns Promise<boolean> - true if user has create permission, false otherwise
 */
const userCreatePermissionMO = async (user_id: any, organization_id: string): Promise<boolean> => {
  try {
    if (!user_id || !organization_id) {
      console.log("userCreatePermissionMO: Missing required parameters");
      return false;
    }

    const findUserCurrentRole = await User.findOne({
      where: {
        id: user_id,
        organization_id: organization_id,
        user_status: {
          [Op.notIn]: [user_status.PENDING, user_status.DELETED, user_status.CANCELLED]
        }
      },
      attributes: ['id', 'user_role_id', 'web_user_active_role_id', 'user_active_role_id'],
      raw: true,
    });

    if (!findUserCurrentRole) {
      return false;
    }

    // Prioritize user_role_id (MORole), then fallback to old roles
    const active_role_id = findUserCurrentRole.user_role_id || findUserCurrentRole.web_user_active_role_id || findUserCurrentRole.user_active_role_id;

    if (!active_role_id) {
      return false;
    }

    // Check if using MORole (user_role_id exists)
    if (findUserCurrentRole.user_role_id) {
      const getRoleName = await MORole.findOne({
        where: {
          id: active_role_id,
          organization_id: organization_id,
          role_status: 'active'
        },
        attributes: ["role_name"],
        raw: true,
      });

      const adminSideUser = [
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ];

      if (_.includes(adminSideUser, getRoleName?.role_name)) {
        return true;
      } else {
        return false;
      }
    } else {
      // Fallback to old system
      return await userCreatePermission(user_id) || false;
    }
  } catch (error) {
    console.log("userCreatePermissionMO error:", error);
    return false;
  }
};

/**
 * Enhanced user create permission check that works with both old and new role systems
 * @param user_id - User ID
 * @param organization_id - Organization ID for filtering (optional for old system)
 * @returns Promise<boolean> - true if user has create permission, false otherwise
 */
const userCreatePermissionEnhanced = async (user_id: any, organization_id?: string): Promise<boolean> => {
  try {
    // If organization_id is provided, try new MORole system first
    if (organization_id) {
      const newSystemResult = await userCreatePermissionMO(user_id, organization_id);
      if (newSystemResult) {
        return true;
      }
    }

    // Fallback to old system
    return await userCreatePermission(user_id) || false;
  } catch (error) {
    console.log("userCreatePermissionEnhanced error:", error);
    return false;
  }
};

/**
 * Permission for admin check using MORole system (new implementation)
 * @param role_id - Role ID
 * @param organization_id - Organization ID for filtering
 * @returns Promise<boolean> - true if role has admin permission, false otherwise
 */
const permissionForAdminMO = async (role_id: any, organization_id: string): Promise<boolean> => {
  try {
    if (!role_id || !organization_id) {
      console.log("permissionForAdminMO: Missing required parameters");
      return false;
    }

    const getRoleName = await MORole.findOne({
      where: {
        id: role_id,
        organization_id: organization_id,
        role_status: 'active'
      },
      attributes: ["role_name"],
      raw: true,
    });

    const adminSideUser = [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
      ROLE_CONSTANT.AREA_MANAGER,
      ROLE_CONSTANT.BRANCH_MANAGER,
      ROLE_CONSTANT.HOTEL_MANAGER,
    ];

    if (_.includes(adminSideUser, getRoleName?.role_name)) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.log("permissionForAdminMO error:", error);
    return false;
  }
};

/**
 * Enhanced permission for admin check that works with both old and new role systems
 * @param role_id - Role ID
 * @param organization_id - Organization ID for filtering (optional for old system)
 * @returns Promise<boolean> - true if role has admin permission, false otherwise
 */
const permissionForAdminEnhanced = async (role_id: any, organization_id?: string): Promise<boolean> => {
  try {
    // If organization_id is provided, try new MORole system first
    if (organization_id) {
      const newSystemResult = await permissionForAdminMO(role_id, organization_id);
      if (newSystemResult) {
        return true;
      }
    }

    // Fallback to old system
    return await permissionForAdmin(role_id) || false;
  } catch (error) {
    console.log("permissionForAdminEnhanced error:", error);
    return false;
  }
};

/**
 * Get admin staffs using MORole system (new implementation)
 * @param branch_id - Branch ID for filtering
 * @param login_user_id - Current user ID to exclude
 * @param role_id - Role ID to exclude (optional)
 * @param organization_id - Organization ID for filtering
 * @returns Promise<any[]> - Array of admin staff users
 */
const getAdminStaffsMO = async (
  branch_id?: any,
  login_user_id?: any,
  role_id: any = null,
  organization_id?: any,
): Promise<any[]> => {
  try {
    if (!organization_id) {
      console.log("getAdminStaffsMO: Missing organization_id");
      return [];
    }

    const roleWhere = role_id ? `AND mo_roles.id != ${role_id}` : "";

    const findUsers = await User.findAll({
      attributes: ['id', 'appToken', 'webAppToken', 'user_email'],
      where: {
        user_status: {
          [Op.notIn]: [user_status.PENDING, user_status.DELETED, user_status.CANCELLED]
        },
        id: {
          [Op.in]: [
            sequelize.literal(
              `(SELECT users.id from users
               INNER JOIN mo_roles ON users.user_role_id = mo_roles.id
               WHERE mo_roles.role_name IN ('${ROLE_CONSTANT.SUPER_ADMIN}','${ROLE_CONSTANT.ADMIN}','${ROLE_CONSTANT.DIRECTOR}','${ROLE_CONSTANT.HR}','${ROLE_CONSTANT.AREA_MANAGER}','${ROLE_CONSTANT.BRANCH_MANAGER}','${ROLE_CONSTANT.HOTEL_MANAGER}')
               AND mo_roles.organization_id = '${organization_id}'
               AND mo_roles.role_status = 'active'
               AND (
                 (mo_roles.role_name != '${ROLE_CONSTANT.BRANCH_MANAGER}' AND mo_roles.role_name != '${ROLE_CONSTANT.HOTEL_MANAGER}') OR
                 users.branch_id = ${branch_id}
               ) AND users.id != ${login_user_id} ${roleWhere}
               )`,
            ),
          ],
        },
        organization_id: organization_id,
      },
      raw: true,
      group: ["id"],
    }) || [];

    return findUsers;
  } catch (error) {
    console.log("getAdminStaffsMO error:", error);
    return [];
  }
};

/**
 * Enhanced get admin staffs function that works with both old and new role systems
 * @param branch_id - Branch ID for filtering
 * @param login_user_id - Current user ID to exclude
 * @param role_id - Role ID to exclude (optional)
 * @param organization_id - Organization ID for filtering (optional for old system)
 * @returns Promise<any[]> - Array of admin staff users
 */
const getAdminStaffsEnhanced = async (
  branch_id?: any,
  login_user_id?: any,
  role_id: any = null,
  organization_id?: any,
): Promise<any[]> => {
  try {
    // If organization_id is provided, try new MORole system first
    if (organization_id) {
      const newSystemResult = await getAdminStaffsMO(branch_id, login_user_id, role_id, organization_id);
      if (newSystemResult && newSystemResult.length > 0) {
        return newSystemResult;
      }
    }

    // Fallback to old system
    return await getAdminStaffs(branch_id, login_user_id, role_id, organization_id) || [];
  } catch (error) {
    console.log("getAdminStaffsEnhanced error:", error);
    return [];
  }
};
/** Leave Policy Rules */
const leavePolicyRules = async (
  requestId: any,
  bodyData: any,
  organization_id: any,
) => {
  try {
    /** Get user assign policy */
    const getUserPolicy: any = await UserLeavePolicy.findOne({
      where: {
        user_id: bodyData.user_id,
        user_leave_policy_status: user_leave_policy_status.ACTIVE,
      },
      include: [
        {
          model: LeaveAccuralPolicy,
          as: "user_leave_policy",
          where: { status: status.ACTIVE },
          required: true, // Ensures that LeaveAccuralPolicy exists
          include: [
            {
              attributes: ["id", "leave_period_type"],
              model: LeaveTypeModel,
              as: "leave_accural_leave_type_id",
              required: true, // Ensures that LeaveTypeModel exists
              where: { id: requestId, status: status.ACTIVE },
            },
          ],
        },
      ],
      raw: true,
      nest: true,
    });

    const findUserDetail: any = await User.findOne({
      where: { id: bodyData.user_id },
      include: {
        model: UserMeta,
        as: "user_meta",
      },
    });

    const startDate = moment(findUserDetail?.user_joining_date);
    const probationDays = findUserDetail?.user_meta?.probation_length;

    // Calculate the probation end date
    const probationEndDate = startDate.clone().add(probationDays, "days");
    const isUserOnProbation = moment().isSameOrBefore(
      moment(probationEndDate),
      "day",
    );
    if (getUserPolicy) {
      const generalSettings = await getGeneralSettingObj(organization_id);
      const LeaveAccuralPolicy = getUserPolicy.user_leave_policy;
      const LeavePeriodType = generalSettings?.leave_period_type || 'day';

      const leaveType =
        getUserPolicy.user_leave_policy.leave_accural_leave_type_id
          .leave_period_type;
      const policy_id = LeaveAccuralPolicy.id;
      const leave_calender_year_start_from =
        LeaveAccuralPolicy.leave_calender_year_start_from;

      const start_date = moment.utc(bodyData.start_date);
      const end_date = moment.utc(bodyData.end_date);
      const current_date = moment();
      let leaveDays = 0;
      if (LeavePeriodType == "day") {
        leaveDays = bodyData.leave_days_obj ? await calculateLeaveTotal(bodyData.leave_days_obj, LeavePeriodType) : end_date.diff(start_date, "days") + 1;
      }
      let leaveHour = 0;
      if (LeavePeriodType == "hour") {
        leaveHour = bodyData.leave_days_obj
          ? await calculateLeaveTotal(
            bodyData.leave_days_obj,
            LeavePeriodType,
          )
          : bodyData.leave_days;
      }

      const statusTrueObj: any = {
        status: true,
      };
      const working_hour_per_day = generalSettings.working_hour_per_day;
      /**
       * --------------------------------------------Tab-1 Leave Accural Rules ---------------------------------------------------
       * Leave Accural Rules */
      const leavePolicyData = (
        await getUserLeaveBalanceFunc(
          bodyData.user_id,
          start_date.year(),
          null,
          requestId,
          null,
          null,
          false,
          LeavePeriodType,
          organization_id,
        )
      ).data;

      const userRemainingLeave = leavePolicyData[0]?.user_remaining_leave; // Default to 0 if null
      // Ensure start_date is within the same year as leave_calender_year_start_from
      const leaveYearStart = moment(
        leave_calender_year_start_from,
        "YYYY-MM-DD",
      );
      const leaveYearEnd = moment(
        LeaveAccuralPolicy.leave_policy_end_date,
        "YYYY-MM-DD",
      );

      if (
        start_date.isBefore(leaveYearStart) ||
        start_date.isAfter(leaveYearEnd)
      ) {
        return { status: false, message: "LEAVE_POLICY_NOT_STARTED" };
      }

      // Ensure end_date is not beyond leave_policy_end_date
      if (end_date.isAfter(leaveYearEnd)) {
        return { status: false, message: "LEAVE_POLICY_OVER" };
      }

      if (leavePolicyData[0]?.has_leave_unlimited != true) {
        /** if user user leave hours is greater than remaining leave hours then throw error */
        if (userRemainingLeave < leaveDays) {
          return { status: false, message: "FAIL_LEAVE_LIMIT_EXCEED" };
        }
      }

      const new_employee_leave_entitlement_their_type =
        LeaveAccuralPolicy.new_employee_leave_entitlement_their_type;
      const new_employee_leave_entitlement_type =
        LeaveAccuralPolicy.new_employee_leave_entitlement_type;
      const new_employee_leave_entitlement_count =
        LeaveAccuralPolicy.new_employee_leave_entitlement_count;
      // Determine the reference date based on new_employee_leave_entitlement_their_type
      let referenceDate;
      if (
        new_employee_leave_entitlement_their_type === "date_of_joining" &&
        findUserDetail?.user_joining_date
      ) {
        referenceDate = moment(findUserDetail?.user_joining_date, "YYYY-MM-DD");
        // } else if (new_employee_leave_entitlement_their_type === "after_internship_end") {
        //   referenceDate = moment(internship_end_date, "YYYY-MM-DD");
        // }
      } else if (
        new_employee_leave_entitlement_their_type === "after_probation_end" &&
        probationEndDate
      ) {
        referenceDate = moment(probationEndDate, "YYYY-MM-DD");
      }

      // // Calculate the eligibility date
      const eligibilityDate = moment(referenceDate);
      if (new_employee_leave_entitlement_type === "days") {
        eligibilityDate.add(new_employee_leave_entitlement_count, "days");
      } else if (new_employee_leave_entitlement_type === "months") {
        eligibilityDate.add(new_employee_leave_entitlement_count, "months");
      }

      if (moment().isBefore(eligibilityDate)) {
        const pendingDays = eligibilityDate.diff(moment(), "days"); // Calculate pending days
        return {
          status: false,
          message: "LEAVE_ENTITLMENT_NOT_ASSIGN",
          isEligable: true,
          pendingDays,
        };
      }
      if (
        isUserOnProbation &&
        LeaveAccuralPolicy.has_employee_leave_in_probation == true
      ) {
        if (
          bodyData.leave_days > LeaveAccuralPolicy.probation_leave_days_count
        ) {
          return { status: false, message: "PROBATION_LEAVE_RESTRIC" };
        }
      }

      // if (LeaveAccuralPolicy?.leave_policy_accural) {
      //   if ((getUserPolicy.total_used_leave + bodyData.leave_days) > getUserPolicy.leave_current_balance) {
      //     return { status: false, message: "LEAVE_POLICY_BALANCE_STOPPED" };
      //   }

      if (userRemainingLeave > 0) {
        if (LeaveAccuralPolicy?.stop_policy_accural) {
          if (userRemainingLeave <= LeaveAccuralPolicy.stop_policy_accural_limit) {
            return { status: false, message: "LEAVE_POLICY_BALANCE_STOPPED", leave_limit: LeaveAccuralPolicy.stop_policy_accural_limit };
          }
        }
      }
      // }
      /** check user is on notice period */
      const checkUserStatus = await Resignation.findOne({
        where: {
          user_id: bodyData.user_id,
          resignation_status: resignation_status.ACCEPTED,
        },
        attributes: ["id", "last_serving_date"],
        raw: true,
      });
      if (LeaveAccuralPolicy?.restrict_leave_accural_on_emp_exit) {
        if (
          bodyData.leave_start_date &&
          bodyData.leave_end_date &&
          checkUserStatus?.last_serving_date
        ) {
          const leaveStart = moment(bodyData.start_date).startOf("day");
          const leaveEnd = moment(bodyData.end_date).startOf("day");
          const lastServingDate = moment(
            checkUserStatus.last_serving_date,
          ).startOf("day");

          if (
            leaveStart.isAfter(lastServingDate) ||
            leaveEnd.isAfter(lastServingDate)
          ) {
            return {
              status: false,
              message: "CANNOT_LEAVE_BEYOND_SERVING_DATE",
            };
          }
        }
      }

      if (LeaveAccuralPolicy.leave_accural_based_on_contract == false) {
        const contract_expiry_date = moment(
          findUserDetail?.user_meta?.expire_date,
          "YYYY-MM-DD HH:mm:ss",
        );

        if (start_date.isSameOrAfter(contract_expiry_date)) {
          return { status: false, message: "ERROR_CONTRACT_EXPIRE" };
        }
      }

      /**
       * --------------------------------------------Tab-2 Leave Apllication Rules ---------------------------------------------------
       * Leave Application Rules */
      /** get days diffrenec between leave start date and end date*/

      const getApplicationRules: any =
        await LeaveApplicationRulesPolicy.findOne({
          where: { leave_accural_policy_id: policy_id },
          raw: true,
        });
      if (getApplicationRules) {
        /** check half day condition */
        // if (getApplicationRules.allow_emp_for_half_day == false) {
        //   /** Get hours from leave start and end date */
        //   const hoursDiff = moment.duration(end_date.diff(start_date)).asHours();
        //   /**check if it's 4 and below 4 then throw error */
        //   if (hoursDiff <= (LEAVE_POLICY.HOURS / 2)) {
        //     return { status: false, message: 'ERROR_HALF_DAY_LEAVE_NOT_ALLOWED' }
        //   }
        // }

        // /** check short leave is allowed */
        // if (leaveType == leave_period_type.HOUR) {
        //   /** check half day condition for hours base */

        //   // if (getApplicationRules.allow_emp_for_half_day == true) {
        //   //   const hoursDiff = moment.duration(end_date.diff(start_date)).asHours();
        //   //   if (hoursDiff >= getApplicationRules.allow_emp_for_short_leave_count) {
        //   //     return {
        //   //       status: false, message: 'ERROR_HOURS_LEAVE_NOT_ALLOWED',
        //   //       hours: (LEAVE_POLICY.HOURS / 2)
        //   //     }
        //   //   }
        //   // }

        //   /** check condition for short leave */
        //   // if (getApplicationRules.allow_emp_for_short_leave == true) {
        //   //   /** Get hours from leave start and end date */
        //   //   const hoursDiff = moment.duration(end_date.diff(start_date)).asHours();
        //   //   if (hoursDiff >= getApplicationRules.allow_emp_for_short_leave_count) {
        //   //     return {
        //   //       status: false, message: 'ERROR_HOURS_LEAVE_NOT_ALLOWED',
        //   //       hours: getApplicationRules.allow_emp_for_short_leave_count
        //   //     }
        //   //   }
        //   // }
        // }

        /** Check documents condition */
        // if (getApplicationRules.has_document_required == true) {
        //   /** Get Days difference from leave date */
        //   const daysDiff = end_date.diff(start_date, 'days') + 1; // Get difference in full days
        //   /** check if leave days limit is exceed and document proof is not exist then throw error */
        //   if (getApplicationRules.limit_for_document_required == daysDiff && (!bodyData.document_file || bodyData.document_file == null)) {
        //     return { status: false, message: 'ERROR_LEAVE_DOCUMENTS_REQUIRED' }
        //   }
        // }

        /** Check Gender restrictions  */
        if (
          getApplicationRules.restrict_by_gender == true &&
          getApplicationRules.restrict_by_gender == bodyData.user_gender
        ) {
          return { status: false, message: "ERROR_LEAVE_GENDER_RESTRICTION" };
        }

        /** Check Marital status restrictions  */
        if (
          getApplicationRules.restrict_by_marital_status == true &&
          getApplicationRules.restrict_by_marital_status ==
          bodyData.marital_status
        ) {
          return {
            status: false,
            message: "ERROR_LEAVE_MARITAL_STATUS_RESTRICTION",
          };
        }

        /** check conditions for leave balance is in decimals, do you want to round-off the balance for the purpose of leave application?  */
        if (leaveType == leave_period_type.HOUR) {
          /** do not round off */
          const hoursDiff = bodyData.leave_days_obj
            ? await calculateLeaveTotal(bodyData.leave_data_obj, leaveType)
            : moment.duration(end_date.diff(start_date)).asHours();
          if (getApplicationRules.round_of_decimal_leave_balance == "no") {
            /** get user remaining leave */
            /** if user user leave hours is greater than remaining leave hours then throw error */
            if (leavePolicyData[0]?.has_leave_unlimited != true) {
              if (hoursDiff >= getUserPolicy.user_remaining_leave) {
                return {
                  status: false,
                  message: "ERROR_YOU_DO_NOT_HAVE_ENOUGH_LEAVE_BALANCE",
                };
              }
            }
          }
          /** half day round off */
          // if (getApplicationRules.round_of_decimal_leave_balance == 'half_day') {
          //   // Extract integer and decimal parts from remaining balance
          //   const integerPart = Math.floor(getUserPolicy.user_remaining_leave); // Before decimal

          //   let roundedDecimal = 0;
          //   if (hoursDiff <= 0.25) {
          //     roundedDecimal = 0;
          //   } else if (hoursDiff > 0.25 && hoursDiff <= 0.75) {
          //     roundedDecimal = 0.5;
          //   } else if (hoursDiff > 75) {
          //     roundedDecimal = 1; // Increase integer part
          //   }
          //   // Final rounded leave balance
          //   const finalRoundedBalance = integerPart + roundedDecimal;
          //   return { status: true, message: finalRoundedBalance }
          // }

          if (
            getApplicationRules.round_of_decimal_leave_balance == "full_day"
          ) {
            // Extract integer and decimal parts from remaining balance
            const integerPart = Math.floor(getUserPolicy.user_remaining_leave); // Before decimal

            let roundedDecimal = 0;
            if (hoursDiff <= 0.25) {
              roundedDecimal = 0;
            } else if (hoursDiff > 0.5) {
              roundedDecimal = 1; // Increase integer part
            }
            // Final rounded leave balance
            const finalRoundedBalance = integerPart + roundedDecimal;

            statusTrueObj.leaveDays = finalRoundedBalance;
          }
        }
        /** check leave past and future date conditions */
        if (getApplicationRules.allow_leave_request_data) {
          const leave_request_data = JSON.parse(
            getApplicationRules.allow_leave_request_data,
          );
          /** check my start date status, if it's past date then execute below code*/
          if (start_date.isBefore(current_date, "day")) {
            /** If past date not allow then throw error */
            if (leave_request_data.past_date == false) {
              return { status: false, message: "ERROR_PAST_DATE_RESTRICTION" };
            } else {
              /** check if past date is allow but days are not specified,
               * check joining date is less than financial leave year then compare with JD else compare with financial leave year*/
              if (leave_request_data.past_date_status == false) {
                /** get leave financial year and employee joining date */
                const leave_financial_year = moment(
                  leave_calender_year_start_from,
                  "YYYY-MM-DD HH:mm:ss",
                );
                const joining_date = moment(
                  bodyData.user_joining_date,
                  "YYYY-MM-DD HH:mm:ss",
                );

                /* check if employee joining date is before leave financial year */
                const isBeforeFinancialYear = joining_date.isBefore(
                  leave_financial_year,
                  "day",
                );
                if (!isBeforeFinancialYear) {
                  /** If no then compare start date with joining date
                   * if start date is before joining date then throw error
                   */
                  if (start_date.isBefore(joining_date, "day")) {
                    return {
                      status: false,
                      message: "ERROR_LEAVE_GREATER_THAN_JOINING_RESTRICTION",
                      joining_date: moment(bodyData.user_joining_date).format(
                        "YYYY-MM-DD",
                      ),
                    };
                  }
                } else {
                  /** If no then compare start date with financial date
                   * if start date is before financial date then throw error
                   */
                  if (start_date.isBefore(leave_financial_year, "day")) {
                    return {
                      status: false,
                      message: "ERROR_LEAVE_GREATER_THAN_JOINING_RESTRICTION",
                      joining_date: moment(
                        leave_calender_year_start_from,
                      ).format("YYYY-MM-DD"),
                    };
                  }
                }
              } else {
                /** check if past date is allow and days are specified ,
                 * check joining date is less than specified days then compare with JD else compare with specofoed days*/

                /** get specified days date and employee joining date */
                const specified_days_date = moment()
                  .subtract(leave_request_data.past_date_days, "days")
                  .format("YYYY-MM-DD HH:mm:ss");
                const joining_date = moment(
                  bodyData.user_joining_date,
                  "YYYY-MM-DD HH:mm:ss",
                );
                /* check if employee joining date is before specified days date */
                const isBeforeSpecifiedDays = joining_date.isBefore(
                  specified_days_date,
                  "day",
                );
                if (!isBeforeSpecifiedDays) {
                  /** If no then compare start date with joining date
                   * if start date is before joining date then throw error
                   */
                  if (start_date.isBefore(joining_date, "day")) {
                    return {
                      status: false,
                      message: "ERROR_LEAVE_GREATER_THAN_JOINING_RESTRICTION",
                      joining_date: moment(bodyData.user_joining_date).format(
                        "YYYY-MM-DD",
                      ),
                    };
                  }
                } else {
                  /** If no then compare start date with specified date days
                   * if start date is before fspecified days date then throw error
                   */
                  if (start_date.isBefore(specified_days_date, "day")) {
                    return {
                      status: false,
                      message: "ERROR_LEAVE_GREATER_THAN_JOINING_RESTRICTION",
                      joining_date: moment(
                        leave_calender_year_start_from,
                      ).format("YYYY-MM-DD"),
                    };
                  }
                }
              }
            }
          } else {
            /** start date is future date then execute below code
             * If future date not allow then throw error */
            if (leave_request_data.future_date == false) {
              return {
                status: false,
                message: "ERROR_FUTURE_DATE_RESTRICTION",
              };
            } else {
              /** check if future date is allow but days are not specified,
               * check employeement contract expire date, if it's preset */
              /** get employee contract date */
              const getContractExpiryDate: any = await UserMeta.findOne({
                where: { user_id: bodyData.user_id },
                attributes: ["expire_date"],
                raw: true,
              });
              if (leave_request_data.future_date_status == false) {
                /** Get contarct expiry date and add 365 into current date */
                const future_date = moment().add(365, "days");
                if (
                  getContractExpiryDate &&
                  getContractExpiryDate.expire_date
                ) {
                  const contract_expiry_date = moment(
                    getContractExpiryDate.expire_date,
                    "YYYY-MM-DD HH:mm:ss",
                  );

                  /* Check if contract expiry date is before future date */
                  const isBeforeFutureDate = contract_expiry_date.isBefore(
                    future_date,
                    "day",
                  );

                  if (!isBeforeFutureDate) {
                    /** If contract expiry date is greater than future date
                     * compare start date with future date
                     * if start date is greater than future date then throw error
                     */
                    if (
                      start_date.isAfter(future_date, "day") &&
                      !start_date.isSame(future_date, "day")
                    ) {
                      return {
                        status: false,
                        message:
                          "ERROR_LEAVE_LESS_THAN_CONTRACT_DATE_RESTRICTION",
                        contract_date: moment(future_date).format("YYYY-MM-DD"),
                      };
                    }
                  } else {
                    /** If contract expiry date is less than future date
                     * compare start date with contract expiry date
                     * if start date is greater than contract expiry date then throw error
                     */
                    if (
                      start_date.isAfter(contract_expiry_date, "day") &&
                      !start_date.isSame(contract_expiry_date, "day")
                    ) {
                      return {
                        status: false,
                        message:
                          "ERROR_LEAVE_LESS_THAN_CONTRACT_DATE_RESTRICTION",
                        contract_date: moment(
                          getContractExpiryDate.expire_date,
                        ).format("YYYY-MM-DD"),
                      };
                    }
                  }
                } else {
                  /** If no contract expiry date is found, only compare start date with future date */
                  if (
                    start_date.isAfter(future_date, "day") &&
                    !start_date.isSame(future_date, "day")
                  ) {
                    return {
                      status: false,
                      message:
                        "ERROR_LEAVE_LESS_THAN_CONTRACT_DATE_RESTRICTION",
                      contract_date: moment(future_date).format("YYYY-MM-DD"),
                    };
                  }
                }
              } else {
                /** check if future date is allow and days are specified ,
                /** get specified days date and employee contract expiry date */
                const specified_days_date = moment()
                  .add(leave_request_data.future_date_days, "days")
                  .utc();
                if (
                  getContractExpiryDate &&
                  getContractExpiryDate.expire_date
                ) {
                  const contract_expiry_date = moment(
                    getContractExpiryDate.expire_date,
                    "YYYY-MM-DD HH:mm:ss",
                  );

                  /* Check if contract expiry date is before specified days date */
                  const isBeforeFutureDate = contract_expiry_date.isBefore(
                    specified_days_date,
                    "day",
                  );

                  if (!isBeforeFutureDate) {
                    /** If contract expiry date is greater than specified days date
                     * compare start date with specified days date
                     * if start date is greater than specified days date then throw error
                     */
                    if (
                      start_date.isAfter(specified_days_date, "day") &&
                      !start_date.isSame(specified_days_date, "day")
                    ) {
                      return {
                        status: false,
                        message:
                          "ERROR_LEAVE_LESS_THAN_CONTRACT_DATE_RESTRICTION",
                        contract_date:
                          moment(specified_days_date).format("YYYY-MM-DD"),
                      };
                    }
                  } else {
                    /** If contract expiry date is less than specified days date
                     * compare start date with contract expiry date
                     * if start date is greater than contract expiry date then throw error
                     */
                    if (
                      start_date.isAfter(contract_expiry_date, "day") &&
                      !start_date.isSame(contract_expiry_date, "day")
                    ) {
                      return {
                        status: false,
                        message:
                          "ERROR_LEAVE_LESS_THAN_CONTRACT_DATE_RESTRICTION",
                        contract_date: moment(
                          getContractExpiryDate.expire_date,
                        ).format("YYYY-MM-DD"),
                      };
                    }
                  }
                } else {
                  /** If no contract expiry date is found, only compare start date with specified_days_date */
                  if (
                    start_date.isAfter(specified_days_date, "day") &&
                    !start_date.isSame(specified_days_date, "day")
                  ) {
                    return {
                      status: false,
                      message:
                        "ERROR_LEAVE_LESS_THAN_CONTRACT_DATE_RESTRICTION",
                      contract_date:
                        moment(specified_days_date).format("YYYY-MM-DD"),
                    };
                  }
                }
              }
            }
            /** check if apply leave in advance days status is true and days ar specified */
            if (
              leave_request_data.apply_in_advance_days_status == true &&
              leave_request_data.apply_in_advance_days
            ) {
              const daysDiff = start_date.diff(current_date, "days");
              if (daysDiff <= leave_request_data.apply_in_advance_days) {
                return {
                  status: false,
                  message: "ERROR_ADVANCE_LEAVE_RESTRICTION",
                  advance_hours: leave_request_data.apply_in_advance_days,
                };
              }
            }
          }
        }
      }

      /***  ------------------------------------- Tab-3 leave Restriction ------------------------------------------------------------ */
      const getLeaveRestrictions: any = await LeaveRestrictionPolicy.findOne({
        where: { leave_accural_policy_id: policy_id },
        raw: true,
      });
      if (getLeaveRestrictions) {
        /** check condition for max consecutive days  */
        if (getLeaveRestrictions.has_maximum_consecutive == true) {
          /** get days difference between start date and end date */
          const daysDiff = end_date.diff(start_date, "days");
          if (daysDiff >= getLeaveRestrictions.maximum_consecutive_count) {
            return {
              status: false,
              message: "ERROR_MAX_CONSECUTIVE_LEAVE_RESTRICTION",
              max_consecutive_days:
                getLeaveRestrictions.maximum_consecutive_count,
            };
          }
        }

        /** check condition for gap between 2 leave application. */
        if (getLeaveRestrictions.has_allow_gap_between_leave == true) {
          /* Fetch last  leave for the user */
          const lastLeave: any = await UserRequest.findOne({
            where: { from_user_id: bodyData.user_id },
            order: [["id", "DESC"]], // Get the most recent leave
            raw: true,
          });
          if (lastLeave) {
            /** get last leave end date and new leave start date */
            const lastLeaveEndDate = moment(
              lastLeave.end_date,
              "YYYY-MM-DD HH:mm:ss",
            );
            const newLeaveStartDate = moment(start_date, "YYYY-MM-DD HH:mm:ss");

            /* Calculate gap in days */
            const gapInDays = newLeaveStartDate.diff(lastLeaveEndDate, "days");
            if (
              gapInDays <= getLeaveRestrictions.gap_between_leave_application
            ) {
              return {
                status: false,
                message: "ERROR_GAP_BETWEEN_LEAVE_RESTRICTION",
                leave_gap: getLeaveRestrictions.gap_between_leave_application,
              };
            }
          }
        }

        /** check condition for apply leave for notice period user.
         * if it's false and user try to apply leave then throw error
         */
        if (
          getLeaveRestrictions.emp_can_apply_in_notice_period == false &&
          checkUserStatus
        ) {
          return {
            status: false,
            message: "ERROR_NOTICE_PERIOD_USER_LEAVE_RESTRICTION",
          };
        }
        /** check condition for club leave with another leave type
         * on-hold due to club leave
         */

        /** check condition for max leave request allowed for period */
        if (getLeaveRestrictions.maximum_leave_allowed_for_period_type) {
          if (getLeaveRestrictions.maximum_leave_allowed_for_period_count > 0) {
            const appliedStartDate = bodyData.start_date; // The start date of the leave request
            /** Function to calculate the start date for filtering leaves */
            const startDateFilter: any = getStartDate(
              getLeaveRestrictions.maximum_leave_allowed_for_period_type,
              getLeaveRestrictions.maximum_leave_allowed_for_period_count,
              appliedStartDate,
            );
            const endDateFilter = moment(startDateFilter.date)
              .endOf(startDateFilter.type)
              .format("YYYY-MM-DD");

            // Fetch existing leave requests within the specified period
            const leaveCount = await UserRequest.count({
              where: {
                from_user_id: bodyData.user_id,
                start_date: {
                  [Op.gte]: startDateFilter.date,
                  [Op.lte]: endDateFilter, // Ensuring we only count leaves in the same month
                },
              },
            });

            if (leaveCount) {
              // Check if leave count exceeds the allowed limit
              if (
                leaveCount >=
                getLeaveRestrictions.maximum_leave_allowed_for_period_count
              ) {
                return {
                  status: false,
                  message: "ERROR_MAX_LEAVE_RESTRICTION",
                  max_leaves_allowed:
                    getLeaveRestrictions.maximum_leave_allowed_for_period_count,
                  policy_type:
                    getLeaveRestrictions.maximum_leave_allowed_for_period_type,
                };
              }
            }
          }
        }

        /** check condition for max leave balance allowed for period
         * on-hold due to club leave
         */
      }

      /** check condition for holiday count as a leave */
      const leaveStart: any = moment(start_date).utc().startOf("day");
      const leaveEnd: any = moment(end_date).utc().startOf("day");

      const findWeekEnds: any = await UserWeekDay.findOne({
        where: {
          user_id: bodyData.user_id,
          user_weekday_status: user_weekday_status.ACTIVE,
        },
        attributes: [
          "monday",
          "tuesday",
          "wednesday",
          "thursday",
          "friday",
          "saturday",
          "sunday",
        ],
        raw: true,
      });

      const dayOffNumbers = Object.entries(findWeekEnds)
        .filter(([, status]) => status === "dayoff")
        .map(([day]) => moment().day(day).day());

      /***  ------------------------------------- Tab-4 Holidays and weekends ------------------------------------------------------------ */
      const getLeaveHolidayRules: any = await LeaveHolidayWeekendPolicy.findOne(
        { where: { leave_accural_policy_id: policy_id }, raw: true },
      );
      if (getLeaveHolidayRules) {
        /** ------------------------------------- Holday Policy ---------------------------------------------------------------------- */
        if (
          dayOffNumbers.includes(leaveStart.day()) &&
          dayOffNumbers.includes(leaveEnd.day()) // Sat to Sun
        ) {
          return { status: false, message: "ERROR_LEAVE_CANNOT_APPLY_WEEKEND" };
        }

        /***  check both holiday and weekend are simultaneously */
        const holiday_data = JSON.parse(
          getLeaveHolidayRules.holiday_between_leave_period_data,
        );
        const weekend_data = JSON.parse(
          getLeaveHolidayRules.weekend_between_leave_period_data,
        );
        const configure_sandwich_days =
          weekend_data.configure_sandwich_days > 0
            ? weekend_data.configure_sandwich_days
            : 1;

        // Check if leave is near a weekend
        const isBeforeWeekend =
          dayOffNumbers.length > 0 &&
          dayOffNumbers.includes(
            start_date.clone().subtract(configure_sandwich_days, "day").day(),
          ); // Friday or Saturday before
        const isAfterWeekend =
          dayOffNumbers.length > 0 &&
          dayOffNumbers.includes(
            end_date.clone().add(configure_sandwich_days, "day").day(),
          ); // Saturday or Sunday after

        let holidaysDiff = 0;
        let holidayHourDiff = 0;
        let getHolidayPolicies: any = {};
        /** Check if user has an assigned holiday policy */
        const getAssignedPolicy = await UserHolidayPolicy.findAll({
          where: {
            user_id: bodyData.user_id,
            user_holiday_policy_status: user_holiday_policy_status.ACTIVE,
          },
          attributes: ["holiday_policy_id"],
          raw: true,
        });
        if (getAssignedPolicy.length > 0) {
          /** Fetch all holidays matching the same month and year as leave start date */
          getHolidayPolicies = await HolidayPolicy.findAll({
            where: {
              id: {
                [Op.in]: getAssignedPolicy.map(
                  (policy) => policy.holiday_policy_id,
                ),
              },
              holiday_policy_status: holiday_policy_status.ACTIVE,
              // [Op.or]: [
              //   // Case 1: Holiday fully overlaps with leave
              //   {
              //     holiday_policy_start_date: { [Op.lte]: leaveEnd.toDate() }, // Holiday starts on or before leave end
              //     holiday_policy_end_date: { [Op.gte]: leaveStart.toDate() } // Holiday ends on or after leave start
              //   },
              //   // Case 2: Holiday spans across two months
              //   {
              //     [Op.and]: [
              //       Sequelize.where(
              //         Sequelize.fn("MONTH", Sequelize.col("holiday_policy_start_date")),
              //         { [Op.ne]: Sequelize.fn("MONTH", Sequelize.col("holiday_policy_end_date")) } // Different months
              //       ),
              //       Sequelize.where(
              //         Sequelize.fn("YEAR", Sequelize.col("holiday_policy_start_date")),
              //         Sequelize.fn("YEAR", Sequelize.col("holiday_policy_end_date")) // Same year
              //       ),
              //       {
              //         holiday_policy_start_date: { [Op.lte]: leaveEnd.toDate() }, // Holiday starts before or within leave period
              //         holiday_policy_end_date: { [Op.gte]: leaveStart.toDate() } // Holiday ends within or after leave period
              //       }
              //     ]
              //   },
              //   // Case 3: Holiday immediately follows leave period
              //   {
              //     holiday_policy_start_date: { [Op.gte]: leaveEnd.clone().add(1, 'day').toDate() } // Holiday starts the next day after leave ends
              //   },
              //   // Case 4: Holiday immediately precedes leave period (1 day before)
              //   {
              //     holiday_policy_end_date: { [Op.lte]: leaveStart.clone().add(-1, 'day').toDate() } // Holiday ends 1 day before leave starts
              //   }
              // ]
            },
            raw: true,
          });
        }

        /** if default holiday and weekend not selected exclude both dates */
        if (getLeaveHolidayRules.holiday_between_leave_period == "dont_count" && getLeaveHolidayRules.weekend_between_leave_period == "dont_count") {
          if (getHolidayPolicies.length > 0) {
            for (const holiday of getHolidayPolicies) {
              const holidayStart = moment
                .utc(holiday.holiday_policy_start_date)
                .startOf("day");
              const holidayEnd = moment
                .utc(holiday.holiday_policy_end_date)
                .endOf("day");

              /** Get days difference between holiday start and end */
              const holidaysDiff = holidayEnd.diff(holidayStart, "days") + 1;
              const holidayHourDiff = holidaysDiff * working_hour_per_day;

              /** Check if leave falls within sandwich window */
              const leaveCoversHoliday =
                moment(start_date).isSameOrBefore(holidayStart) &&
                moment(end_date).isSameOrAfter(holidayEnd);
              const leaveStartsInsideHoliday = holidayStart.isBetween(
                start_date,
                end_date,
                null,
                "[]",
              ); // true if start date is on or inside the holiday
              const leaveEndsInsideHoliday = holidayEnd.isBetween(
                start_date,
                end_date,
                null,
                "[]",
              ); // true if end date is on or inside the holiday

              let totalDays = 0;
              let totalHours = 0;
              // Loop through the days between the start and end date
              const currentDate = start_date.clone();
              while (
                currentDate.isBefore(end_date) ||
                currentDate.isSame(end_date, "day")
              ) {
                const dayOfWeek = currentDate.day();
                // If it's not a Saturday or Sunday, count the day
                if (!dayOffNumbers.includes(dayOfWeek)) {
                  totalDays++;
                  totalHours + working_hour_per_day;
                }
                currentDate.add(1, "days"); // Move to the next day
              }
              if (
                leaveCoversHoliday ||
                leaveEndsInsideHoliday ||
                leaveStartsInsideHoliday
              ) {
                totalDays -= holidaysDiff;
                totalHours -= holidayHourDiff;
              }
              statusTrueObj.leaveDays =
                LeavePeriodType == "day"
                  ? totalDays > 0
                    ? totalDays
                    : leaveDays
                  : totalHours > 0
                    ? totalHours
                    : leaveHour;
            }
          }
        }

        if (getLeaveHolidayRules.holiday_between_leave_period == "count_as_leave" && getLeaveHolidayRules.weekend_between_leave_period == "count_as_leave") {
          /** check sandwitch before holiday and weekend before holiday  */
          if (holiday_data.apply_sandwich_before_holiday == true && weekend_data.apply_sandwich_before_weekend == true && getHolidayPolicies.length > 0) {
            for (const holiday of getHolidayPolicies) {
              const holidayStart = moment.utc(
                holiday.holiday_policy_start_date,
              ); // Start date of holiday (e.g., March 14)
              const holidayEnd = moment.utc(holiday.holiday_policy_end_date); // End date of holiday (e.g., March 14)
              /** Get days difference between holiday start date and end date */
              holidaysDiff = holidayEnd.diff(holidayStart, "days") + 1;
              holidayHourDiff = holidaysDiff * working_hour_per_day;

              /** Define the sandwich window (configure_sandwich_days days before the holiday) */
              const sandwichBeforeStart = holidayStart
                .clone()
                .subtract(holiday_data.configure_sandwich_days, "days"); // configure_sandwich_days days before the holiday
              /** Ensure the leave start and end dates are within the sandwich window before the holiday */

              if (
                moment(start_date).isBetween(
                  sandwichBeforeStart,
                  holidayStart,
                  null,
                  "[]",
                ) || // Leave start is within configure_sandwich_days days before holiday
                moment(end_date).isBetween(
                  sandwichBeforeStart,
                  holidayStart,
                  null,
                  "[]",
                ) // Leave end is within configure_sandwich_days days before holiday
              ) {
                leaveDays = leaveDays + holidaysDiff;
                leaveHour = leaveHour + holidayHourDiff;
                // Add holiday days to the leave days
              }
            }
            if (isBeforeWeekend) {
              leaveDays += dayOffNumbers.length;
              leaveHour += dayOffNumbers.length * working_hour_per_day;
            }

            statusTrueObj.leaveDays =
              LeavePeriodType == "day"
                ? leaveDays > 0
                  ? leaveDays
                  : leaveDays
                : leaveHour > 0
                  ? leaveHour
                  : leaveHour;
          }

          /** check sandwitch before holiday and weekend before holiday  */
          if (holiday_data.apply_sandwich_after_holiday == true && weekend_data.apply_sandwich_after_weekend == true && getHolidayPolicies.length > 0) {
            for (const holiday of getHolidayPolicies) {
              const holidayStart = moment.utc(
                holiday.holiday_policy_start_date,
              ); // Start date of holiday (e.g., March 14)
              const holidayEnd = moment.utc(holiday.holiday_policy_end_date); // End date of holiday (e.g., March 14)
              /** Get days difference between holiday start date and end date */
              holidaysDiff = holidayEnd.diff(holidayStart, "days") + 1;
              holidayHourDiff = holidaysDiff * working_hour_per_day;

              /** Define the sandwich window (configure_sandwich_days days after the holiday) */
              const sandwichAfterEnd = holidayEnd
                .clone()
                .add(holiday_data.configure_sandwich_days, "days"); // configure_sandwich_days days before the holiday
              /** Ensure the leave start and end dates are within the sandwich window before the holiday */
              if (
                moment(start_date).isBetween(
                  holidayEnd,
                  sandwichAfterEnd,
                  null,
                  "[]",
                ) || // Leave start is within configure_sandwich_days days after holiday
                moment(end_date).isBetween(
                  holidayEnd,
                  sandwichAfterEnd,
                  null,
                  "[]",
                ) // Leave end is within configure_sandwich_days days after holiday
              ) {
                leaveDays = leaveDays + holidaysDiff;
                leaveHour = leaveHour + holidayHourDiff; // Add holiday days to the leave days
              }
            }
            if (isAfterWeekend) {
              leaveDays += dayOffNumbers.length;
              leaveHour += dayOffNumbers.length * working_hour_per_day;
            }
          }

          /** check sandwitch before holiday and weekend before holiday  */
          if (holiday_data.apply_sandwich_between_holiday == true && weekend_data.apply_sandwich_between_weekend == true && getHolidayPolicies.length > 0
          ) {
            for (const holiday of getHolidayPolicies) {
              const holidayStart = moment.utc(
                holiday.holiday_policy_start_date,
              );
              const holidayEnd = moment.utc(holiday.holiday_policy_end_date);

              /** Get days difference between holiday start and end */
              const holidaysDiff = holidayEnd.diff(holidayStart, "days") + 1;
              const holidayHourDiff = holidaysDiff * working_hour_per_day;

              /** Define sandwich window */
              const sandwichBeforeStart = holidayStart
                .clone()
                .subtract(holiday_data.configure_sandwich_days, "days");
              const sandwichAfterEnd = holidayEnd
                .clone()
                .add(holiday_data.configure_sandwich_days, "days");

              /** Check if leave falls within sandwich window */
              const leaveStartsInside = moment(start_date).isBetween(
                sandwichBeforeStart,
                sandwichAfterEnd,
                null,
                "[]",
              );
              const leaveEndsInside = moment(end_date).isBetween(
                sandwichBeforeStart,
                sandwichAfterEnd,
                null,
                "[]",
              );

              /** Check if leave already covers the holiday */
              const leaveCoversHoliday =
                moment(start_date).isSameOrBefore(holidayStart) &&
                moment(end_date).isSameOrAfter(holidayEnd);
              /**
               *  Fix: Add only if BOTH conditions are met:
               *  The leave falls within the sandwich window.
               *  The leave does NOT fully cover the holiday.
               */
              if (
                (leaveStartsInside || leaveEndsInside) &&
                !leaveCoversHoliday
              ) {
                const newLeaveDays = leaveDays + holidaysDiff;
                const newLeaveHours = leaveHour + holidayHourDiff;
                leaveDays = newLeaveDays;
                if (LeavePeriodType == "hour") {
                  leaveHour = newLeaveHours;
                }
              }
              break; // Prevent multiple additions
            }

            const leaveDaysArr = [];
            /** check if is before weekend and after weekend scenario */
            if (isBeforeWeekend || isAfterWeekend) {
              leaveDays += dayOffNumbers.length;
              leaveHour += dayOffNumbers.length * working_hour_per_day;
            }
            /** Get user previous leave before weekend */
            const leaveDaysBeforeWeekend = await UserRequest.findOne({
              where: {
                from_user_id: bodyData.user_id,
                request_status: [request_status.APPROVED],
              },
              order: [["id", "DESC"]],
              raw: true,
            });
            if (leaveDaysBeforeWeekend) {
              const lastLeaveEndDate = moment(leaveDaysBeforeWeekend.end_date);
              const lastLeaveStartDate = moment(
                leaveDaysBeforeWeekend.start_date,
              );
              const previousLeaveDaysDifference =
                lastLeaveEndDate.diff(lastLeaveStartDate, "days") + 1;
              // Check if the last leave ended on a Friday & new leave starts on Monday
              if (
                dayOffNumbers.includes(lastLeaveEndDate.day()) && // Last leave ended on a day off (0, 6)
                dayOffNumbers.includes(start_date.day())
              ) {
                // Include Saturday & Sunday as leave days
                leaveDaysArr.push(
                  lastLeaveEndDate.clone().add(1, "day").format("YYYY-MM-DD"),
                ); // Saturday
                leaveDaysArr.push(
                  lastLeaveEndDate.clone().add(2, "day").format("YYYY-MM-DD"),
                ); // Sunday
                leaveDays = leaveDaysArr.length + previousLeaveDaysDifference;
                leaveHour =
                  leaveDaysArr.length * working_hour_per_day +
                  previousLeaveDaysDifference * working_hour_per_day;
              }
            }
            statusTrueObj.leaveDays =
              LeavePeriodType == "day"
                ? leaveDays > 0
                  ? leaveDays
                  : 0
                : leaveHour > 0
                  ? leaveHour
                  : 0;
          }

          if (
            holiday_data.holiday_accompaying_days > 0 &&
            weekend_data.weekend_accompaying_days > 0 &&
            getHolidayPolicies.length > 0
          ) {
            for (const holiday of getHolidayPolicies) {
              const holidayStart = moment(
                holiday.holiday_policy_start_date,
              ).utc();
              const holidayEnd = moment(holiday.holiday_policy_end_date).utc();
              /** get days difference between holiday start date and end date */
              holidaysDiff = holidayEnd.diff(holidayStart, "days") + 1;
              holidayHourDiff = holidaysDiff * working_hour_per_day;
              /** Scenario 1: If leave falls within a holiday, reject the leave */
              // Case 1: Single-day holiday (start == end)
              if (holidayStart.isSame(holidayEnd)) {
                if (
                  leaveStart.isSame(holidayStart) &&
                  leaveEnd.isSame(holidayEnd)
                ) {
                  return {
                    status: false,
                    message: "ERROR_YOU_NOT_APPLY_LEAVE_ON_HOLIDAY",
                  };
                }
              }

              // Case 2: Multi-day holiday (start < end)
              else if (
                leaveStart.isBetween(holidayStart, holidayEnd, null, "[]") ||
                leaveEnd.isBetween(holidayStart, holidayEnd, null, "[]")
              ) {
                return {
                  status: false,
                  message: "ERROR_YOU_NOT_APPLY_LEAVE_ON_HOLIDAY",
                };
              }
              /** Scenario 2: If leave is adjacent to a holiday, consider it as leave and exclude weekends */
              if (
                // Case 1: Leave is exactly before the holiday
                end_date.isSame(
                  holidayStart.clone().subtract(1, "day"),
                  "day",
                ) ||
                // Case 2: Leave is exactly after the holiday
                start_date.isSame(holidayEnd.clone().add(1, "day"), "day") ||
                // Case 3: Leave period overlaps with the holiday
                start_date.isBetween(holidayStart, holidayEnd, null, "[]") ||
                end_date.isBetween(holidayStart, holidayEnd, null, "[]") ||
                (start_date.isBefore(holidayStart) &&
                  end_date.isAfter(holidayEnd))
              ) {
                if (
                  leaveDays == holiday_data.holiday_accompaying_days ||
                  leaveDays <= holiday_data.holiday_accompaying_days
                ) {
                  leaveDays = leaveDays + holidaysDiff;
                }
                if (
                  leaveHour ==
                  holiday_data.holiday_accompaying_days *
                  working_hour_per_day ||
                  leaveHour <=
                  holiday_data.holiday_accompaying_days * working_hour_per_day
                ) {
                  leaveHour = leaveHour + holidayHourDiff;
                }
                if (leaveHour == holiday_data.holiday_accompaying_days * working_hour_per_day || leaveHour <= holiday_data.holiday_accompaying_days * working_hour_per_day) {

                  leaveHour = leaveHour + holidayHourDiff

                }
              }
            }

            if (
              leaveDays >= weekend_data.weekend_accompaying_days ||
              isAfterWeekend ||
              isBeforeWeekend
            ) {
              leaveDays += dayOffNumbers.length;
            }
            if (
              leaveHour * working_hour_per_day >=
              weekend_data.weekend_accompaying_days * working_hour_per_day ||
              isAfterWeekend ||
              isBeforeWeekend
            ) {
              leaveHour += dayOffNumbers.length * working_hour_per_day;
            }

            statusTrueObj.leaveDays =
              LeavePeriodType == "day"
                ? leaveDays > 0
                  ? leaveDays
                  : 0
                : leaveHour > 0
                  ? leaveHour
                  : 0;
          }
        }
        /** ----------------------------------- Holiday policy ----------------------------------------------------------------------- */
        if (getLeaveHolidayRules.holiday_between_leave_period == "count_as_leave" || getLeaveHolidayRules.holiday_between_leave_period == "dont_count") {
          const holiday_data = JSON.parse(
            getLeaveHolidayRules.holiday_between_leave_period_data,
          );
          if (holiday_data) {
            let holidaysDiff = 0;
            let getHolidayPolicies: any = {};
            /** Check if user has an assigned holiday policy */
            const getAssignedPolicy = await UserHolidayPolicy.findAll({
              where: {
                user_id: bodyData.user_id,
                user_holiday_policy_status: user_holiday_policy_status.ACTIVE,
              },
              attributes: ["holiday_policy_id"],
              raw: true,
            });
            if (getAssignedPolicy.length > 0) {
              /** Fetch all holidays matching the same month and year as leave start date */
              getHolidayPolicies = await HolidayPolicy.findAll({
                where: {
                  id: {
                    [Op.in]: getAssignedPolicy.map(
                      (policy) => policy.holiday_policy_id,
                    ),
                  },
                  holiday_policy_status: holiday_policy_status.ACTIVE,
                  // [Op.or]: [
                  //   // Case 1: Holiday fully overlaps with leave
                  //   {
                  //     holiday_policy_start_date: { [Op.lte]: leaveEnd.toDate() }, // Holiday starts on or before leave end
                  //     holiday_policy_end_date: { [Op.gte]: leaveStart.toDate() } // Holiday ends on or after leave start
                  //   },
                  //   // Case 2: Holiday spans across two months
                  //   {
                  //     [Op.and]: [
                  //       Sequelize.where(
                  //         Sequelize.fn("MONTH", Sequelize.col("holiday_policy_start_date")),
                  //         { [Op.ne]: Sequelize.fn("MONTH", Sequelize.col("holiday_policy_end_date")) } // Different months
                  //       ),
                  //       Sequelize.where(
                  //         Sequelize.fn("YEAR", Sequelize.col("holiday_policy_start_date")),
                  //         Sequelize.fn("YEAR", Sequelize.col("holiday_policy_end_date")) // Same year
                  //       ),
                  //       {
                  //         holiday_policy_start_date: { [Op.lte]: leaveEnd.toDate() }, // Holiday starts before or within leave period
                  //         holiday_policy_end_date: { [Op.gte]: leaveStart.toDate() } // Holiday ends within or after leave period
                  //       }
                  //     ]
                  //   },
                  //   // Case 3: Holiday immediately follows leave period
                  //   {
                  //     holiday_policy_start_date: { [Op.gte]: leaveEnd.clone().add(1, 'day').toDate() } // Holiday starts the next day after leave ends
                  //   },
                  //   // Case 4: Holiday immediately precedes leave period (1 day before)
                  //   {
                  //     holiday_policy_end_date: { [Op.lte]: leaveStart.clone().add(-1, 'day').toDate() } // Holiday ends 1 day before leave starts
                  //   }
                  // ]
                },
                raw: true,
              });
              if (getHolidayPolicies.length > 0) {
                for (const holiday of getHolidayPolicies) {
                  const holidayStart = moment(holiday.holiday_policy_start_date)
                    .utc()
                    .startOf("day");
                  const holidayEnd = moment(holiday.holiday_policy_end_date)
                    .utc()
                    .startOf("day");
                  /** Scenario 1: If leave falls within a holiday, reject the leave */
                  // Case 1: Single-day holiday (start == end)
                  if (holidayStart.isSame(holidayEnd)) {
                    if (
                      leaveStart.isSame(holidayStart) &&
                      leaveEnd.isSame(holidayEnd)
                    ) {
                      return {
                        status: false,
                        message: "ERROR_YOU_NOT_APPLY_LEAVE_ON_HOLIDAY",
                      };
                    }
                  }
                }
              }
            }
            /** check holiday consider as a leave */
            if (holiday_data.holiday_accompaying_days > 0 && getHolidayPolicies.length > 0) {
              for (const holiday of getHolidayPolicies) {
                const holidayStart = moment(
                  holiday.holiday_policy_start_date,
                ).utc();
                const holidayEnd = moment(
                  holiday.holiday_policy_end_date,
                ).utc();
                /** get days difference between holiday start date and end date */
                holidaysDiff = holidayEnd.diff(holidayStart, "days") + 1;
                /** Scenario 1: If leave falls within a holiday, reject the leave */
                // Case 1: Single-day holiday (start == end)
                if (holidayStart.isSame(holidayEnd)) {
                  if (
                    leaveStart.isSame(holidayStart) &&
                    leaveEnd.isSame(holidayEnd)
                  ) {
                    return {
                      status: false,
                      message: "ERROR_YOU_NOT_APPLY_LEAVE_ON_HOLIDAY",
                    };
                  }
                }

                // Case 2: Multi-day holiday (start < end)
                else if (
                  leaveStart.isBetween(holidayStart, holidayEnd, null, "[]") ||
                  leaveEnd.isBetween(holidayStart, holidayEnd, null, "[]")
                ) {
                  return {
                    status: false,
                    message: "ERROR_YOU_NOT_APPLY_LEAVE_ON_HOLIDAY",
                  };
                }
                /** Scenario 2: If leave is adjacent to a holiday, consider it as leave and exclude weekends */
                if (
                  // Case 1: Leave is exactly before the holiday
                  end_date.isSame(
                    holidayStart.clone().subtract(1, "day"),
                    "day",
                  ) ||
                  // Case 2: Leave is exactly after the holiday
                  start_date.isSame(holidayEnd.clone().add(1, "day"), "day") ||
                  // Case 3: Leave period overlaps with the holiday
                  start_date.isBetween(holidayStart, holidayEnd, null, "[]") ||
                  end_date.isBetween(holidayStart, holidayEnd, null, "[]") ||
                  (start_date.isBefore(holidayStart) &&
                    end_date.isAfter(holidayEnd))
                ) {
                  if (
                    leaveDays == holiday_data.holiday_accompaying_days ||
                    leaveDays <= holiday_data.holiday_accompaying_days
                  ) {
                    leaveDays = leaveDays + holidaysDiff;
                  }
                }
              }

              statusTrueObj.leaveDays = leaveDays > 0 ? leaveDays : 0;
            }

            /** check  Sandwith rules condition */
            if (holiday_data.configure_sandwich_days > 0) {
              /** check if before and after both policy are true */
              if (holiday_data.apply_sandwich_before_holiday == true && holiday_data.apply_sandwich_after_holiday == true && getHolidayPolicies.length > 0
              ) {
                for (const holiday of getHolidayPolicies) {
                  const holidayStart = moment.utc(
                    holiday.holiday_policy_start_date,
                  );
                  const holidayEnd = moment.utc(
                    holiday.holiday_policy_end_date,
                  );
                  /** get days difference between holiday start date and end date */
                  holidaysDiff = holidayEnd.diff(holidayStart, "days") + 1;

                  /** Define the sandwich window (2 days before and 2 days after the holiday) */
                  const sandwichBeforeStart = holidayStart
                    .clone()
                    .subtract(holiday_data.configure_sandwich_days, "days"); // 2 days before the holiday
                  const sandwichAfterEnd = holidayEnd
                    .clone()
                    .add(holiday_data.configure_sandwich_days, "days"); // 2 days after the holiday

                  /** Ensure leave start date is AFTER the holiday date */
                  if (
                    moment(start_date).isBetween(
                      sandwichBeforeStart,
                      sandwichAfterEnd,
                      null,
                      "[]",
                    ) || // Leave start is within the sandwich window
                    moment(end_date).isBetween(
                      sandwichBeforeStart,
                      sandwichAfterEnd,
                      null,
                      "[]",
                    ) // Leave end is within the sandwich window
                  ) {
                    leaveDays = leaveDays + holidaysDiff; // Add holiday days to the leave days
                  }
                }

                statusTrueObj.leaveDays = leaveDays > 0 ? leaveDays : 0;
              }
              /** check sandwitch rules apply before holiday*/
              if (holiday_data.apply_sandwich_before_holiday == true && getHolidayPolicies.length > 0) {
                for (const holiday of getHolidayPolicies) {
                  const holidayStart = moment.utc(
                    holiday.holiday_policy_start_date,
                  ); // Start date of holiday (e.g., March 14)
                  const holidayEnd = moment.utc(
                    holiday.holiday_policy_end_date,
                  ); // End date of holiday (e.g., March 14)
                  /** Get days difference between holiday start date and end date */
                  holidaysDiff = holidayEnd.diff(holidayStart, "days") + 1;

                  /** Define the sandwich window (configure_sandwich_days days before the holiday) */
                  const sandwichBeforeStart = holidayStart
                    .clone()
                    .subtract(holiday_data.configure_sandwich_days, "days"); // configure_sandwich_days days before the holiday
                  /** Ensure the leave start and end dates are within the sandwich window before the holiday */
                  if (
                    moment(start_date).isBetween(
                      sandwichBeforeStart,
                      holidayStart,
                      null,
                      "[]",
                    ) || // Leave start is within configure_sandwich_days days before holiday
                    moment(end_date).isBetween(
                      sandwichBeforeStart,
                      holidayStart,
                      null,
                      "[]",
                    ) // Leave end is within configure_sandwich_days days before holiday
                  ) {
                    leaveDays = leaveDays + holidaysDiff; // Add holiday days to the leave days
                  }
                }
                statusTrueObj.leaveDays = leaveDays > 0 ? leaveDays : 0;
              }
              /** check sandwitch rules apply after holiday*/
              if (holiday_data.apply_sandwich_after_holiday == true && getHolidayPolicies.length > 0) {
                for (const holiday of getHolidayPolicies) {
                  const holidayStart = moment.utc(holiday.holiday_policy_start_date); // Start date of holiday (e.g., March 14)
                  const holidayEnd = moment.utc(holiday.holiday_policy_end_dates); // End date of holiday (e.g., March 14)
                  /** Get days difference between holiday start date and end date */
                  holidaysDiff = holidayEnd.diff(holidayStart, "days") + 1;

                  /** Define the sandwich window (configure_sandwich_days days after the holiday) */
                  const sandwichAfterEnd = holidayEnd
                    .clone()
                    .add(holiday_data.configure_sandwich_days, "days"); // configure_sandwich_days before the holiday
                  /** Ensure the leave start and end dates are within the sandwich window before the holiday */
                  if (moment(start_date).isBetween(holidayEnd, sandwichAfterEnd, null, "[]",) || // Leave start is within configure_sandwich_days days after holiday
                    moment(end_date).isBetween(holidayEnd, sandwichAfterEnd, null, "[]",
                    ) // Leave end is within configure_sandwich_days days after holiday
                  ) {
                    leaveDays = leaveDays + holidaysDiff; // Add holiday days to the leave days
                  }
                }

                statusTrueObj.leaveDays = leaveDays > 0 ? leaveDays : 0;
              }
              /** check sandwitch rules apply between holiday*/
              if (
                holiday_data.apply_sandwich_between_holiday === true &&
                getHolidayPolicies.length > 0
              ) {
                for (const holiday of getHolidayPolicies) {
                  const holidayStart = moment.utc(
                    holiday.holiday_policy_start_date,
                  );
                  const holidayEnd = moment.utc(
                    holiday.holiday_policy_end_date,
                  );

                  /** Get days difference between holiday start and end */
                  const holidaysDiff =
                    holidayEnd.diff(holidayStart, "days") + 1;

                  /** Define sandwich window */
                  const sandwichBeforeStart = holidayStart
                    .clone()
                    .subtract(holiday_data.configure_sandwich_days, "days");
                  const sandwichAfterEnd = holidayEnd
                    .clone()
                    .add(holiday_data.configure_sandwich_days, "days");

                  /** Check if leave falls within sandwich window */
                  const leaveStartsInside = moment(start_date).isBetween(
                    sandwichBeforeStart,
                    sandwichAfterEnd,
                    null,
                    "[]",
                  );
                  const leaveEndsInside = moment(end_date).isBetween(
                    sandwichBeforeStart,
                    sandwichAfterEnd,
                    null,
                    "[]",
                  );

                  /** Check if leave already covers the holiday */
                  const leaveCoversHoliday =
                    moment(start_date).isSameOrBefore(holidayStart) &&
                    moment(end_date).isSameOrAfter(holidayEnd);
                  /**
                   *  Fix: Add only if BOTH conditions are met:
                   *  The leave falls within the sandwich window.
                   *  The leave does NOT fully cover the holiday.
                   */
                  if (
                    (leaveStartsInside || leaveEndsInside) &&
                    !leaveCoversHoliday
                  ) {
                    const newLeaveDays = leaveDays + holidaysDiff;
                    leaveDays = newLeaveDays;
                  }
                  break; // Prevent multiple additions
                }
                statusTrueObj.leaveDays = leaveDays > 0 ? leaveDays : 0;
              }
            }
          }
        }
        /** ----------------------------------- Weekend policy ----------------------------------------------------------------------- */
        if (getLeaveHolidayRules.weekend_between_leave_period == "count_as_leave") {
          const weekend_data = JSON.parse(
            getLeaveHolidayRules.weekend_between_leave_period_data,
          );
          if (weekend_data) {
            const configure_sandwich_days =
              weekend_data.configure_sandwich_days > 0
                ? weekend_data.configure_sandwich_days
                : 1;
            // Check if leave is near a weekend
            const isBeforeWeekend =
              dayOffNumbers.length > 0 &&
              dayOffNumbers.includes(
                start_date
                  .clone()
                  .subtract(configure_sandwich_days, "day")
                  .day(),
              ); // Friday or Saturday before
            const isAfterWeekend =
              dayOffNumbers.length > 0 &&
              dayOffNumbers.includes(
                end_date.clone().add(configure_sandwich_days, "day").day(),
              ); // Saturday or Sunday after
            /** check weekends consider as a leave, if leave days is equal to configurable days */
            if (weekend_data.weekend_accompaying_days > 0) {
              if (
                leaveDays == weekend_data.weekend_accompaying_days ||
                isAfterWeekend ||
                isBeforeWeekend
              ) {
                leaveDays += dayOffNumbers.length;
              }
              if (
                leaveHour ==
                weekend_data.weekend_accompaying_days *
                working_hour_per_day ||
                isAfterWeekend ||
                isBeforeWeekend
              ) {
                leaveHour += dayOffNumbers.length * working_hour_per_day;
              }

              statusTrueObj.leaveDays =
                LeavePeriodType == "day"
                  ? leaveDays > 0
                    ? leaveDays
                    : 0
                  : leaveHour > 0
                    ? leaveHour
                    : 0;
            }
            /** check  Sandwith rules condition  */
            if (weekend_data.configure_sandwich_days > 0) {
              /** check if before and after both policy are true */
              if (
                weekend_data.apply_sandwich_before_weekend == true &&
                weekend_data.apply_sandwich_after_weekend == true
              ) {
                if (isAfterWeekend || isBeforeWeekend) {
                  leaveDays += dayOffNumbers.length;
                  leaveHour += dayOffNumbers.length * working_hour_per_day;
                }
              }

              /** check condition for after weekend scenario */
              if (weekend_data.apply_sandwich_after_weekend == true && weekend_data.weekend_accompaying_days == 0) {
                if (isAfterWeekend) {
                  leaveDays += dayOffNumbers.length;
                  leaveHour += dayOffNumbers.length * working_hour_per_day;
                }
                statusTrueObj.leaveDays =
                  LeavePeriodType == "day"
                    ? leaveDays > 0
                      ? leaveDays
                      : 0
                    : leaveHour > 0
                      ? leaveHour
                      : 0;
              }
              /** check condition for after weekend scenario */
              if (
                weekend_data.apply_sandwich_before_weekend == true &&
                weekend_data.weekend_accompaying_days == 0
              ) {
                if (isBeforeWeekend) {
                  leaveDays += dayOffNumbers.length;
                  leaveHour += dayOffNumbers.length * working_hour_per_day;
                }
                statusTrueObj.leaveDays =
                  LeavePeriodType == "day"
                    ? leaveDays > 0
                      ? leaveDays
                      : 0
                    : leaveHour > 0
                      ? leaveHour
                      : 0;
              }

              /** check condition for leave between weekends  */
              if (
                weekend_data.apply_sandwich_between_weekend == true &&
                weekend_data.weekend_accompaying_days == 0
              ) {
                const leaveDaysArr = [];
                /** check if is before weekend and after weekend scenario */
                // if (isBeforeWeekend || isAfterWeekend) {
                //   leaveDays += dayOffNumbers.length;
                //   leaveHour += dayOffNumbers.length * working_hour_per_day;
                // }
                /** Get user previous leave before weekend */
                const leaveDaysBeforeWeekend = await UserRequest.findOne({
                  where: {
                    from_user_id: bodyData.user_id,
                    request_status: [request_status.APPROVED],
                  },
                  order: [["id", "DESC"]],
                  raw: true,
                });
                if (leaveDaysBeforeWeekend) {
                  const lastLeaveEndDate = moment(
                    leaveDaysBeforeWeekend.end_date,
                  );
                  const lastLeaveStartDate = moment(
                    leaveDaysBeforeWeekend.start_date,
                  );
                  const previousLeaveDaysDifference =
                    lastLeaveEndDate.diff(lastLeaveStartDate, "days") + 1;
                  // Check if the last leave ended on a Friday & new leave starts on Monday
                  if (
                    dayOffNumbers.length > 0 &&
                    dayOffNumbers.includes(lastLeaveEndDate.day()) && // Last leave ended on a weekend day
                    dayOffNumbers.includes(start_date.day()) // New leave starts on a weekend day
                  ) {
                    // Include Saturday & Sunday as leave days
                    leaveDaysArr.push(
                      lastLeaveEndDate
                        .clone()
                        .add(1, "day")
                        .format("YYYY-MM-DD"),
                    ); // Saturday
                    leaveDaysArr.push(
                      lastLeaveEndDate
                        .clone()
                        .add(2, "day")
                        .format("YYYY-MM-DD"),
                    ); // Sunday
                    leaveDays =
                      leaveDaysArr.length + previousLeaveDaysDifference;
                    leaveHour =
                      leaveDaysArr.length * working_hour_per_day +
                      previousLeaveDaysDifference * working_hour_per_day;
                  }
                }
              }
              statusTrueObj.leaveDays =
                LeavePeriodType == "day"
                  ? leaveDays > 0
                    ? leaveDays
                    : 0
                  : leaveHour > 0
                    ? leaveHour
                    : 0;
            }
          }
        }
      }

      /***  ------------------------------------- Tab-5 Leave Approval ------------------------------------------------------------ */
      const getLeaveApprovalRules: any = await LeaveApprovalPolicy.findOne({
        where: { leave_accural_policy_id: policy_id },
        raw: true,
      });
      const getLeaveApprovalMetaRules: any =
        await LeaveApprovalMetaPolicy.findAll({
          where: {
            leave_accural_policy_id: policy_id,
            approval_type: approval_type.LEAVE,
          },
          raw: true,
        });
      if (getLeaveApprovalRules) {
        /** check leave request require an approval */
        if (getLeaveApprovalRules.leave_request_require_approval == false) {
          statusTrueObj.leave_request_approval_userId = 0;
          statusTrueObj.auto_approval = true;
        }

        /** check leave request require an approval , then get all userid*/
        if (getLeaveApprovalRules.leave_request_require_approval == true) {
          /** Removed if any repaated userid exist */

          const userIds = [
            ...new Set(
              getLeaveApprovalMetaRules.map((item: any) => item.user_id),
            ),
          ];

          statusTrueObj.leave_request_approval_userId = userIds;
          statusTrueObj.auto_approval = false;
        }

        /** check condition for add person to inform via email about leave request. */
        if (getLeaveApprovalRules.add_person_to_inform_about_leave == true) {
          statusTrueObj.email_status = true;
        }
      }
      // let totalDays = 0;
      // // Loop through the days between the start and end date
      // const currentDate = start_date.clone();
      // while (currentDate.isBefore(end_date) || currentDate.isSame(end_date, 'day')) {
      //   const dayOfWeek = currentDate.day();
      //   // If it's not a Saturday or Sunday, count the day
      //   if (!dayOffNumbers.includes(dayOfWeek)) {
      //     totalDays++;
      //   }
      //   currentDate.add(1, 'days'); // Move to the next day
      // }
      statusTrueObj.message = LeavePeriodType == "day" ? leaveDays : leaveHour;
      return statusTrueObj;
    } else {
      return { status: false, messgae: "ERROR_POLICY_NOT_ASSIGNED" };
    }
  } catch (err) {
    console.log("err>>", err);
    return { status: false, data: err };
  }
};

const getCurrentLeaveBalance = async (
  year: any,
  leave_type: any,
  leave_obj: any,
  user_joining_date?: any,
  isResetOn?: any,
  findLeaveResetType?: any,
  leaveBalanceBasedOnEmpContract?: any,
  userLeaveDays?: any,
) => {
  const currentDate = moment();
  const currentYear = year ? moment(year).year() : currentDate.year();

  // Determine the last month and day based on the provided year
  const isCurrentYear = currentYear == currentDate.year();

  const lastMonth = isCurrentYear ? currentDate.month() + 1 : 12; // months are 1-based
  const lastDay = isCurrentYear ? currentDate.date() : 31;

  let totalDays = 0;
  let fullDays = 0;
  let currentDays = 0;
  if (leave_obj) {
    leave_obj.forEach(({ on_date, month, days }: any) => {
      if (on_date === "joining_date" && user_joining_date) {
        on_date = moment(user_joining_date, "DD-MM-YYYY").date();
      }
      // Skip if `on_date` is after the current date in the same month
      if (month === lastMonth && on_date > currentDate.date()) {
        return;
      }

      if (leaveBalanceBasedOnEmpContract == true) {
        fullDays += Number(userLeaveDays);
      } else {
        fullDays += Number(days);
      }
      switch (leave_type) {
        case "yearly":
          if (leaveBalanceBasedOnEmpContract == true) {
            totalDays += Number(userLeaveDays);
            currentDays += Number(userLeaveDays);
          } else {
            totalDays += Number(days);
            currentDays += Number(days);
          }
          break;
        case "monthly":
          // Only current and previous month
          // Skip future months/dates for all types
          if (month > lastMonth || (month === lastMonth && on_date > lastDay)) {
            return;
          }
          if (month == moment().month() + 1) {
            if (leaveBalanceBasedOnEmpContract == true) {
              currentDays += Number(userLeaveDays);
            } else {
              currentDays += Number(days);
            }
          }

          if (isResetOn && findLeaveResetType == "monthly") {
            if (month == moment().month() + 1) {
              if (leaveBalanceBasedOnEmpContract == true) {
                totalDays += Number(userLeaveDays);
              } else {
                totalDays += Number(days);
              }
            }
          } else {
            if (month == lastMonth || month == lastMonth - 1) {
              if (leaveBalanceBasedOnEmpContract == true) {
                totalDays += Number(userLeaveDays);
              } else {
                totalDays += Number(days);
              }
            }
          }
          break;

        case "quarterly": {
          const lastMonth = isCurrentYear ? currentDate.month() + 1 : 12;

          // Determine the current and previous quarters dynamically
          const currentQuarter = Math.ceil(lastMonth / 3);
          const previousQuarter =
            currentQuarter > 1 ? currentQuarter - 1 : null; // If Q1, there's no previous quarter

          // Correctly calculate the quarter for the current entry
          const entryQuarter = Math.ceil(month / 3);

          if (leaveBalanceBasedOnEmpContract == true) {
            currentDays += Number(userLeaveDays);
            if (
              entryQuarter === currentQuarter ||
              (previousQuarter !== null && entryQuarter === previousQuarter)
            ) {
              totalDays += Number(userLeaveDays);
            }
          } else {
            currentDays += Number(days);
            if (
              entryQuarter === currentQuarter ||
              (previousQuarter !== null && entryQuarter === previousQuarter)
            ) {
              totalDays += Number(days);
            }
          }

          break;
        }

        case "half_yearly": {
          if (leaveBalanceBasedOnEmpContract == true) {
            currentDays += Number(userLeaveDays);
          } else {
            currentDays += Number(days);
          }
          // Original half-yearly logic (all months up to now in the half-year)
          const validHalfYear = leave_obj.filter(
            ({ month: m, on_date: o }: any) =>
              m <= lastMonth && !(m === lastMonth && o > currentDate.date()),
          );
          totalDays =
            leaveBalanceBasedOnEmpContract == true
              ? userLeaveDays
              : validHalfYear.reduce(
                (sum: any, { days: d }: any) => sum + d,
                0,
              );
          break;
        }

        case "one_time": {
          if (leaveBalanceBasedOnEmpContract == true) {
            totalDays += Number(userLeaveDays); // Fixed yearly (all valid months)
            currentDays += Number(userLeaveDays);
          } else {
            totalDays += Number(days); // Fixed yearly (all valid months)
            currentDays += Number(days);
          }
          break;
        }

        default:
          console.warn("Invalid leave type");
      }
    });
  }

  return {
    totalDays: formatNumber(totalDays),
    fullDays: formatNumber(fullDays),
    currentDays: formatNumber(currentDays),
  };
};

const calculateProrateLeaveBalance = async (
  joining_date: any,
  annualQuota: any,
) => {
  const joinMonth = moment(joining_date).month() + 1; // Convert from 0-based (Jan=0) to 1-based
  const monthsRemaining = 13 - joinMonth;
  const proratedLeave = Math.floor((annualQuota * monthsRemaining) / 12);
  return proratedLeave;
};

const getUserLeaveBalanceFunc = async (
  userId: any,
  year?: any,
  leave_period_type?: any,
  leave_type_id?: any,
  start_date?: any,
  end_date?: any,
  is_report?: any,
  report_mode?: any,
  organization_id?: any,
) => {
  try {
    report_mode = report_mode ? report_mode : "day";
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1; // getMonth() returns 0-indexed month, so add 1
    const whereObj: any = {
      status: status.ACTIVE,
      organization_id: organization_id,
    };
    year = year ? year : currentYear;
    if (leave_period_type) {
      whereObj.leave_period_type = leave_period_type;
    }

    const leavePolicyWhere: any = {
      id: {
        [Op.in]: [
          sequelize.literal(`(
    select leave_accural_policy_id from nv_user_leave_policy
          where user_id = ${userId} AND user_leave_policy_status = '${user_leave_policy_status.ACTIVE}')`),
        ],
      },
      status: status.ACTIVE,
      [Op.and]: [
        sequelize.where(
          sequelize.fn("YEAR", sequelize.col("leave_calender_year_start_from")),
          { [Op.lte]: year },
        ),
        {
          [Op.or]: [
            { leave_policy_end_date: null },
            year == currentYear
              ? sequelize.where(
                sequelize.fn("DATE", sequelize.col("leave_policy_end_date")),
                { [Op.gte]: `${year}-${currentMonth}-01` },
              )
              : sequelize.where(
                sequelize.fn("YEAR", sequelize.col("leave_policy_end_date")),
                { [Op.gte]: year },
              ),
          ],
        },
      ],
    };

    if (leave_type_id) {
      leavePolicyWhere.leave_type_id = leave_type_id;
    }

    const getLeaveTypeList: any = await LeaveTypeModel.findAll({
      attributes: [
        "id",
        "name",
        "leave_deduction_type",
        "leave_type_color",
        "leave_period_type",
      ],
      where: whereObj,
      include: [
        {
          model: LeaveAccuralPolicy,
          as: "leave_accural_policy",
          where: leavePolicyWhere,
          attributes: [
            "id",
            "leave_calender_year_start_from",
            "leave_policy_end_date",
            "stop_policy_accural_timewise_type",
            "stop_policy_accural_timewise_value",
            "leave_policy_accural",
            "leave_policy_name",
            "has_leave_policy_default",
            "leave_policy_description",
            "leave_type_id",
            "pro_rate_accural",
            "effective_after_count",
            "effective_after_type",
            "effective_from_type",
          ],
        },
      ],
      raw: true,
      nest: true,
    });

    if (getLeaveTypeList?.length > 0) {
      for (const leaveType of getLeaveTypeList) {
        if (leaveType?.leave_accural_policy) {
          const findLeavePolicyData = leaveType?.leave_accural_policy;

          const whereObj: any = {
            leave_accural_policy_id: findLeavePolicyData?.id,
            user_id: userId,
            user_leave_policy_status: user_leave_policy_status.ACTIVE,
          };

          const findUserLeave: any = await UserLeavePolicy.findOne({
            where: whereObj,
            raw: true,
          });
          const userPolicyHistoryWhere: any = {
            leave_user_policy_id: findUserLeave?.id,
            user_leave_policy_history_status:
              user_leave_policy_history_status.ACTIVE,
          };

          // Apply date range filter if start_date and end_date are provided
          if (start_date && end_date) {
            userPolicyHistoryWhere[Op.and] = [
              where(
                fn("DATE", col("leave_balance_start_date")),
                "<=",
                end_date,
              ),
              where(
                fn("DATE", col("leave_balance_end_date")),
                ">=",
                start_date,
              ),
            ];
          }
          if (year) {
            if (!userPolicyHistoryWhere[Op.and]) {
              userPolicyHistoryWhere[Op.and] = [];
            }
            const startYear = new Date(findLeavePolicyData.leave_calender_year_start_from).getFullYear();
            let endYear = findLeavePolicyData.leave_policy_end_date
            if (endYear) {
              endYear = new Date(endYear).getFullYear();
              if (startYear !== endYear) {
                userPolicyHistoryWhere[Op.or] = [
                  Sequelize.where(
                    Sequelize.fn("YEAR", Sequelize.col("leave_accural_date")),
                    { [Op.gte]: startYear, [Op.lte]: endYear }
                  )
                ];
              } else {
                userPolicyHistoryWhere[Op.or] = [
                  Sequelize.where(
                    Sequelize.fn("YEAR", Sequelize.col("leave_accural_date")),
                    startYear
                  )
                ];
              }
            } else {
              userPolicyHistoryWhere[Op.and] = [
                Sequelize.where(
                  Sequelize.fn("YEAR", Sequelize.col("leave_accural_date")),
                  year
                )
              ];
            }
          }

          const findUserLeaveHistory: any =
            await UserLeavePolicyHistory.findAll({
              attributes: [
                "leave_total_day_balance",
                "leave_current_day_balance",
                "leave_total_hour_balance",
                "leave_current_hour_balance",
                "leave_accural_date",
                "carry_forward_day_balance",
                "carry_forward_hour_balance",
              ],
              where: userPolicyHistoryWhere,
              order: [["leave_accural_date", "DESC"]],
              raw: true,
            });

          if (findLeavePolicyData) {
            const user = await User.findOne({ attributes: ["user_joining_date"], where: { id: userId }, raw: true });
            const findUserMeta = await UserMeta.findOne({
              attributes: ["probation_length"],
              where: { user_id: userId },
              raw: true,
            });

            // Calculate the probation end date
            const probationEndDate = moment(user?.user_joining_date).clone().add(findUserMeta?.probation_length, "days");
            const isUserOnProbation = moment().isSameOrBefore(moment(probationEndDate), "day",);

            let baseDate;
            let checkEffective = true
            // Determine base date based on effective_from_type
            if (findLeavePolicyData.effective_from_type == "date_of_joining") {
              baseDate = moment(user?.user_joining_date);
              checkEffective = false
            } else if (
              findLeavePolicyData.effective_from_type ==
              "after_probation_end" &&
              isUserOnProbation
            ) {
              checkEffective = false
              baseDate = moment(probationEndDate);
            }

            // Calculate the effective date
            const effectiveDate = moment(baseDate);
            if (findLeavePolicyData.effective_after_type === "days" && !checkEffective) {
              effectiveDate.add(findLeavePolicyData.effective_after_count, "days",);
            } else if (findLeavePolicyData.effective_after_type === "months" && !checkEffective) {
              effectiveDate.add(findLeavePolicyData.effective_after_count, "months");
            }
            const is_effective = !checkEffective ? moment().isSameOrAfter(effectiveDate) : true;
            leaveType.has_leave_unlimited = findLeavePolicyData.leave_policy_accural ? false : true;

            const resetLeaveBalance = findLeavePolicyData.leave_policy_accural
              ? findLeavePolicyData.pro_rate_accural == "pro_rate" && moment(user?.user_joining_date).isSameOrAfter(moment(
                findLeavePolicyData.leave_calender_year_start_from,
                "YYYY-MM",)
                .startOf("month")
                .format("YYYY-MM-DD"),
              )
                ? await calculateProrateLeaveBalance(user?.user_joining_date, await getResetLeaveBalance(
                  findUserLeaveHistory,
                  findLeavePolicyData.leave_policy_reset,
                  is_report,
                  start_date,
                  end_date,
                  organization_id,
                ),
                )
                : await getResetLeaveBalance(
                  findUserLeaveHistory,
                  findLeavePolicyData.leave_policy_reset,
                  is_report,
                  start_date,
                  end_date,
                  organization_id
                )
              : 0;
            const totalBalance = !is_effective ? 0 : findUserLeaveHistory.length > 0 ? resetLeaveBalance : 0;
            leaveType.leave_balance = findLeavePolicyData?.leave_policy_accural == true ? totalBalance || 0 : 0;
            if (is_report == false || is_report == "false") {
              const findLeaveApplication =
                await LeaveApplicationRulesPolicy.findOne({
                  where: { leave_accural_policy_id: findLeavePolicyData.id },
                  raw: true,
                });
              const findLeaveRestriction = await LeaveRestrictionPolicy.findOne(
                {
                  where: { leave_accural_policy_id: findLeavePolicyData.id },
                  raw: true,
                },
              );
              const findLeaveHoliday = await LeaveHolidayWeekendPolicy.findOne({
                where: { leave_accural_policy_id: findLeavePolicyData.id },
                raw: true,
              });

              leaveType.leave_accural_policy = {
                ...leaveType.leave_accural_policy,
                ...findLeaveApplication,
                ...findLeaveRestriction,
                ...findLeaveHoliday,
              };
            } else {
              leaveType.leave_accural_policy = {};
            }

            const whereObj: any = {
              from_user_id: userId,
              leave_request_type: findLeavePolicyData.leave_type_id,
              request_status: {
                [Op.in]: [request_status.APPROVED, request_status.PENDING],
              },
            };

            if (start_date && end_date) {
              whereObj[Op.and] = [
                sequelize.where(
                  sequelize.fn("DATE", sequelize.col("start_date")),
                  {
                    [Op.lte]: end_date,
                  },
                ),
                sequelize.where(
                  sequelize.fn("DATE", sequelize.col("end_date")),
                  {
                    [Op.gte]: start_date,
                  },
                ),
              ];
            } else if (start_date && !end_date) {
              whereObj[Op.and] = [
                sequelize.where(
                  sequelize.fn("DATE", sequelize.col("start_date")),
                  {
                    [Op.lte]: start_date,
                  },
                ),
                sequelize.where(
                  sequelize.fn("DATE", sequelize.col("end_date")),
                  {
                    [Op.gte]: start_date,
                  },
                ),
              ];
            } else if (end_date && !start_date) {
              whereObj[Op.and] = [
                sequelize.where(
                  sequelize.fn("DATE", sequelize.col("start_date")),
                  {
                    [Op.lte]: end_date,
                  },
                ),
                sequelize.where(
                  sequelize.fn("DATE", sequelize.col("end_date")),
                  {
                    [Op.gte]: end_date,
                  },
                ),
              ];
            }


            // const existingLeaveHour: any = await calculateUserLeaveHours(userId, findGeneralSetting?.working_hours_per_day, findGeneralSetting?.leave_period_type, findLeavePolicyData.leave_type_id)
            const usedData: any = await UserRequest.findAll({
              attributes: [
                // DAY based leaves
                [
                  Sequelize.literal(`
                    SUM(CASE
                      WHEN leave_period_type = 'day' AND DATE(end_date) >= CURDATE()
                      THEN leave_days ELSE 0
                    END)
                  `),
                  "day_planned_leaves",
                ],
                [
                  Sequelize.literal(`
                    SUM(CASE
                      WHEN leave_period_type = 'day' AND DATE(end_date) < CURDATE()
                      THEN leave_days ELSE 0
                    END)
                  `),
                  "day_used_leaves",
                ],
                [
                  Sequelize.literal(`
                    SUM(CASE
                      WHEN leave_period_type = 'day'
                      THEN leave_days ELSE 0
                    END)
                  `),
                  "day_total",
                ],

                // HOUR based leaves
                [
                  Sequelize.literal(`
                    SUM(CASE
                      WHEN leave_period_type = 'hour' AND DATE(end_date) >= CURDATE()
                      THEN leave_days ELSE 0
                    END)
                  `),
                  "hour_planned_leaves",
                ],
                [
                  Sequelize.literal(`
                    SUM(CASE
                      WHEN leave_period_type = 'hour' AND DATE(end_date) < CURDATE()
                      THEN leave_days ELSE 0
                    END)
                  `),
                  "hour_used_leaves",
                ],
                [
                  Sequelize.literal(`
                    SUM(CASE
                      WHEN leave_period_type = 'hour'
                      THEN leave_days ELSE 0
                    END)
                  `),
                  "hour_total",
                ],
              ],
              where: whereObj,
              raw: true,
            });

            leaveType.used_day_leave = { total: formatNumber(usedData[0]?.day_total) || 0 };
            leaveType.used_hour_leave = { total: formatNumber(usedData[0]?.hour_total) || 0 };
            leaveType.used_leave = {
              planned_leaves:
                report_mode == "day"
                  ? formatNumber(usedData[0]?.day_planned_leaves)
                  : formatNumber(usedData[0]?.hour_planned_leaves),
              used_leaves:
                report_mode == "day"
                  ? formatNumber(usedData[0]?.day_used_leaves)
                  : formatNumber(usedData[0]?.hour_used_leaves),
              total:
                report_mode == "day"
                  ? formatNumber(usedData[0]?.day_total)
                  : formatNumber(usedData[0]?.hour_total),
            };
            const totalCurrentBalance = report_mode == "day" ? formatNumber(usedData[0]?.day_total) : formatNumber(usedData[0]?.hour_total);
            leaveType.user_remaining_leave = !is_effective ? 0 : findLeavePolicyData?.leave_policy_accural == true ? formatNumber(totalBalance - formatNumber(totalCurrentBalance),) || 0 : 0;
          }
        }
      }
    }
    return { data: getLeaveTypeList };
  } catch (error) {
    console.error(error);
    return { status: false, message: "SOMETHING_WENT_WRONG", error };
  }
};

/** Get staff count for that organizatio */
const getStaffCount = async (organizationId: any,) => {
  const getUserId: any = await getSuperAdminUserIdEnhanced(organizationId)
  const staffCount: any = await User.count({
    where: {
      organization_id: organizationId,
      id: {
        [Op.notIn]: getUserId,
      },
    },
  });
  /** Get subscription based user count */
  const selectQuery = `SELECT sp.id, sp.sp_limit_max, splan.subs_plan_name
  FROM subscription_purchased AS sp
  JOIN users AS u ON u.id = sp.user_id
  JOIN subscription_plan AS splan ON splan.id = sp.plan_id
  WHERE sp.sp_status ='active' AND u.organization_id='${organizationId}' AND splan.subs_plan_category = 'core'
  ORDER BY sp.id DESC;`;

  const getSubscriptionData: any = await sequelize.query(selectQuery, {
    type: QueryTypes.SELECT,
  });
  if (getSubscriptionData.length > 0) {
    const subscriptionUsers = getSubscriptionData[0].sp_limit_max;

    /** Compare plan count and staff count */
    if (staffCount == subscriptionUsers) {
      return { status: false, staffCount: subscriptionUsers };
    }
  }
  return { status: true };
};

// Function to convert month name to month number
async function getMonthNumber(monthName: any) {
  const date = new Date(Date.parse(monthName + " 1, 2000"));
  const monthNumber = date.getMonth() + 1;
  return isNaN(monthNumber) ? null : monthNumber;
}

// const calculateExpiryDate = async (startDate: any, duration: any) => {
//   try {
//     const date = moment(startDate);
//     switch (duration) {
//       case '2_month':
//         return date.add(2, 'months').format('YYYY-MM-DD');
//       case '4_month':
//         return date.add(4, 'months').format('YYYY-MM-DD');
//       case '6_month':
//         return date.add(6, 'months').format('YYYY-MM-DD');
//       case '1_year':
//         return date.add(1, 'year').format('YYYY-MM-DD');
//       case 'custom':
//         return null;
//       default:
//         return date.format('YYYY-MM-DD');
//     }
//   } catch (error) {
//     return null
//   }
// };

const calculateExpiryDate = async (startDate: any, duration: any) => {
  try {
    const today = moment();
    const start = moment(startDate);
    let expiryDate: moment.Moment;
    switch (duration) {
      case "2_month":
        expiryDate = start.clone().add(2, "months");
        if (expiryDate.isSameOrBefore(today, "day")) {
          return today.clone().add(2, "months").format("YYYY-MM-DD");
        } else {
          return expiryDate.add(2, "months").format("YYYY-MM-DD");
        }

      case "4_month":
        expiryDate = start.clone().add(4, "months");
        if (expiryDate.isSameOrBefore(today, "day")) {
          return today.clone().add(4, "months").format("YYYY-MM-DD");
        } else {
          return expiryDate.add(4, "months").format("YYYY-MM-DD");
        }

      case "6_month":
        expiryDate = start.clone().add(6, "months");
        if (expiryDate.isSameOrBefore(today, "day")) {
          return today.clone().add(6, "months").format("YYYY-MM-DD");
        } else {
          return expiryDate.add(6, "months").format("YYYY-MM-DD");
        }

      case "1_year":
        expiryDate = start.clone().add(1, "year");
        if (expiryDate.isSameOrBefore(today, "day")) {
          return today.clone().add(1, "year").format("YYYY-MM-DD");
        } else {
          return expiryDate.add(1, "year").format("YYYY-MM-DD");
        }

      case "custom": {
        const dayGap = start.diff(today, "days"); // Compare endDate to today
        if (dayGap <= 0) {
          return today.clone().add(1, "days").format("YYYY-MM-DD");
        } else {
          // If expiry already passed or today, use 1
          return start.clone().add(dayGap, "days").format("YYYY-MM-DD");
        }
      }

      default:
        return today.format("YYYY-MM-DD");
    }
  } catch (error) {
    return null;
  }
};

/** check userStorageLimit */
const checkUserStorageLimit = async (organizationId: any) => {
  const selectQuery = `SELECT
    MAX(sp.id) AS id,
    SUM(CASE 
      WHEN splan.subs_plan_category = 'core' THEN sp.sp_storage_value
      WHEN splan.subs_plan_category = 'storage' THEN sp.sp_limit_max
      ELSE 0
    END) AS subs_limit_max,
    GROUP_CONCAT(splan.subs_plan_name) AS subs_plan_name,
    MAX(sp.user_id) AS user_id,
    MAX(splan.sub_storage_size) AS sub_storage_size
FROM
    subscription_purchased AS sp
JOIN
    users AS u ON u.id = sp.user_id
JOIN
    subscription_plan AS splan ON splan.id = sp.plan_id
WHERE
    sp.sp_status IN('active', 'inactive')
    AND u.organization_id = '${organizationId}'
    AND splan.subs_plan_category IN ('storage', 'core')
GROUP BY
    user_id
ORDER BY
    id DESC;`;

  const getSubscriptionData: any = await sequelize.query(selectQuery, {
    type: QueryTypes.SELECT,
  });

  if (getSubscriptionData.length > 0) {
    return {
      status: true,
      limitInMB: getSubscriptionData[0].subs_limit_max,
      subs_storage_size: getSubscriptionData[0].sub_storage_size,
    };
  } else {
    return { status: false, limitInMB: 0, subs_storage_size: 'GB' }
  }
};

const calculateTotalDaysAndHours = async (
  value: any,
  workingHoursPerDay: any,
  type: string,
) => {
  if (type == "hours_to_days") {
    const totalDays = Math.floor(value / workingHoursPerDay);
    const remainingHours = +(value % workingHoursPerDay).toFixed(2);
    return { days: totalDays, hours: remainingHours };
  } else if (type == "days_to_hours") {
    const totalHours = +(value * workingHoursPerDay).toFixed(2);
    return { hours: totalHours };
  }
};

const calculateUserLeaveHours = async (
  user_id: number,
  workingHoursPerDay: number,
  leave_period_type: string,
  leave_policy_id?: number,
) => {
  const whereObj: any = {
    from_user_id: user_id,
    request_status: {
      [Op.in]: [request_status.APPROVED, request_status.PENDING],
    },
  };
  if (leave_policy_id) {
    whereObj.leave_request_type = Number(leave_policy_id);
  }
  const allLeaves: any = await UserRequest.findAll({
    attributes: ["leave_days", "leave_period_type", "start_date", "end_date"],
    where: whereObj,
    raw: true,
  });

  let total = 0;
  let used = 0;
  let planned = 0;
  const now = moment();
  for (const leave of allLeaves) {
    const leaveInHours =
      leave.leave_period_type == "day"
        ? (
          await calculateTotalDaysAndHours(
            leave.leave_days,
            workingHoursPerDay,
            "days_to_hours",
          )
        )?.hours
        : leave.leave_days;

    total += Number(leaveInHours);

    const start = moment(leave.start_date);
    const end = moment(leave.end_date);

    if (start.isSameOrBefore(now, "day") && end.isSameOrAfter(now, "day")) {
      used += Number(leaveInHours);
    } else if (start.isAfter(now, "day") && end.isAfter(now, "day")) {
      planned += Number(leaveInHours);
    }
  }

  return {
    hours: {
      total,
      used,
      planned,
    },

    converted: {
      total:
        leave_period_type == "day"
          ? await calculateTotalDaysAndHours(
            total,
            workingHoursPerDay,
            "days_to_hours",
          )
          : await calculateTotalDaysAndHours(
            total,
            workingHoursPerDay,
            "hours_to_days",
          ),
      used: await calculateTotalDaysAndHours(
        used,
        workingHoursPerDay,
        "hours_to_days",
      ),
      planned: await calculateTotalDaysAndHours(
        planned,
        workingHoursPerDay,
        "hours_to_days",
      ),
    },
  };
};

async function convertFutureLeaves(
  user_id: any,
  option: any,
  working_hours_per_day: any,
  organization_id: any,
) {
  const today = moment().startOf("day");

  // Fetch future leaves from DB using provided function
  const futureLeaves = await UserRequest.findAll({
    where: {
      start_date: { [Op.gt]: today },
      request_status: {
        [Op.in]: [request_status.APPROVED, request_status.PENDING],
      },
      from_user_id: {
        [Op.in]: sequelize.literal(`(
          SELECT id
          FROM nv_users
          WHERE organization_id = '${organization_id}'
          AND user_status NOT IN ('deleted', 'cancelled')
        )`)
      }
    },
    raw: true,
  });

  if (option === "cancel") {
    for (const leave of futureLeaves) {
      await UserRequest.update(
        { request_status: request_status.CANCELLED },
        { where: { id: leave.id } },
      );
    }
    return { message: "All future leaves have been cancelled." };
  }

  if (option === "convert") {
    for (const leave of futureLeaves) {
      const updates: any = {
        updated_by: user_id,
      };

      // Convert days to hours or hours to days
      if (leave.leave_period_type === "day") {
        updates.leave_days = formatNumber(
          leave.leave_days * working_hours_per_day,
        );

        if (leave?.leave_days_obj) {
          const daysObj = JSON.parse(leave.leave_days_obj);
          const updatedObj = JSON.stringify(
            daysObj.map((d: any) => {
              // Convert based on type (full_day, first_half, second_half)
              const hourValue =
                d.type === "full_day"
                  ? working_hours_per_day
                  : working_hours_per_day / 2;

              if (
                d.type == "day_off" ||
                d.type == "holiday" ||
                d.type == "day_off_holiday"
              ) {
                return {
                  date: d.date,
                  total_hours: formatNumber(hourValue),
                  type: d.type,
                };
              } else {
                return {
                  date: d.date,
                  total_hours: formatNumber(hourValue),
                };
              }
            }),
          );
          updates.leave_days_obj = updatedObj;
          updates.leave_days = formatNumber(
            await calculateLeaveTotal(updatedObj, "hour"),
          );
        }
        updates.leave_period_type = "hour";
      } else if (leave.leave_period_type === "hour") {
        updates.leave_days = formatNumber(
          leave.leave_days / working_hours_per_day,
        );

        if (leave?.leave_days_obj) {
          const hoursObj = JSON.parse(leave.leave_days_obj);

          const updatedObj = JSON.stringify(
            hoursObj.map((d: any) => {
              // Determine day type based on total_hours
              let type = "first_half";
              if (d.total_hours > working_hours_per_day / 2) {
                type = "full_day";
              }

              return {
                date: d.date,
                type:
                  d.type == "day_off" ||
                    d.type == "holiday" ||
                    d.type == "day_off_holiday"
                    ? d.type
                    : type,
              };
            }),
          );
          updates.leave_days_obj = updatedObj;
          updates.leave_days = formatNumber(
            await calculateLeaveTotal(updatedObj, "day"),
          );
        }

        updates.leave_period_type = "day";
      }

      await UserRequest.update(updates, { where: { id: leave.id } });
    }

    return { message: "All future leaves have been converted." };
  }

  return { error: 'Invalid option provided. Use "cancel" or "convert".' };
}

async function calculateLeaveTotal(leave_days_obj: any, type: any) {
  let total = 0;

  // Parse leave_days_obj if it's a string
  leave_days_obj = leave_days_obj ? JSON.parse(leave_days_obj) : [];
  if (type === "hour") {
    // Sum total_hours for hour-based leave
    for (const entry of leave_days_obj) {
      total += parseFloat(entry.total_hours) || 0;
    }
  } else if (type === "day") {
    // Convert types to appropriate values and sum for day-based leave
    for (const entry of leave_days_obj) {
      switch (entry.type) {
        case "full_day":
          total += 1;
          break;
        case "first_half":
        case "second_half":
          total += 0.5;
          break;
        case "day_off":
        case "holiday":
        default:
          total += 0;
      }
    }
  }

  return total;
}

const getResetLeaveBalance = async (
  data: any,
  isResetOn: any,
  is_report: any = false,
  start_date?: any,
  end_date?: any,
  organization_id?: any,
) => {
  if (!Array.isArray(data) || data.length === 0) return 0;

  // Sort entries by leave_accural_date DESC
  const sorted = [...data].sort((a: any, b: any) =>
    moment(b.leave_accural_date).diff(moment(a.leave_accural_date)),
  );

  const latest = sorted[0];
  const generalSettings = await getGeneralSettingObj(organization_id);

  const latestBalance =
    generalSettings?.leave_period_type == "day"
      ? latest?.leave_current_day_balance + latest?.carry_forward_day_balance ||
      0
      : latest?.leave_current_hour_balance +
      latest?.carry_forward_hour_balance || 0;

  if (is_report && start_date && end_date) {
    return parseFloat(
      sorted.reduce((sum, entry) => {
        return (
          sum +
          (generalSettings?.leave_period_type == "day"
            ? entry?.leave_current_day_balance +
            entry?.carry_forward_day_balance || 0
            : entry?.leave_current_hour_balance +
            entry?.carry_forward_hour_balance || 0)
        );
      }, 0),
    );
  } else {
    if (isResetOn) {
      // ✅ Only latest balance
      return latestBalance;
    } else {
      // 🔁 Get latest + one more from a different month
      const latestMonth = moment(latest.leave_accural_date).format("YYYY-MM");

      const diffMonthEntry = sorted.find((entry, index) => {
        if (index === 0) return false;
        return (
          moment(entry.leave_accural_date).format("YYYY-MM") !== latestMonth
        );
      });

      const diffBalance =
        generalSettings?.leave_period_type == "day"
          ? diffMonthEntry?.leave_current_day_balance +
          diffMonthEntry?.carry_forward_day_balance || 0
          : diffMonthEntry?.leave_current_hour_balance +
          diffMonthEntry?.carry_forward_hour_balance || 0;
      return latestBalance + diffBalance;
    }
  }
};

const handleLeaveAccrual = async (findLeaveType = "yearly", findLeaveObj: any = [], findExistingAssigned: any = {}, findUserMeta: any, generalSettings: any, currentYear = moment().year(), req: any, user: any, leaveBalanceBasedOnEmpContract: any, findLeaveYear: any, findLeaveEnd: any, isResetOn: any = false) => {
  const currentDate = moment();

  const getStartDate = (on_date: any, month: any) => {
    let day = on_date;
    if (on_date === "joining_date" && user?.user_joining_date) {
      day = moment(user.user_joining_date, "DD-MM-YYYY").date();
    }
    return moment()
      .year(currentYear)
      .month(month - 1)
      .date(day);
  };

  const getEndDate = (entryMonth: number, type: string) => {
    if (type === "monthly")
      return moment().year(currentYear).month(entryMonth).endOf("month");
    if (type === "quarterly")
      return moment()
        .year(currentYear)
        .month(entryMonth + 2)
        .endOf("month");
    if (type === "half_yearly")
      return moment()
        .year(currentYear)
        .month(entryMonth + 5)
        .endOf("month");
    return findLeaveEnd ? moment(findLeaveEnd) : null;
  };

  const getCondition = (month: number) => ({
    leave_user_policy_id: findExistingAssigned?.id,
    [Op.and]: [
      Sequelize.where(
        Sequelize.fn("MONTH", Sequelize.col("leave_accural_date")),
        month,
      ),
      Sequelize.where(
        Sequelize.fn("YEAR", Sequelize.col("leave_accural_date")),
        currentYear,
      ),
    ],
  });

  const createOrUpdate = async (
    condition: any,
    data: any,
    accural_date: any,
  ) => {
    const existing = await UserLeavePolicyHistory.findOne({ where: condition, raw: true });

    if (existing) {
      await UserLeavePolicyHistory.update(data, { where: condition });
    } else {
      await UserLeavePolicyHistory.create({
        ...data,
        leave_user_policy_id: findExistingAssigned?.id,
        created_by: req.user.id,
        leave_accural_date: accural_date,
      });
    }
  };
  // Fetch user leave balances for this policy
  const findAccural = await LeaveAccuralPolicy.findOne({
    where: { id: findExistingAssigned.leave_accural_policy_id },
  });
  let carryForwardLeave = 0;
  const carry_forward_date_obj = findAccural?.carry_forward_date
    ? JSON.parse(findAccural?.carry_forward_date)
    : {};

  const carry_forward_number = carry_forward_date_obj.carry_forward_number || 0
  const carry_forward_number_by_type = carry_forward_date_obj.carry_forward_number_by_type || "percentage"
  const carry_forward_max_limit = carry_forward_date_obj.carry_forward_max_limit || 0
  const carry_forward_min_limit = carry_forward_date_obj.carry_forward_min_limit || 0
  const working_hours_per_day = generalSettings?.working_hours_per_day || 0
  if (isResetOn) {
    const leavePolicyData = (await getUserLeaveBalanceFunc(user.id, currentYear, null, findAccural?.id, null, null, false, generalSettings?.leave_period_type)).data

    if (carry_forward_number_by_type == "percentage") {
      carryForwardLeave = Math.floor(
        (leavePolicyData[0]?.user_remaining_leave *
          Number(carry_forward_number)) /
        100,
      );
    } else if (carry_forward_number_by_type == "unit") {
      carryForwardLeave = Math.min(
        leavePolicyData[0]?.user_remaining_leave,
        carry_forward_number,
      );
    }

    carryForwardLeave =
      carry_forward_max_limit > 0
        ? Math.max(
          carry_forward_min_limit,
          Math.min(carryForwardLeave, carry_forward_max_limit),
        )
        : carryForwardLeave;
  }

  if (["yearly", "one_time"].includes(findLeaveType)) {
    const entry: any = findLeaveObj[0];
    const on_date = entry.on_date;
    const month = entry.month;

    const accrualDate = getStartDate(on_date, month);
    if (!currentDate.isSameOrAfter(accrualDate)) return;

    const leaveUpdateObj: any = {
      updated_by: req.user.id,
      leave_balance_start_date: findLeaveYear,
      leave_balance_end_date: findLeaveEnd ? findLeaveEnd : null,
    };

    const balance = leaveBalanceBasedOnEmpContract ? findUserMeta?.leave_days || 0 : entry.days || 0

    if (generalSettings?.leave_period_type === "day") {
      leaveUpdateObj.leave_total_day_balance = parseFloat(balance);
      leaveUpdateObj.leave_current_day_balance = parseFloat(balance);
      leaveUpdateObj.carry_forward_day_balance = isResetOn
        ? carryForwardLeave || 0
        : 0;

      leaveUpdateObj.leave_total_hour_balance = working_hours_per_day
        ? formatNumber(parseFloat(balance) * working_hours_per_day)
        : 0;
      leaveUpdateObj.leave_current_hour_balance = working_hours_per_day
        ? formatNumber(parseFloat(balance) * working_hours_per_day)
        : 0;
      leaveUpdateObj.carry_forward_hour_balance = isResetOn
        ? formatNumber(carryForwardLeave * working_hours_per_day)
        : 0;
    } else {
      leaveUpdateObj.leave_total_hour_balance = parseFloat(balance);
      leaveUpdateObj.leave_current_hour_balance = parseFloat(balance);
      leaveUpdateObj.carry_forward_hour_balance = isResetOn
        ? carryForwardLeave || 0
        : 0;

      leaveUpdateObj.leave_total_day_balance = working_hours_per_day
        ? formatNumber(parseFloat(balance) / working_hours_per_day)
        : 0;
      leaveUpdateObj.leave_current_day_balance = working_hours_per_day
        ? formatNumber(parseFloat(balance) / working_hours_per_day)
        : 0;
      leaveUpdateObj.carry_forward_day_balance = isResetOn
        ? formatNumber(carryForwardLeave / working_hours_per_day)
        : 0;
    }

    const condition = {
      leave_user_policy_id: findExistingAssigned.id,
      [Op.and]: [
        Sequelize.where(
          Sequelize.fn("YEAR", Sequelize.col("leave_accural_date")),
          currentYear,
        ),
      ],
    };

    await createOrUpdate(condition, leaveUpdateObj, moment());
  } else {
    let total_day = 0;

    for (const entry of findLeaveObj) {
      const entryMonth = parseInt(entry.month) - 1;
      const accrualDate = getStartDate(entry.on_date, entry.month);
      const endDate = getEndDate(entryMonth, findLeaveType);
      const current_day = parseFloat(entry.days);

      if (!currentDate.isSameOrAfter(accrualDate)) continue;

      total_day += current_day;

      const leaveUpdateObj: any = {
        updated_by: req.user.id,
        leave_balance_start_date: accrualDate.format("YYYY-MM-DD"),
        leave_balance_end_date: endDate?.format("YYYY-MM-DD"),
      };

      const balance = leaveBalanceBasedOnEmpContract
        ? { total: findUserMeta?.leave_days, current: findUserMeta?.leave_days }
        : {
          total: total_day,
          current: current_day,
        };

      if (generalSettings?.leave_period_type === "day") {
        const dayValue = parseFloat(balance.total ?? balance);
        const currentDay = parseFloat(balance.current ?? balance);

        leaveUpdateObj.leave_total_day_balance = dayValue || 0;
        leaveUpdateObj.leave_current_day_balance = currentDay || 0;
        leaveUpdateObj.carry_forward_day_balance = isResetOn ? carryForwardLeave || 0 : 0;

        leaveUpdateObj.leave_total_hour_balance = working_hours_per_day
          ? formatNumber(dayValue * working_hours_per_day)
          : 0;
        leaveUpdateObj.leave_current_hour_balance = working_hours_per_day
          ? formatNumber(currentDay * working_hours_per_day)
          : 0;
        leaveUpdateObj.carry_forward_hour_balance = isResetOn
          ? formatNumber(carryForwardLeave * working_hours_per_day)
          : 0;
      } else {
        const hourValue = parseFloat(balance.total ?? balance);
        const currentHour = parseFloat(balance.current ?? balance);

        leaveUpdateObj.leave_total_hour_balance = hourValue || 0;
        leaveUpdateObj.leave_current_hour_balance = currentHour || 0;
        leaveUpdateObj.carry_forward_hour_balance = isResetOn ? carryForwardLeave || 0 : 0;

        leaveUpdateObj.leave_total_day_balance = working_hours_per_day
          ? formatNumber(hourValue / working_hours_per_day)
          : 0;
        leaveUpdateObj.leave_current_day_balance = working_hours_per_day
          ? formatNumber(currentHour / working_hours_per_day)
          : 0;
        leaveUpdateObj.carry_forward_day_balance = isResetOn
          ? formatNumber(carryForwardLeave / working_hours_per_day)
          : 0;
      }

      const condition = getCondition(entryMonth + 1);
      await createOrUpdate(condition, leaveUpdateObj, endDate);
    }
  }
};

// Function to convert size to bytes
const convertToBytes = (size: number, unit: string): number => {
  switch (unit.toUpperCase()) {
    case "KB":
      return size * 1024;
    case "MB":
      return size * 1024 * 1024;
    case "GB":
      return size * 1024 * 1024 * 1024;
    case "B":
      return size;
    default:
      return size;
  }
};

/** check storage size of document category */
const checkStorageSize = async (req: any, res: any) => {
  /** check user storage limit */
  let totalStorageLimit = global.config.FILE_SIZE_LIMIT;
  let unit = "GB";

  /** check if user has any subscription plan of storage */
  const checkUserStorage: any = await checkUserStorageLimit(
    req.user.organization_id,
  );
  if (checkUserStorage.status) {
    totalStorageLimit += checkUserStorage.limitInMB;
    unit = checkUserStorage.subs_storage_size;
  }
  /** Get all category items */
  const categoryItems = await DocumentCategory.findAll({
    where: {
      organization_id: req.user.organization_id,
      category_status: { [Op.not]: category_status.DELETED },
    },
    attributes: ["id", "category_image"],
    include: [
      {
        model: Item,
        as: "item_image",
        where: { item_status: { [Op.not]: item_status.DELETED } },
        attributes: ["item_size"],
      },
      {
        model: DocumentCategoryItem,
        as: "document_category",
        where: {
          document_category_item_status: {
            [Op.not]: document_category_item_status.DELETED,
          },
        },
        attributes: ["category_id"],
        required: false,
        include: [
          {
            model: Item,
            as: "document_item",
            where: { item_status: { [Op.not]: item_status.DELETED } },
            attributes: ["item_size"],
          },
        ],
      },
    ],
    raw: true,
    nest: true,
  });

  const getBytes = convertToBytes(totalStorageLimit, unit);

  // Calculate total size
  let totalSize = 0;
  categoryItems.forEach((category: any) => {
    if (category.item_image && category.item_image.item_size) {
      totalSize += Number(category.item_image.item_size) || 0;
    }
    if (category.document_category) {
      if (
        category.document_category.document_item &&
        category.document_category.document_item.item_size
      ) {
        totalSize +=
          Number(category.document_category.document_item.item_size) || 0;
      }
    }
  });

  // Check if new upload would exceed limit
  if (req.files) {
    let newUploadSize = 0;

    // Check category_image size
    if (req.files.category_image) {
      if (Array.isArray(req.files.category_image)) {
        newUploadSize += req.files.category_image.reduce(
          (acc: number, file: any) => acc + (file.size || 0),
          0,
        );
      } else {
        newUploadSize += req.files.category_image.size || 0;
      }
    }

    // Check item_list size
    if (req.files.item_list) {
      if (Array.isArray(req.files.item_list)) {
        newUploadSize += req.files.item_list.reduce(
          (acc: number, file: any) => acc + (file.size || 0),
          0,
        );
      } else {
        newUploadSize += req.files.item_list.size || 0;
      }
    }

    if (totalSize + newUploadSize > getBytes) {
      return {
        status: false,
        message: res.__("ERROR_STORAGE_LIMIT_EXCEEDED"),
      };
    }

    return { status: true };
  }
};

/** email push notification functions */
const sendEmailNotification = async (templateData: any) => {
  const queue: any = RABBITMQ_QUEUE.EMAIL_NOTIFICATION;
  await rabbitmqPublisher.publishMessage(queue, templateData);
};

const sendNotificationToUser = async (templateData: any) => {
  const queue: any = RABBITMQ_QUEUE.PUSH_NOTIFICATION_SUCCESS;
  await rabbitmqPublisher.publishMessage(queue, templateData);
}

/** Get organization logo */
const getOrganizationLogo = async (organization_id: any) => {
  // Get item_location directly in a single query using subquery
  const result = await sequelize.query(
    `
    SELECT i.item_location
    FROM nv_settings s
    LEFT JOIN nv_items i ON i.id = s.value
    WHERE s.setting_status = '${setting_status.ACTIVE}'
    AND s.organization_id = '${organization_id}'
    AND s.key = 'brand_logo'
    LIMIT 1
  `,
    { type: QueryTypes.SELECT, raw: true },
  );

  // Check if we got a result with item_location
  if (result && result.length > 0 && result[0].item_location) {
    return `${global.config.API_BASE_URL}${result[0].item_location}`;
  }

  return "";
};




const generateContractBeforeOnboard = async (user_id: any, req: any, notify: any = false, isRemove: any, employment_contract?: any) => {
  try {
    const findUserData: any = await User.findOne({
      attributes: ['id', 'user_first_name', 'user_middle_name', 'user_last_name', 'appToken', 'webAppToken', 'address_line1', 'address_line2', 'pin_code', 'geo_city', 'geo_state', 'geo_country', 'user_signature', 'user_joining_date', 'country', 'branch_id', 'organization_id', 'user_email'],
      where: { id: user_id, organization_id: req.user.organization_id },
      include: {
        model: Branch,
        as: "branch",
        attributes: ["branch_name"],
      },
      raw: true,
      nest: true,
    });

    if (!isNaN(findUserData?.user_signature)) {
      const findItem = await Item.findOne({ where: { id: findUserData?.user_signature } })
      if (findItem) {
        findUserData.user_signature = findItem?.item_location
      }
    } else {
      if (findUserData.user_signature) {
        findUserData.user_signature = `/signatures/` + findUserData.user_signature
      }
    }

    const getHrmcDetail: any = await HrmcForm.findOne({
      where: { checklist_id: 3, user_id: user_id },
    });

    const findUserMeta: any = await UserMeta.findOne({
      where: { user_id: user_id }
    })

    let findLatestGeneral, findLatestDept, findLatestAdditiTemplate, findLatestAdditiTemplateIds
    let additionalDuties = ''
    let jobTitle: any
    if (findUserMeta?.general_template) {
      findLatestGeneral = await EmpContractTemplateVersion.findOne({ where: { emp_contract_template_id: findUserMeta?.general_template }, order: [['id', "desc"]] })
    }

    if (findUserMeta?.department_template) {
      findLatestDept = await EmpContractTemplateVersion.findOne({ where: { emp_contract_template_id: findUserMeta?.department_template }, order: [['id', "desc"]] })
      const findContractName = await EmpContractTemplate.findOne({ where: { id: findUserMeta?.department_template } })
      if (findContractName) {
        jobTitle = findContractName.name ? findContractName.name : ''
      }
    }

    if (findUserMeta?.additional_template) {
      const findAdditionalTemplate = findUserMeta?.additional_template.split(",").map(Number)
      const findLatestAdditionalArr: any = []
      for (let i = 0; i < findAdditionalTemplate.length; i++) {
        const empContract = findAdditionalTemplate[i];
        findLatestAdditiTemplate = await EmpContractTemplateVersion.findOne({
          where: { emp_contract_template_id: empContract },
          order: [['id', 'desc']]
        });

        // Add the ID to the array
        findLatestAdditionalArr.push(findLatestAdditiTemplate?.id);

        additionalDuties += (additionalDuties ? ' <br> ' : '') + findLatestAdditiTemplate?.content; // Append subsequent content
      }
      findLatestAdditiTemplateIds = findLatestAdditionalArr.toString();
    }


    const isTemplateUpdated = employment_contract ? (findUserMeta && (employment_contract.general_template != findLatestGeneral?.id) || (employment_contract.department_template != findLatestDept?.id) || (employment_contract.additional_template != findLatestAdditiTemplateIds) || (employment_contract.other != findUserMeta?.other) || (moment(findUserMeta?.expire_date).format("YYYY-MM-DD") != moment(employment_contract?.expire_date).format("YYYY-MM-DD")) || (moment(findUserData?.user_joining_date).format("YYYY-MM-DD") != moment(employment_contract?.start_date).format("YYYY-MM-DD")) || (employment_contract.wages_hours != findUserMeta?.wages_hours) || (employment_contract.fixed_types != findUserMeta?.fixed_types) || (employment_contract.leave_policy_id != findUserMeta?.leave_policy_id) || (employment_contract.probation_length != findUserMeta?.probation_length) || (employment_contract?.working_hours != findUserMeta?.working_hours) || (employment_contract?.duration_type != findUserMeta?.duration_type) || (employment_contract?.wage_type != findUserMeta?.wage_type) || (employment_contract?.contract_remark != findUserMeta?.contract_remark) || (employment_contract?.contract_name != findUserMeta?.contract_name) || (employment_contract?.leave_type_id != findUserMeta?.leave_type_id) || (employment_contract?.leave_days != findUserMeta?.leave_days) || (employment_contract?.leave_remark != findUserMeta?.leave_remark) || (employment_contract?.leave_duration_type != findUserMeta?.leave_duration_type) || (employment_contract?.place_of_work != findUserMeta?.place_of_work) || (employment_contract?.contract_name_id != findUserMeta?.contract_name_id) || (employment_contract?.has_holiday_entitlement != findUserMeta?.has_holiday_entitlement) || (employment_contract?.holiday_entitlement_remark != findUserMeta?.holiday_entitlement_remark)) : true

    const fileName = isTemplateUpdated ? `emp_contract_${findUserData?.user_first_name}_${findUserData?.user_last_name}_${findUserData.id}_${moment().format('YYYY-MM-DD_HH-mm-sss')}.pdf` : employment_contract?.contract_with_sign;

    const addressParts = [];

    if (findUserData?.address_line1) {
      addressParts.push(findUserData.address_line1);
    }
    if (findUserData?.address_line2) {
      addressParts.push(findUserData.address_line2);
    }
    if (findUserData?.pin_code) {
      addressParts.push(findUserData.pin_code);
    }
    if (findUserData?.geo_city) {
      addressParts.push((await getGeoDetails(findUserData.geo_city)).place_name);
    }
    if (findUserData?.geo_state) {
      addressParts.push((await getGeoDetails(findUserData.geo_state)).place_name);
    }
    if (findUserData?.geo_country) {
      addressParts.push((await getGeoDetails(findUserData.geo_country)).place_name);
    }


    const employeeName = [];

    if (findUserData?.user_first_name) {
      employeeName.push(findUserData.user_first_name);
    }
    if (findUserData?.user_middle_name) {
      employeeName.push(findUserData.user_middle_name);
    }
    if (findUserData?.user_last_name) {
      employeeName.push(findUserData.user_last_name);
    }
    const findGeneralSetting: any = await getGeneralSettingObj(findUserData.organization_id)
    let findBranchSetting: any
    if (findUserData.branch_id) {
      findBranchSetting = await getBranchSettingObj(findUserData.branch_id)
    }

    let namaste_logo: any = `${global.config.API_BASE_URL}/admin/namaste-logo.jpg`
    if (findGeneralSetting && findGeneralSetting.brand_logo_link) {
      namaste_logo = findGeneralSetting.brand_logo_link
    }

    const leaveTypeObj: any = {
      durationType: 'Days',
      days: findUserMeta && findUserMeta?.leave_days ? findUserMeta?.leave_days : 0
    };

    let getContractName: any
    if (findUserMeta?.contract_name_id) {
      getContractName = await ContractNameModel.findOne({ where: { id: findUserMeta?.contract_name_id } }) || ""
    }

    const expireDate: any = findUserMeta?.expire_date == null ? findUserMeta?.expire_duration && findUserMeta?.expire_date ? await calculateExpiryDate(findUserMeta?.expire_date, findUserMeta?.expire_duration) : findUserMeta?.expire_date : findUserMeta?.expire_date

    const compileData = {
      NAMASTE_LOGO: namaste_logo,
      branch_heading_employer_name: findBranchSetting && findBranchSetting.branch_employer_name ? findBranchSetting.branch_employer_name : findGeneralSetting && findGeneralSetting.employer_name != '' ? findGeneralSetting.employer_name : "Namaste Village",
      employer_name: findBranchSetting && findBranchSetting.branch_heading_employer_name ? findBranchSetting.branch_heading_employer_name : findGeneralSetting && findGeneralSetting.employer_name != '' ? findGeneralSetting.employer_name : "Micro ",
      branch_heading_name: findBranchSetting && findBranchSetting.branch_heading_name ? findBranchSetting.branch_heading_name : "Namaste Village Norwich Ltd.",
      branch_heading_work_place: findBranchSetting && findBranchSetting.branch_heading_work_place ? findBranchSetting.branch_heading_work_place : null,
      work_place: findUserMeta && findUserMeta?.place_of_work ? findUserMeta.place_of_work : findBranchSetting && findBranchSetting.branch_work_place ? findBranchSetting.branch_work_place : null,
      employee_name: employeeName.join(" "),
      employee_address: addressParts.join(", "),
      employee_sign: isRemove == true ? null : !isTemplateUpdated ? employment_contract && (employment_contract.is_confirm_sign == true || employment_contract.is_confirm_sign == 'true') ? findUserData.user_signature && findUserData.user_signature != '' ? `${global.config.API_BASE_URL}${findUserData.user_signature}` : null : null : null,
      employer_sign: findBranchSetting && findBranchSetting.branch_sign ? `${findBranchSetting.branch_sign}` : findGeneralSetting.employer_sign && findGeneralSetting.employer_sign != '' ? findGeneralSetting.employer_sign : null,
      insurance_number: getHrmcDetail?.insurance_number,
      job_title: jobTitle,
      joining_date: findUserData?.user_joining_date
        ? moment(findUserData?.user_joining_date).format("DD/MM/YYYY")
        : "",
      date: moment().format("DD/MM/YYYY"),
      generalContent: findLatestGeneral?.content,
      deptContent: findLatestDept?.content,
      addittionalContent: additionalDuties ? additionalDuties : '',
      otherContent: findUserMeta?.other,
      expire_date: expireDate ? moment(expireDate).format("DD/MM/YYYY") : "",
      // start_date: findUserMeta?.start_date ? moment(findUserMeta?.start_date).format("DD/MM/YYYY") : "",
      expire_duration: findUserMeta?.expire_duration,
      contract_type: JSON.parse(JSON.stringify(findUserMeta)),
      wages_per_hours: findUserMeta?.wages_hours,
      wages_type: findUserMeta?.wage_type,
      probation_period: findUserMeta?.probation_length,
      fixed_types: findUserMeta?.wage_type && findUserMeta.wage_type === wageType.FIXED && findUserMeta?.fixed_types ? findUserMeta?.fixed_types : findUserMeta?.fixed_types,
      tips_grade: findUserMeta?.tips_grade,
      annual_holiday: findUserMeta?.has_holiday_entitlement ? leaveTypeObj ? leaveTypeObj : null : null,
      contract_type_name: getContractName?.contract_name ? getContractName?.contract_name : "",
      nationality: findUserData?.country ? findUserData?.country : "",
      registration_number: findBranchSetting && findBranchSetting?.registration_number ? findBranchSetting?.registration_number : null
    };

    if (notify == "only") {
      const templateData = {
        name: findUserData.user_first_name,
        admin_name: req.user?.user_first_name,
        email: findUserData.user_email,
        mail_type: 'onboarding_regenerate',
        ORGANIZATION_LOGO: await getOrganizationLogo(req.user.organization_id),
        LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
        ADDRESS: EMAIL_ADDRESS.ADDRESS,
        PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
        EMAIL: EMAIL_ADDRESS.EMAIL,
        ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
        smtpConfig: 'INFO'
      }
      await sendEmailNotification(templateData)

      await createNotification([findUserData], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.ONBOARDING_FORM_REMIND.content, NOTIFICATIONCONSTANT.ONBOARDING_FORM_REMIND.heading, REDIRECTION_TYPE.ONBOARDING)
      return true
    } else {
      const s3Result = await generateS3EmployeeContract(
        fileName,
        compileData,
        FORMCONSTANT.EMPLOYE_CONTRACT.template,
        findUserData.organization_id,
        findUserData.id
      );

      if (s3Result.success) {
        await User.setHeaders(req).update(
          { employment_contract: fileName },
          { where: { id: findUserData.id } },
        );

        if (findUserMeta) {
          findUserMeta.isDraft = false;
          await findUserMeta.save();
        }

        await UserMeta.update(
          { expire_date: expireDate, updated_by: req.user.id },
          { where: { user_id: user_id } },
        );
        if (isTemplateUpdated) {
          await UserEmploymentContract.update(
            {
              contract_status: contract_status.INACTIVE,
              updated_by: req.user.id,
            },
            {
              where: {
                user_id: user_id,
                contract_status: contract_status.ACTIVE,
              },
            },
          );

          await UserEmploymentContract.create({
            contract_with_sign: s3Result.item_id?.toString(),
            is_confirm_sign: false,
            contract_status: contract_status.ACTIVE,
            updated_by: req.user.id,
            created_by: req.user.id,
            general_template: findLatestGeneral?.id,
            department_template: findLatestDept?.id,
            additional_template: findLatestAdditiTemplateIds,
            other: findUserMeta?.other,
            tips_grade: findUserMeta?.tips_grade,
            expire_date: expireDate,
            start_date: findUserData?.user_joining_date,
            user_id: findUserData.id,
            fixed_types: findUserMeta?.fixed_types,
            contract_type: findUserMeta?.contract_type,
            wages_hours: findUserMeta?.wages_hours,
            leave_policy_id: findUserMeta?.leave_policy_id,
            probation_length: findUserMeta?.probation_length,
            working_hours: findUserMeta?.working_hours,
            duration_type: findUserMeta?.duration_type,
            wage_type: findUserMeta?.wage_type,
            contract_remark: findUserMeta?.contract_remark,
            contract_name: findUserMeta?.contract_name,
            leave_type_id: findUserMeta?.leave_type_id,
            leave_days: findUserMeta?.leave_days,
            leave_remark: findUserMeta?.leave_remark,
            leave_duration_type: findUserMeta?.leave_duration_type,
            place_of_work: findUserMeta?.place_of_work,
            working_hour_per_day: findGeneralSetting?.working_hours_per_day
              ? findGeneralSetting?.working_hours_per_day
              : null,
            max_limit_per_week: findGeneralSetting?.max_limit_per_week
              ? findGeneralSetting?.max_limit_per_week
              : null,
            contract_name_id: findUserMeta?.contract_name_id,
            has_holiday_entitlement: findUserMeta?.has_holiday_entitlement,
            holiday_entitlement_remark:
              findUserMeta?.holiday_entitlement_remark,
          } as any);

          await UserCheckList.setHeaders(req).update(
            { status: check_list_status.PENDING },
            {
              where: {
                to_user_id: user_id,
                from_user_id: user_id,
                checklist_id: 4,
              },
            },
          );

          await User.update(
            { user_status: user_status.ONGOING },
            { where: { id: user_id, user_status: { [Op.notIn]: [user_status.PENDING, user_status.ACTIVE] } } },
          );
        } else {
          if (employment_contract) {
            await UserEmploymentContract.update(
              {
                expire_date: expireDate,
                contract_with_sign: s3Result.item_id?.toString(),
                is_confirm_sign: false,
                contract_status: contract_status.ACTIVE,
                updated_by: req.user.id,
              },
              { where: { id: employment_contract?.id } },
            );
          }

          await UserCheckList.setHeaders(req).update(
            { status: check_list_status.PENDING },
            {
              where: {
                to_user_id: user_id,
                from_user_id: user_id,
                checklist_id: 4,
              },
            },
          );

          await User.update(
            { user_status: user_status.ONGOING },
            { where: { id: user_id, user_status: { [Op.notIn]: [user_status.PENDING, user_status.ACTIVE] } } },
          );
        }

        if (notify == "true") {
          const templateData = {
            name: findUserData.user_first_name,
            admin_name: req.user?.user_first_name,
            email: findUserData.user_email,
            mail_type: "onboarding_regenerate",
            ORGANIZATION_LOGO: await getOrganizationLogo(
              req.user.organization_id,
            ),
            LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
            ADDRESS: EMAIL_ADDRESS.ADDRESS,
            PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
            EMAIL: EMAIL_ADDRESS.EMAIL,
            ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
            smtpConfig: 'INFO'
          }
          await sendEmailNotification(templateData)
          await createNotification([findUserData], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.ONBOARDING_FORM_REQUEST.content(req.user.user_first_name, "regenerated"), NOTIFICATIONCONSTANT.ONBOARDING_FORM_REQUEST.heading, REDIRECTION_TYPE.ONBOARDING)

        }
      } else {
        console.error("Error generating S3 employee contract:", s3Result.error);
      }
    }
  } catch (error) {
    console.error("Error in generateContractBeforeOnboard:", error);
    return false
  }
}

const getOrgName = async (orgId: any) => {
  try {
    const selectQuery = `SELECT NAME FROM ORG WHERE ID = '${orgId}'`
    const orgName = await sequelize.query(selectQuery, { type: sequelize.QueryTypes.SELECT })

    if (orgName[0].NAME && orgName[0].NAME.includes('_')) {
      /** Replace underscore with space */
      orgName[0].NAME = orgName[0].NAME.replace(/_/g, ' ')
    }
    return orgName[0].NAME
  } catch (error) {
    console.error("Error in getOrgName:", error);
    return false
  }
}

const getGeneralSetting = async () => {
  try {
    const getSettingList = await VersionSetting.findAll({
      where: {
        setting_status: setting_status.ACTIVE
      },
      raw: true,
    });

    if (getSettingList.length > 0) {
      const settingObj: any = {};
      for (const key of getSettingList) {
        settingObj[key.key] = key.value;
      }
      return settingObj;
    } else {
      return {};
    }
  } catch (error) {
    console.log("error", error);
  }
};

/** send change request for profile update */
const sendChangeRequestForProfileUpdate = async (
  req: any,
  subject: any,
  cr_type: any,
  model: any,
  allFields: any = null,
  existingId: any = null,
  existingId2: any = null
) => {
  try {
    // Get change request settings for the organization
    const setting = await ChangeRequestSettings.findOne({
      where: { organization_id: req.user.organization_id },
      raw: true,
    });
    const parseFields: string[] = setting?.key ? JSON.parse(setting.key) : [];
    // Get model fields
    const modelFields = Object.keys(allFields);

    let userOldData: any = {};
    const hasUserProfileFields = parseFields.some(field => modelFields.includes(field));
    if (hasUserProfileFields) {
      if (model == User) {
        const USER_FIELDS = ['username', 'user_first_name', 'user_last_name', 'user_email', 'user_phone_number', 'user_avatar', 'user_signature', 'address_line1', 'address_line2', 'country', 'user_gender', 'user_gender_other', 'marital_status', 'marital_status_other', 'date_of_birth', 'emergency_contact', 'pin_code', 'geo_country', 'geo_state', 'geo_city'];
        const hasUserFields = parseFields.some(field => USER_FIELDS.includes(field));
        if (hasUserFields) {
          userOldData = await User.findOne({
            where: { id: req.user.id, organization_id: req.user.organization_id },
            attributes: USER_FIELDS
          });

          if (userOldData) {
            userOldData = userOldData.get({ plain: true });
          }
        }
      }

      if (model == RightToWorkCheckList) {
        const RTWC_FIELDS = ['has_right_to_work_in_uk', 'is_uk_citizen', 'is_confirm_upload', 'photoID', 'ni_letter', 'statements_dl_utility', 'cv', 'p45', 'share_code', 'passport_front', 'passport_back', 'brp_front', 'brp_back', 'student_letter'];
        const hasRTWCFields = parseFields.some(field => RTWC_FIELDS.includes(field));
        if (hasRTWCFields) {
          userOldData = await RightToWorkCheckList.findOne({
            attributes: RTWC_FIELDS,
            where: { id: existingId }
          })
          if (userOldData) {
            userOldData = userOldData.get({ plain: true });
          }
        }
      }

      if (model == HrmcForm) {
        const hrmcFields = ['insurance_number', 'postgraduate_loan', 'statement_apply', 'is_current_information', 'another_job', 'private_pension', 'payment_from', 'load_guidance', 'statementA', 'statementB', 'statementC'];
        const starterFields = ['medical_disability', 'medical_disability_detail', 'kin1_name', 'kin1_relation', 'kin1_address', 'kin1_mobile_number', 'kin2_name', 'kin2_relation', 'kin2_address', 'kin2_mobile_number', 'professional1_name_contact', 'professional1_role_description', 'professional1_start_date', 'professional1_end_date', 'professional2_name_contact', 'professional2_role_description', 'professional2_start_date', 'professional2_end_date', 'passport_no', 'issued_date', 'permit_type', 'permit_type_other', 'validity', 'bank_account_name', 'bank_account_number', 'bank_sort_code', 'bank_society_name', 'bank_address', 'has_student_or_pg_loan', 'has_p45_form', 'hmrc_p45_form'];

        const parseFieldsHrmc = parseFields.filter(field => hrmcFields.includes(field));
        const parseFieldsStarter = parseFields.filter(field => starterFields.includes(field));

        /** if yes then send chage request, else update directly */
        if (parseFieldsHrmc.length > 0 || parseFieldsStarter.length > 0) {
          // Get HRMC data if parseFields contains HRMC fields
          if (parseFieldsHrmc.length > 0) {
            const hrmcData = await HrmcForm.findOne({
              attributes: parseFieldsHrmc,
              where: { id: existingId2 }
            });
            if (hrmcData) {
              Object.assign(userOldData, hrmcData.get({ plain: true }));
            }
          }

          // Get StarterForm data if parseFields contains StarterForm fields
          if (parseFieldsStarter.length > 0) {
            const starterData = await StarterForm.findOne({
              attributes: parseFieldsStarter,
              where: { id: existingId },
            });
            if (starterData) {
              Object.assign(userOldData, starterData.get({ plain: true }));
            }
          }
        }
      }
    }

    // Prepare objects
    const fieldsForChangeRequest: any = {};
    const fieldsForDirectUpdate: any = {};

    Object.keys(allFields).forEach(fieldName => {
      const fieldValue = allFields[fieldName];

      // Skip undefined/null/empty string
      if (fieldValue === undefined || fieldValue === null) return;

      const dbValue = userOldData[fieldName];

      // Special handling for date comparison
      const isDateField = fieldName === 'date_of_birth'; // Add more if needed
      const oldVal = isDateField && dbValue
        ? new Date(dbValue).toISOString().split('T')[0]
        : dbValue?.toString().trim() ?? "";

      const newVal = isDateField
        ? new Date(fieldValue).toISOString().split('T')[0]
        : fieldValue?.toString().trim() ?? "";

      // If field exists in model
      if (modelFields.includes(fieldName)) {
        if (oldVal !== newVal) {
          if (parseFields.includes(fieldName)) {
            fieldsForChangeRequest[fieldName] = fieldValue;
          } else {
            fieldsForDirectUpdate[fieldName] = fieldValue;
          }
        }
      }
    });
    // Check for existing pending change requests with the same fields
    if (Object.keys(fieldsForChangeRequest).length > 0) {
      const existingPendingRequests = await ChangeRequest.findAll({
        where: {
          user_id: req.user.id,
          change_request_status: change_request_status.PENDING,
          change_request_type: cr_type
        },
        raw: true
      });

      let newFields: string[] = [];
      let duplicateFields: string[] = [];
      const currentFields = Object.keys(fieldsForChangeRequest);

      /** if there is any pending change request for the user */
      if (existingPendingRequests.length > 0) {
        /** get all the fields from the existing pending change requests */
        const allExistingFields = existingPendingRequests.reduce((acc: string[], request) => {
          const data = JSON.parse(request.new_data || '{}');
          return acc.concat(Object.keys(data));
        }, []);

        /** remove duplicates from the existing fields */
        const uniqueExistingFields = [...new Set(allExistingFields)];
        /** get the duplicate fields */
        duplicateFields = currentFields.filter(field => uniqueExistingFields.includes(field));
        /** get the new fields */
        newFields = currentFields.filter(field => !uniqueExistingFields.includes(field));
        if (newFields.length === 0) {
          return {
            status: false,
            message: `CHANGE_REQUEST_ALREADY_PENDING`,
            duplicateFields: duplicateFields
          };
        }
      } else {
        newFields = currentFields;
      }

      const newChangeRequestData: Record<string, any> = {};
      const oldChangeRequestData: Record<string, any> = {};

      for (const field of newFields) {
        const newVal = fieldsForChangeRequest[field];
        const oldVal = userOldData[field];

        newChangeRequestData[field] = newVal;
        oldChangeRequestData[field] = oldVal;

        // Handle user_avatar field for image names
        if (field === 'user_avatar') {
          // Fetch both old and new image locations from the Item table
          const [oldImage, newImage] = await Promise.all([
            Item.findOne({ where: { id: oldVal }, attributes: ['item_location'], raw: true }),
            Item.findOne({ where: { id: newVal }, attributes: ['item_location'], raw: true })
          ]);

          oldChangeRequestData['user_avatar_name'] = oldImage?.item_location || null;
          newChangeRequestData['user_avatar_name'] = newImage?.item_location || null;
        }
        if (field === 'user_signature') {
          // Fetch both old and new image locations from the Item table
          const [oldImage, newImage] = await Promise.all([
            Item.findOne({ where: { id: oldVal }, attributes: ['item_location'], raw: true }),
            Item.findOne({ where: { id: newVal }, attributes: ['item_location'], raw: true })
          ]);
          oldChangeRequestData['user_signature_name'] = oldImage?.item_location || null;
          newChangeRequestData['user_signature_name'] = newImage?.item_location || null;
        }
        if (field === 'photoID') {
          // Fetch both old and new image locations from the Item table
          const [oldImage, newImage] = await Promise.all([
            Item.findOne({ where: { id: oldVal }, attributes: ['item_location'], raw: true }),
            Item.findOne({ where: { id: newVal }, attributes: ['item_location'], raw: true })
          ]);

          oldChangeRequestData['photoID_name'] = oldImage?.item_location || null;
          newChangeRequestData['photoID_name'] = newImage?.item_location || null;
        }
        if (field === 'ni_letter') {
          // Fetch both old and new image locations from the Item table
          const [oldImage, newImage] = await Promise.all([
            Item.findOne({ where: { id: oldVal }, attributes: ['item_location'], raw: true }),
            Item.findOne({ where: { id: newVal }, attributes: ['item_location'], raw: true })
          ]);

          oldChangeRequestData['ni_letter_name'] = oldImage?.item_location || null;
          newChangeRequestData['ni_letter_name'] = newImage?.item_location || null;
        }
        if (field === 'brp_front') {
          // Fetch both old and new image locations from the Item table
          const [oldImage, newImage] = await Promise.all([
            Item.findOne({ where: { id: oldVal }, attributes: ['item_location'], raw: true }),
            Item.findOne({ where: { id: newVal }, attributes: ['item_location'], raw: true })
          ]);

          oldChangeRequestData['brp_front_name'] = oldImage?.item_location || null;
          newChangeRequestData['brp_front_name'] = newImage?.item_location || null;
        }
        if (field === 'brp_back') {
          // Fetch both old and new image locations from the Item table
          const [oldImage, newImage] = await Promise.all([
            Item.findOne({ where: { id: oldVal }, attributes: ['item_location'], raw: true }),
            Item.findOne({ where: { id: newVal }, attributes: ['item_location'], raw: true })
          ]);

          oldChangeRequestData['brp_back_name'] = oldImage?.item_location || null;
          newChangeRequestData['brp_back_name'] = newImage?.item_location || null;
        }
        if (field === 'passport_front') {
          const [oldImage, newImage] = await Promise.all([
            Item.findOne({ where: { id: oldVal }, attributes: ['item_location'], raw: true }),
            Item.findOne({ where: { id: newVal }, attributes: ['item_location'], raw: true })
          ]);

          oldChangeRequestData['passport_front_name'] = oldImage?.item_location || null;
          newChangeRequestData['passport_front_name'] = newImage?.item_location || null;
        }
        if (field === 'passport_back') {
          const [oldImage, newImage] = await Promise.all([
            Item.findOne({ where: { id: oldVal }, attributes: ['item_location'], raw: true }),
            Item.findOne({ where: { id: newVal }, attributes: ['item_location'], raw: true })
          ]);

          oldChangeRequestData['passport_back_name'] = oldImage?.item_location || null;
          newChangeRequestData['passport_back_name'] = newImage?.item_location || null;
        }
        if (field === 'cv') {
          const [oldImage, newImage] = await Promise.all([
            Item.findOne({ where: { id: oldVal }, attributes: ['item_location'], raw: true }),
            Item.findOne({ where: { id: newVal }, attributes: ['item_location'], raw: true })
          ]);

          oldChangeRequestData['cv_name'] = oldImage?.item_location || null;
          newChangeRequestData['cv_name'] = newImage?.item_location || null;
        }
        if (field === 'share_code') {
          const [oldImage, newImage] = await Promise.all([
            Item.findOne({ where: { id: oldVal }, attributes: ['item_location'], raw: true }),
            Item.findOne({ where: { id: newVal }, attributes: ['item_location'], raw: true })
          ]);

          oldChangeRequestData['share_code_name'] = oldImage?.item_location || null;
          newChangeRequestData['share_code_name'] = newImage?.item_location || null;
        }
        if (field === 'hmrc_p45_form') {
          const [oldImage, newImage] = await Promise.all([
            Item.findOne({ where: { id: oldVal }, attributes: ['item_location'], raw: true }),
            Item.findOne({ where: { id: newVal }, attributes: ['item_location'], raw: true })
          ]);

          oldChangeRequestData['hmrc_p45_form_name'] = oldImage?.item_location || null;
          newChangeRequestData['hmrc_p45_form_name'] = newImage?.item_location || null;
        }

        if (field === 'statements_dl_utility') {
          const [oldImage, newImage] = await Promise.all([
            Item.findOne({ where: { id: oldVal }, attributes: ['item_location'], raw: true }),
            Item.findOne({ where: { id: newVal }, attributes: ['item_location'], raw: true })
          ]);

          oldChangeRequestData['statements_dl_utility_name'] = oldImage?.item_location || null;
          newChangeRequestData['statements_dl_utility_name'] = newImage?.item_location || null;
        }

        if (field === 'student_letter') {
          const [oldImage, newImage] = await Promise.all([
            Item.findOne({ where: { id: oldVal }, attributes: ['item_location'], raw: true }),
            Item.findOne({ where: { id: newVal }, attributes: ['item_location'], raw: true })
          ]);

          oldChangeRequestData['student_letter_name'] = oldImage?.item_location || null;
          newChangeRequestData['student_letter_name'] = newImage?.item_location || null;
        }
      }
      // Proceed with CR creation
      const sendChangeRequest = await ChangeRequest.create({
        change_request_subject: subject,
        old_data: JSON.stringify(oldChangeRequestData),
        new_data: JSON.stringify(newChangeRequestData), // ✅ object with only new fields
        change_request_status: change_request_status.PENDING,
        user_id: req.user.id,
        created_by: req.user.id,
        updated_by: req.user.id,
        change_request_files: null,
        change_request_type: cr_type
      } as any);

      if (sendChangeRequest) {
        await ChangeRequestHistory.create({
          change_request_id: sendChangeRequest.id,
          change_request_subject: subject,
          old_data: JSON.stringify(oldChangeRequestData),
          new_data: JSON.stringify(newChangeRequestData),
          change_request_status: sendChangeRequest.change_request_status,
          change_request_history_status: change_request_history_status.ACTIVE,
          user_id: req.user.id,
          created_by: req.user.id,
          updated_by: req.user.id,
          change_request_files: null
        } as any);

        const getFullName = await getUserFullName(req.user.id);

        const findHrUsers = (await User.findAll({
          attributes: ['id', 'webAppToken', 'appToken'],
          where: {
            user_status: {
              [Op.not]: [
                user_status.DELETED,
                user_status.PENDING,
                user_status.CANCELLED,
              ],
            },
            id: {
              [Op.in]: [
                sequelize.literal(
                  `(SELECT nv_user_roles.user_id FROM nv_user_roles WHERE nv_user_roles.role_id IN (SELECT id FROM nv_roles WHERE role_name IN ('${ROLE_CONSTANT.HR}')))`
                ),
              ],
            },
            organization_id: req.user.organization_id
          },
          raw: true,
          group: ['id']
        })) || [];

        await createNotification(
          findHrUsers,
          req,
          NOTIFICATION_TYPE.INDIVIDUAL,
          NOTIFICATIONCONSTANT.CHANGE_REQUEST.content(getFullName),
          NOTIFICATIONCONSTANT.CHANGE_REQUEST.heading,
          REDIRECTION_TYPE.CHANGE_REQUEST,
          sendChangeRequest.id,
          { change_request_id: sendChangeRequest.id }
        );

        return {
          status: true,
          // message: duplicateFields.length > 0 ? `These fields are already pending: ${duplicateFields.join(', ') || 'None'}, and a new change request has been created for: ${newFields.join(', ')}` : `A new change request has been created for: ${newFields.join(', ')}`,
          message: duplicateFields.length > 0 && newFields.length === 0 ? "Data updated successfully" : "A new change request has been created",
          fieldsForDirectUpdate, fieldsForChangeRequest, newFields, duplicateFields
        };
      }
    }
    // Optionally return fieldsForDirectUpdate if needed for direct DB update
    return { status: true, fieldsForDirectUpdate, fieldsForChangeRequest };
  } catch (error) {
    console.log("error", error);
    throw error; // Re-throw the error so it can be handled by the calling function
  }
}

/**
 * Get time period configuration for dashboard filtering
 * Returns date range, grouping type, labels, and where query condition
 */
const getTimePeriodConfig = (timePeriod: string) => {
  let startDate: any;
  let endDate: any;
  let groupBy: 'day' | 'week' | 'month';
  let labels: string[] = [];
  const labelKeyMap: Record<string, string> = {};

  switch (timePeriod) {
    case 'this_week':
    case 'last_week': {
      startDate = timePeriod === 'this_week'
        ? moment().startOf('week')
        : moment().subtract(1, 'week').startOf('week');
      endDate = timePeriod === 'this_week'
        ? moment().endOf('week')
        : moment().subtract(1, 'week').endOf('week');
      groupBy = 'day';
      const current = startDate.clone();
      while (current.isSameOrBefore(endDate)) {
        const label = current.format('dddd'); // Sunday, Monday, etc.
        labels.push(label);
        labelKeyMap[label] = label;
        current.add(1, 'day');
      }
      break;
    }

    case 'this_month':
    case 'last_month': {
      startDate = moment().startOf("month").format("YYYY-MM-DD");
      endDate = moment().endOf("month").format("YYYY-MM-DD");

      const start_date = timePeriod === 'this_month'
        ? moment().startOf('month')
        : moment().subtract(1, 'month').startOf('month');

      const end_date = timePeriod === 'this_month'
        ? moment().endOf('month')
        : moment().subtract(1, 'month').endOf('month');

      groupBy = 'week';
      const current = start_date.clone();
      let week = 1;

      while (current.isSameOrBefore(end_date)) {
        const label = `Week ${week}`;
        labels.push(label);
        labelKeyMap[label] = label;

        current.add(7, 'days');
        week++;
      }
      break;
    }

    case 'this_year': {
      startDate = moment().startOf('year');
      endDate = moment().endOf('year');
      groupBy = 'month';
      labels = moment.months(); // ['January', 'February', ...]
      labels.forEach(month => labelKeyMap[month] = month);
      break;
    }

    default: {
      throw new Error(`Invalid timePeriod: ${timePeriod}`);
    }
  }

  return {
    startDate,
    endDate,
    groupBy,
    labels,
    labelKeyMap
  };
};



/**
 * Calculate which week of the month a given date falls into
 * Returns week number (1-based) within the month
 */
const getWeekOfMonth = (date: moment.Moment): number => {
  const monthStart = date.clone().startOf('month');
  const daysDiff = date.diff(monthStart, 'days');
  return Math.floor(daysDiff / 7) + 1;
};

async function generateDashboardWhereClause(datesArray: any) {
  const conditions = datesArray.map(
    (date: any) =>
      `((x.start_date >= '${date.start}' AND x.start_date <= '${date.end}') OR (x.end_date >= '${date.start}' AND x.end_date <= '${date.end}'))`,
  );
  return conditions.join(' OR\n');
}

export default getCurrentLeaveBalance;
export {
  readHTMLFile,
  permittedForAdmin,
  permittedForAdminMO,
  permittedForAdminEnhanced,
  checkBranchUser,
  roleName,
  roleNameMO,
  roleNameEnhanced,
  getRoleName,
  getRoleNameMO,
  getRoleNameEnhanced,
  findParentUserRole,
  userCreatePermission,
  userCreatePermissionMO,
  userCreatePermissionEnhanced,
  permissionForAdmin,
  permissionForAdminMO,
  permissionForAdminEnhanced,
  validateModulePermission,
  findUserExist,
  deleteFiles,
  addActivity,
  generateEmployeeContract,
  getAdminStaffs,
  getAdminStaffsMO,
  getAdminStaffsEnhanced,
  moveFile,
  checkUserProfileComplete,
  getGeneralSettingObj,
  getUserDeviceId,
  createNotification,
  getBranchSettingObj,
  getUserFullName,
  getReminderUser,
  generateReport,
  addSpacesBeforeCapitals,
  formatUserAgentData,
  sendInvitation,
  addFooterToPDF,
  getHash,
  ReadingFile,
  propagateBranchesAndDepartmentsToAllParents,
  updateBranchesAndDepartmentsInAllParents,
  reminderContractExpireSoon,
  formatContentLength,
  findAllChildren,
  getItemTrackDetails,
  fetchCategoriesRecursively,
  calculateTotalSize,
  restoreTrackCategoryOfUser,
  findAllChildrenWithType,
  reminderProbationUserSoon,
  regenerateEmploymentContractFuncation,
  getWeekCount,
  getFirstAndLastDates,
  isDateInRange,
  getReminderUserOnWeekEnd,
  getReminderUserOnLastWeek,
  getReminderUserForExpenses,
  getGeoDetails,
  generateEmploymentNumber,
  generateReportNew,
  getReportFilter,
  getReportCategoryFilter,
  isRecordReferencedInAnyTable,
  getColumnValue,
  getColumnValueForGeneral,
  getDashboardModel,
  generateDateComponents,
  getReportDashboard,
  generateDashboard,
  generateForecastBudgetReport,
  getLeaveRequestsByDateRanges,
  getSuperAdminUserId,
  getSuperAdminUserIdMO,
  getSuperAdminUserIdEnhanced,
  getStaffCount,
  leavePolicyRules,
  getCurrentLeaveBalance,
  calculateProrateLeaveBalance,
  getUserLeaveBalanceFunc,
  calculateExpiryDate,
  checkUserStorageLimit,
  calculateTotalDaysAndHours,
  calculateUserLeaveHours,
  convertFutureLeaves,
  calculateLeaveTotal,
  handleLeaveAccrual,
  convertToBytes,
  checkStorageSize,
  sendEmailNotification,
  getOrganizationLogo,
  sendNotificationToUser,
  getOrgName,
  getGeneralSetting,
  sendChangeRequestForProfileUpdate,
  getPlatformFromRequest,
  getTimePeriodConfig,
  getWeekOfMonth,
  generateDashboardWhereClause
};
