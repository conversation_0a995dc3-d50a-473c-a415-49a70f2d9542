"use strict";
import { Model, DataTypes, Sequelize } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface roleAttributes {
  health_safety_form_id: number;
  health_safety_list_id: number;
  created_by: number;
  createdAt: string;
}

export class HealthSafetyRelation
  extends Model<roleAttributes, never>
  implements roleAttributes {
  health_safety_form_id!: number;
  health_safety_list_id!: number;
  created_by!: number;
  createdAt!: string;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

HealthSafetyRelation.init(
  {
    health_safety_form_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    health_safety_list_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_health_safety_relation",
    modelName: "HealthSafetyRelation",
    timestamps: false,
  },
);

HealthSafetyRelation.removeAttribute("id");

// Define hooks for HealthSafetyRelation model
HealthSafetyRelation.addHook(
  "afterUpdate",
  async (healthSafetyRelation: any) => {
    await addActivity(
      "HealthSafetyRelation",
      "updated",
      healthSafetyRelation,
    );
  },
);

HealthSafetyRelation.addHook(
  "afterCreate",
  async (healthSafetyRelation: HealthSafetyRelation) => {
    await addActivity(
      "HealthSafetyRelation",
      "created",
      healthSafetyRelation,
    );
  },
);

HealthSafetyRelation.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

