"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { Role } from "./Role";
import { addActivity } from "../helper/queue.service";

interface permissionAttributes {
  id: number;
  role_id: number;
  module: string;
  permission: number;
  partial: boolean;
  created_by: number;
  updated_by: number;
}

export enum permission_module {
  DASHBOARD = "dashboard",
  USER = "user",
  BRANCH = "branch",
  DEPARTMENT = "department",
  NOTIFICATION = "notification",
  SETTING = "setting",
  STAFF = "staff",
  LEAVE_CENTER = "leave_center",
  RESIGNATION = "resignation",
  CATEGORY = "category",
  MEDIA = "media",
  PLAYLIST = "playlist",
  ACTIVITY_LOG = "activity_log",
  BRANCH_CARD = "branch_card",
  BRANCH_BANK = "branch_bank",
  DSR = "dsr",
  DSR_REPORT = "dsr_report",
  CHANGE_REQUEST = 'change_request',
  user_invitation = 'user_invitation',
  USER_VERIFICATION = "user_verification",
  EMPLOYEE_CONTRACT = "employee_contract",
  FORECAST = "forecast",
  FORECAST_BUGDET = "forecast_budget",
  LEAVE_SETTING = "leave_setting",
  LEAVE_REPORT = "leave_report",
  SIDE_LETTER = "side_letter",
  SETUP = "setup",
  ROTA = "rota",
  RECIPE = "recipe"
}

export class Permission
  extends Model<permissionAttributes, never>
  implements permissionAttributes {
  id!: number;
  role_id!: number;
  module!: string;
  permission!: number;
  partial!: boolean;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

Permission.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    role_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    module: {
      type: DataTypes.ENUM,
      values: Object.values(permission_module),
      allowNull: false,
    },
    permission: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    partial: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_permissions",
    modelName: "Permission",
  },
);

Role.hasOne(Permission, { foreignKey: "role_id" });
Permission.belongsTo(Role, { foreignKey: "role_id", as: "role" });

Permission.addHook("afterUpdate", async (permission: any) => {
  await addActivity("Permission", "updated", permission);
});

Permission.addHook("afterCreate", async (permission: Permission) => {
  await addActivity("Permission", "created", permission);
});

Permission.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

