"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { fixed_types } from "./UserMeta";

interface ContractTypeAttributes {
    id: number;
    name: string;
    working_hours: number;
    duration_type: 'month' | 'week';
    wage_per_hour: number;
    wage_type: 'hours' | 'fixed';
    fixed_types: string;
    status: string;
    remark: string;
    created_by: number;
    updated_by: number;
}

export class ContractTypeModel
    extends Model<ContractTypeAttributes, never>
    implements ContractTypeAttributes {
    public id!: number;
    public name!: string;
    public working_hours!: number;
    public duration_type!: 'month' | 'week';
    public wage_per_hour!: number;
    public wage_type!: 'hours' | 'fixed';
    public fixed_types!: string;
    public status!: string;
    public remark!: string;
    public created_by!: number;
    public updated_by!: number;

    public readonly createdAt!: Date;
    public readonly updatedAt!: Date;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

export enum status {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DRAFT = "draft",
    DELETED = "deleted"
}
export enum durationType {
    WEEKLY = "week",
    MONTHLY = "month"
}
export enum wageType {
    HOURS = "hours",
    FIXED = "fixed"
}

ContractTypeModel.init(
    {
        id: {
            type: DataTypes.INTEGER.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
        },
        name: {
            type: DataTypes.STRING(100),
            allowNull: false,
        },
        working_hours: {
            type: DataTypes.FLOAT,
            allowNull: false,
        },
        duration_type: {
            type: DataTypes.ENUM,
            values: Object.values(durationType),
            defaultValue: durationType.WEEKLY
        },
        wage_per_hour: {
            type: DataTypes.FLOAT,
            allowNull: false,
        },
        wage_type: {
            type: DataTypes.ENUM,
            values: Object.values(wageType),
            defaultValue: wageType.HOURS
        },
        fixed_types: {
            type: DataTypes.ENUM,
            values: Object.values(fixed_types),
            allowNull: true
        },
        remark: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        status: {
            type: DataTypes.ENUM,
            values: Object.values(status),
            defaultValue: status.ACTIVE,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_contract_type",
        modelName: "ContractTypeModel",
    },
);


// Define hooks for contract type model
ContractTypeModel.addHook("afterUpdate", async (ContractTypeModel: any) => {
    await addActivity("ContractType", "updated", ContractTypeModel);
});

ContractTypeModel.addHook("afterCreate", async (ContractTypeModel: ContractTypeModel) => {
    await addActivity("ContractType", "created", ContractTypeModel);
});

ContractTypeModel.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});