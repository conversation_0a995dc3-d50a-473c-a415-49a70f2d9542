import rabbitMQ from "./rabbitmq";
import { RABBITMQ_QUEUE } from "../helper/constant";

export const setupConsumers = async () => {
    try {
        console.log("Setting up RabbitMQ consumers...");

        // consume messages from the following queues
        await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.STAFF_CREATION_SUCCESS);
        await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.ORG_MASTER_USER);
        await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.ORG_MASTER_USER_VERIFICATION_SUCCESS);
        await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.STAFF_CREATION);
        await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.SESSION_STORE);
        await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.USER_ACTIVITY_LOG);

        /** Banner Notification consumers */
        await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.BANNER_NOTIFICATION);
        console.log("RabbitMQ consumers are set up successfully.");
    } catch (error) {
        console.error("Error setting up RabbitMQ consumers:", error);
    }
};