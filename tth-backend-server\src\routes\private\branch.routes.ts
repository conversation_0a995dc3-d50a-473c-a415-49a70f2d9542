import { Router } from "express";
import branchController from "../../controller/branch.controller";
import branchValidator from "../../validators/branch.validator";
const router: Router = Router();

router.post("/add", branchValidator.createBranch(), branchController.addBranch);
router.put(
  "/update/:branch_id?",
  branchValidator.updateBranch(),
  branchController.saveBranch,
);
router.delete("/delete/:branch_id", branchController.deleteBranch);
router.get("/list", branchController.getAllBranch);
router.get("/get-one/:branch_id", branchController.getBranchById);

export default router;
