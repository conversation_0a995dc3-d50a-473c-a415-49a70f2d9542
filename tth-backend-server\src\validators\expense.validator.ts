import { Segments, Joi, celebrate } from "celebrate";
export default {
  addExpense: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        expense_month: Joi.number().required(),
        expense_year: Joi.number().required(),
        branch_id: Joi.number().required(),
        current_datetime: Joi.date().allow(null, ''),
        data: Joi.array(),
      }),
    }),
  updateExpense: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        data: Joi.array(),
        current_datetime: Joi.date().allow(null, ''),
        expense_month: Joi.number().allow(null, ''),
        expense_year: Joi.number().allow(null, ''),
      }),
    }),

}
