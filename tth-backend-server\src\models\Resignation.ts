"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface resignationAttributes {
  id: number;
  resignation_subject: string;
  resignation_reason: string;
  user_id: string;
  last_serving_date: string;
  resignation_status: string;
  created_by: number;
  updated_by: number;
}

export enum resignation_status {
  PENDING = 'pending',
  IN_DISCUSSION = 'in-discussion',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  CANCELLED = "cancelled",
}

export class Resignation
  extends Model<resignationAttributes, never>
  implements resignationAttributes {
  id!: number;
  resignation_subject!: string;
  resignation_reason!: string;
  user_id!: string;
  last_serving_date!: string;
  resignation_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

Resignation.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    resignation_subject: {
      type: DataTypes.TEXT("long"),
      allowNull: true,
    },
    resignation_reason: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    last_serving_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    resignation_status: {
      type: DataTypes.ENUM,
      values: Object.values(resignation_status),
      defaultValue: resignation_status.PENDING,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_resignation",
    modelName: "Resignation",
  },
);

// Resignation.belongsTo(User, {
//   foreignKey: "user_id",
//   as: "resign_user",
// });
// User.hasMany(Resignation, {
//   foreignKey: "user_id",
//   as: "user_detail",
// });

// Resignation.belongsTo(User, {
//   foreignKey: "created_by",
//   as: "resignation_created_user",
// });
// User.hasMany(Resignation, {
//   foreignKey: "created_by",
//   as: "resignation_created_user_detail",
// });

// Resignation.belongsTo(User, {
//   foreignKey: "updated_by",
//   as: "resignation_updated_user",
// });
// User.hasMany(Resignation, {
//   foreignKey: "updated_by",
//   as: "resignation_updated_user_detail",
// });

Resignation.addHook("afterUpdate", async (resignation: any) => {
  await addActivity("Resignation", "updated", resignation);
});

Resignation.addHook("afterCreate", async (resignation: Resignation) => {
  await addActivity("Resignation", "created", resignation);
});

Resignation.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

