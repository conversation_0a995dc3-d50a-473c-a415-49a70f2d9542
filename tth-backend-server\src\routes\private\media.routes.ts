import { Router } from "express";
import mediaController from "../../controller/media.controller";
import mediaValidator from "../../validators/media.validator";
const router: Router = Router();
import { multerS3 } from "../../helper/upload.service";
import { FILE_UPLOAD_CONSTANT } from "../../helper/constant";

// Initialize multerS3 uploader with proper error handling
const multerS3Upload = multerS3(
  process.env.NODE_ENV || "development",
  FILE_UPLOAD_CONSTANT.MEDIA_FILES.folder,
);

router.post(
  "/add-category",
  mediaValidator.createCategory(),
  mediaController.createCategory,
);

router.delete("/delete-category/:category_id", mediaController.deleteCategory);

router.get("/get-category-list", mediaController.getCategoryList);

router.put(
  "/update-category/:category_id",
  mediaValidator.updateCategory(),
  mediaController.updateCategory,
);

router.post(
  "/create-playlist",
  multerS3Upload.upload("playlist_image"),
  mediaController.createPlaylist,
);

router.put(
  "/update-playlist/:playlist_id",
  multerS3Upload.upload("playlist_image"),
  mediaController.updatePlaylist,
);

router.delete("/delete-playlist/:playlist_id", mediaController.deletePlaylist);

router.delete(
  "/delete-playlist-image/:playlist_id",
  mediaController.deletePlaylistImage,
);

router.get("/get-playlist", mediaController.getPlaylist);

router.get(
  "/get-playlist-by-playlistId/:playlist_id",
  mediaController.getPlaylistById,
);

router.get(
  "/get-playlist-by-categoryId/:category_id",
  mediaController.getPlaylistByCategory,
);

router.post(
  "/create",
  multerS3Upload.upload("media_upload"),
  mediaController.createMedia,
);

router.put(
  "/update/:media_id",
  multerS3Upload.upload("media_upload"),
  mediaController.updateMedia,
);

router.delete("/delete/:media_id", mediaController.deleteMedia);

router.delete("/delete-file/:media_id", mediaController.deleteMediaFile);

router.get("/get-media-list", mediaController.getMediaFile);

router.get("/get-single-media/:media_id", mediaController.getMediaById);

router.get(
  "/get-media-by-playlist/:playlist_id",
  mediaController.getMediaByPlaylist,
);

router.post("/add-media-in-playlist", mediaController.addMediaIntoPlaylist);

router.post(
  "/add-playlist-in-category",
  mediaController.addPlaylistIntoCategory,
);

// Update playlist media order
router.put(
  "/change-playlist-media-order/:playlist_id",
  mediaValidator.changePlaylistMediaOrder(),
  mediaController.changePlaylistMediaOrder,
);

// Update playlist media track
router.put(
  "/update-playlist-media-track/:playlist_id",
  mediaValidator.updatePlaylistMediaTrack(),
  mediaController.updatePlaylistMediaTrack,
);

// Get playlist media track data
router.get(
  "/get-playlist-media-track-data/:playlist_id",
  mediaController.getPlaylistMediaTrackData,
);

router.delete(
  "/remove-media-from-playlist",
  mediaController.removeMediaFromPlaylist,
);

export default router;
