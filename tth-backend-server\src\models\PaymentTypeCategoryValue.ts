"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { PaymentTypeCategoryField } from "./PaymentTypeCategoryField";
import { PaymentTypeCategoryBranch } from "./PaymentTypeCategoryBranch";


interface PaymentTypeCategoryValueAttributes {
  id: number;
  payment_type_category_field_id: number;
  payment_type_category_branch_id: number;
  field_value: string;
  payment_type_category_value_status: string;
  created_by: number;
  updated_by: number;
}

export enum payment_type_category_value_status {
  ACTIVE = "active",
  INACTIVE = "inactive"
}



export class PaymentTypeCategoryValue
  extends Model<PaymentTypeCategoryValueAttributes, never>
  implements PaymentTypeCategoryValueAttributes {
  id!: number;
  payment_type_category_field_id!: number;
  payment_type_category_branch_id!: number;
  field_value!: string;
  payment_type_category_value_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

PaymentTypeCategoryValue.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    payment_type_category_field_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    payment_type_category_branch_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    field_value: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    payment_type_category_value_status: {
      type: DataTypes.ENUM,
      values: Object.values(payment_type_category_value_status),
      defaultValue: payment_type_category_value_status.ACTIVE,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_payment_type_category_value",
    modelName: "PaymentTypeCategoryValue",
  },
);

PaymentTypeCategoryValue.belongsTo(PaymentTypeCategoryField, { foreignKey: "payment_type_category_field_id", as: "payment_type_category_field_list" });
PaymentTypeCategoryField.hasOne(PaymentTypeCategoryValue, { foreignKey: "payment_type_category_field_id", as: "payment_type_category_field" });

PaymentTypeCategoryValue.belongsTo(PaymentTypeCategoryBranch, { foreignKey: "payment_type_category_branch_id", as: "payment_type_category_branch_list" });
PaymentTypeCategoryBranch.hasMany(PaymentTypeCategoryValue, { foreignKey: "payment_type_category_branch_id", as: "payment_type_category_branch_field" });

// Define hooks for Card model
PaymentTypeCategoryValue.addHook("afterUpdate", async (paymentTypeCategoryValue: any) => {
  await addActivity("PaymentTypeCategoryValue", "updated", paymentTypeCategoryValue);
});

PaymentTypeCategoryValue.addHook("afterCreate", async (paymentTypeCategoryValue: PaymentTypeCategoryValue) => {
  await addActivity("PaymentTypeCategoryValue", "created", paymentTypeCategoryValue);
});

PaymentTypeCategoryValue.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

