"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { DocumentCategory } from "./DocumentCategory";
import { Department } from "./Department";

interface documentCategoryDepartmentAttributes {
  category_id: number;
  department_id: number;
  document_category_department_status: string;
  created_by: number;
  updated_by: number;
}

export enum document_category_department_status {
  ACTIVE = "active",
  DRAFT = "draft",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export class DocumentCategoryDepartment
  extends Model<documentCategoryDepartmentAttributes, never>
  implements documentCategoryDepartmentAttributes {
  category_id!: number;
  department_id!: number;
  document_category_department_status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

DocumentCategoryDepartment.init(
  {
    category_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    department_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    document_category_department_status: {
      type: DataTypes.ENUM,
      values: Object.values(document_category_department_status),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_document_category_deparment",
    modelName: "DocumentCategoryDepartment",
  },
);

DocumentCategoryDepartment.removeAttribute("id");
Department.hasMany(DocumentCategoryDepartment, { foreignKey: "department_id", as: "department_wise" });
DocumentCategoryDepartment.belongsTo(Department, { foreignKey: "department_id", as: "category_departments" });
DocumentCategory.hasMany(DocumentCategoryDepartment, { foreignKey: "category_id" });
DocumentCategoryDepartment.belongsTo(DocumentCategory, { foreignKey: "category_id" });

DocumentCategoryDepartment.addHook("afterUpdate", async (documentCategoryDepartment: any) => {
  await addActivity("DocumentCategoryDepartment", "updated", documentCategoryDepartment);
});

DocumentCategoryDepartment.addHook(
  "afterCreate",
  async (documentCategoryDepartment: DocumentCategoryDepartment) => {
    await addActivity("DocumentCategoryDepartment", "created", documentCategoryDepartment);
  },
);

DocumentCategoryDepartment.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
