#!/usr/bin/env ts-node

/**
 * Migration Script: Migrate Users to MORole System
 * 
 * This script migrates existing users from the old role system to the new MORole system
 * by populating the user_role_id field based on their highest role in the UserRole table.
 * 
 * Usage:
 * npm run migrate:users-to-morole
 * 
 * Or directly:
 * npx ts-node scripts/migrate-users-to-morole.ts
 */

import { migrateAllUsersToMORole } from '../src/services/auth.service';
import { sequelize } from '../src/models';

async function runMigration() {
    try {
        console.log('🚀 Starting User to MORole Migration...');
        console.log('=====================================');
        
        // Ensure database connection
        await sequelize.authenticate();
        console.log('✅ Database connection established');
        
        // Run the migration
        await migrateAllUsersToMORole();
        
        console.log('=====================================');
        console.log('✅ Migration completed successfully!');
        console.log('');
        console.log('Summary:');
        console.log('- All existing users have been analyzed');
        console.log('- Users with highest roles have been mapped to MORole system');
        console.log('- user_role_id field has been populated for eligible users');
        console.log('');
        console.log('Next steps:');
        console.log('1. Verify the migration results in your database');
        console.log('2. Test the enhanced permission functions');
        console.log('3. Monitor application logs for any issues');
        
    } catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    } finally {
        // Close database connection
        await sequelize.close();
        console.log('🔌 Database connection closed');
        process.exit(0);
    }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});

// Run the migration
if (require.main === module) {
    setTimeout(()=> {
        runMigration();
    }, 100000)
}

export { runMigration };
