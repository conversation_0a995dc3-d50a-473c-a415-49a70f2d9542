"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface roleAttributes {
  id: number;
  checklist_id: number;
  user_id: number;
  insurance_number: string;
  postgraduate_loan: boolean;
  statement_apply: boolean;
  is_current_information: boolean;
  another_job: boolean;
  private_pension: boolean;
  payment_from: boolean;
  load_guidance: string;
  statementA: boolean;
  statementB: boolean;
  statementC: boolean;
  status: string;
  created_by: number;
  updated_by: number;
}

export class HrmcForm
  extends Model<roleAttributes, never>
  implements roleAttributes {
  id!: number;
  checklist_id!: number;
  user_id!: number;
  insurance_number!: string;
  postgraduate_loan!: boolean;
  statement_apply!: boolean;
  is_current_information!: boolean;
  another_job!: boolean;
  private_pension!: boolean;
  payment_from!: boolean;
  load_guidance!: string;
  statementA!: boolean;
  statementB!: boolean;
  statementC!: boolean;
  status!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

export enum status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

HrmcForm.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    checklist_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    insurance_number: {
      type: DataTypes.STRING,
    },
    postgraduate_loan: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    statement_apply: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    is_current_information: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    another_job: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    private_pension: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    payment_from: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    load_guidance: {
      type: DataTypes.STRING,
    },
    statementA: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    statementB: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    statementC: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    status: {
      type: DataTypes.ENUM,
      values: Object.values(status),
      defaultValue: status.ACTIVE,
    },
    created_by: {
      type: DataTypes.STRING,
    },
    updated_by: {
      type: DataTypes.STRING,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_hrmc_form",
    modelName: "HrmcForm",
  },
);

// Define hooks for HRMC model
HrmcForm.addHook("afterUpdate", async (hrmcFrom: any) => {
  await addActivity("HrmcForm", "updated", hrmcFrom);
});

HrmcForm.addHook("afterCreate", async (hrmcFrom: HrmcForm) => {
  await addActivity("HrmcForm", "created", hrmcFrom);
});

HrmcForm.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});


