import { Router } from "express";
import onbordingController from "../../controller/onbording.controller";
import onbordingValidator from "../../validators/onbording.validator";
const router: Router = Router();
import { multerS3 } from "../../helper/upload.service";
import { FILE_UPLOAD_CONSTANT } from "../../helper/constant";

// Configure multerS3 for onboarding documents
const onboardingFileUpload = multerS3(
  process.env.NODE_ENV!,
  FILE_UPLOAD_CONSTANT.ONBOARDING_FILES.folder,
);

router.post(
  "/create-form",
  onboardingFileUpload.fields([
    { name: "passport_front", maxCount: 1 },
    { name: "passport_back", maxCount: 1 },
    { name: "cv", maxCount: 1 },
    { name: "share_code", maxCount: 1 },
    { name: "brp_front", maxCount: 1 },
    { name: "brp_back", maxCount: 1 },
    { name: "p45", maxCount: 1 },
    { name: "ni_letter", maxCount: 1 },
    { name: "student_letter", maxCount: 1 },
    { name: "statements_dl_utility", maxCount: 1 },
    { name: "photoID", maxCount: 1 },
    { name: "hmrc_p45_form", maxCount: 1 },
  ]),
  onbordingController.createForm,
);
router.get("/request-form", onbordingController.requestForm);
router.put(
  "/update-form",
  onboardingFileUpload.fields([
    { name: "passport_front", maxCount: 1 },
    { name: "passport_back", maxCount: 1 },
    { name: "cv", maxCount: 1 },
    { name: "share_code", maxCount: 1 },
    { name: "brp_front", maxCount: 1 },
    { name: "brp_back", maxCount: 1 },
    { name: "p45", maxCount: 1 },
    { name: "ni_letter", maxCount: 1 },
    { name: "student_letter", maxCount: 1 },
    { name: "statements_dl_utility", maxCount: 1 },
    { name: "photoID", maxCount: 1 },
    { name: "hmrc_p45_form", maxCount: 1 },
  ]),
  onbordingController.updateForm,
);
router.post(
  "/delete-file",
  onbordingValidator.deleteFile(),
  onbordingController.deleteUploadedFiles,
);
router.get("/get-check-list", onbordingController.getCheckList);
router.get("/get-form-detail", onbordingController.getUserFormDetail);
router.get(
  "/get-health-safety-checklist",
  onbordingController.getHealthSafetyCheckList,
);
router.post(
  "/checklist-verification",
  onbordingValidator.formVerification(),
  onbordingController.verifyUserCheckList,
);
router.post("/approve-reject-form", onbordingController.approveRejectForm);
router.get(
  "/get-verification-checklist",
  onbordingController.getverifiedUserCheckList,
);
router.get(
  "/get-right-to-work-form",
  onbordingController.getRightToWorkFormData,
);
router.get(
  "/download-right-to-work-zip",
  onbordingController.downloadRightTOWorkFiles,
);
router.get(
  "/get-health-safety-progress",
  onbordingController.getHealthSafetyProgress,
);
router.get(
  "/regenerate-employment-contract/:user_id",
  onbordingController.regenerateEmploymentContract,
);
router.post(
  "/user-onboarding-reset",
  onbordingValidator.UserOnboardingReset(),
  onbordingController.userOnboardingReset,
);

export { router as default };
