import { Segments, Jo<PERSON>, celebrate } from "celebrate";
export default {
  createDepartment: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        department_name: Joi.string().required(),
        department_remark: Joi.string().allow(null),
        departmentStatus : Joi.string().required()
      }),
    }),
  updateDepartment: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        department_name: Joi.string().required(),
        departmentStatus : Joi.string().required(),
        department_remark: Joi.string().allow(null),
      }),
    }),
};
