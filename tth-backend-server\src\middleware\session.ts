import { NextFunction, Request, Response } from "express";
import { Session, sessions } from "../helper/session.service";

const sessionValidator = (req: Request, res: Response, next: NextFunction) => {
  try {
    // set message of unauthorised accordingly
    if (!req.cookies) {
      res.status(401).send({ message: "Unauthorized request" });
      return;
    }

    const sessionToken = req.cookies["secure_session"];
    if (!sessionToken) {
      res.status(401).send({ message: "Unauthorized request" });
      return;
    }
    const userSession: Session | undefined = sessions[sessionToken];
    if (!userSession) {
      // If the session token is not present in session map, return an unauthorized error
      res.status(401).send({ message: "Unauthorized request" });
      return;
    }
    // if the session has expired, return an unauthorized error, and delete the
    // session from our map
    if (userSession.isExpired()) {
      delete sessions[sessionToken];
      res.status(401).send({ message: "Session Expired" });
      return;
    }
    next();
  } catch (e: any) {
    console.log(e);
    return res.status(403).send({ status: false, message: e?.message });
  }
};

export default sessionValidator;
