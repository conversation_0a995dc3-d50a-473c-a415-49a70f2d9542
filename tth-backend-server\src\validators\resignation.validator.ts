import { Segments, Joi, celebrate } from "celebrate";
export default {
  sendResignation: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        resignation_subject: Joi.string().required(),
        resignation_reason: Joi.string().required(),
      }),
    }),
  updateResignationRequest: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        resignation_status: Joi.string().valid('in-discussion', 'accepted', 'rejected', "cancelled").required(),
        remarks: Joi.when('resignation_status', {
          is: Joi.string().valid('in-discussion'),
          then: Joi.string().required(),
          otherwise: Joi.string().allow(null, ''),
        }),
        last_serving_date: Joi.when('resignation_status', {
          is: Joi.string().valid('accepted'),
          then: Joi.date().required(),
          otherwise: Joi.date().allow(null, ''),
        }),
      }),
    }),
  verifyLeavingCheckList: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        verified_checklist: Joi.array().required().items(Joi.number().required()).min(1),
      }),
    }),
};
