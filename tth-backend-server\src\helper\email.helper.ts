import nodemailer from "nodemailer";
import path from "path";
import handlebars from "handlebars";
import { addMail, readHTMLFile } from "./common";
import { mail_status } from "../models/Mail";

const sendEmail = async function (
  to: any,
  subject: any,
  template: any,
  from = global.config.FROM_EMAIL,
  data: any,
  attachments?: any
) {
  try {
    let transporter = null;
    if (
      typeof global.config.IS_EMAIL_USE_SMTP !== "undefined" &&
      global.config.IS_EMAIL_USE_SMTP == "on"
    ) {
      transporter = nodemailer.createTransport({
        host: global.config.EMAIL_HOST,
        port: global.config.EMAIL_PORT,
        secure: global.config.EMAIL_PORT == 465 ? true : false,
        auth: {
          user: global.config.FROM_EMAIL,
          pass: global.config.EMAIL_PASSWORD,
        },
      });
    } else {
      transporter = nodemailer.createTransport({
        sendmail: true,
        newline: "unix",
        path: "/usr/sbin/sendmail",
      });
    }

    // Handle multiple recipients
    const toEmails = Array.isArray(to) ? to : to.split(",");
    const mainRecipient = toEmails[0];
    const ccRecipients = toEmails.slice(1, Math.ceil(toEmails.length / 2) + 1);
    const bccRecipients = toEmails.slice(Math.ceil(toEmails.length / 2) + 1);

    const mailOptions: any = {
      from: from,
      to: mainRecipient,
      cc: ccRecipients.length > 0 ? ccRecipients.join(',') : undefined,
      bcc: bccRecipients.length > 0 ? bccRecipients.join(',') : undefined,
      subject: `${subject} ${process.env.NEXT_NODE_ENV === undefined
        ? "(development)"
        : process.env.NEXT_NODE_ENV !== "production"
          ? `(${process.env.NEXT_NODE_ENV})`
          : ""
        }`,
      html: template
    };

    const mail_detail: any = {
      mail_subject: mailOptions.subject,
      mail_to: mailOptions.to,
      mail_cc: mailOptions.cc,
      mail_bcc: mailOptions.bcc,
      mail_from: mailOptions.from,
      mail_body: JSON.stringify(data)
    }
    if (attachments && attachments.length > 0) {
      mailOptions.attachments = attachments

    }
    console.log("mailOptions", mailOptions);
    console.log("mail_detail", mail_detail);
    if (to && to != "") {
      return await transporter.sendMail(
        mailOptions,
        async (error: any, info: any) => {
          console.log("error", error);
          console.log("info", info);
          if (error) {
            mail_detail.mail_status = mail_status.FAILED
            mail_detail.mail_response = JSON.stringify(error)
            await addMail(mail_detail)
            console.log("\n if Email fail ==> ", error);
            // const templateData = {
            //   message: error.message ? error.message : "",
            // };
          } else if (info) {
            mail_detail.mail_response = JSON.stringify(info)
            mail_detail.mail_status = mail_status.SENT
            await addMail(mail_detail)
          }
        },
      );
    } else {
      return;
    }
  } catch (e) {
    console.log("\nEmail failed catch ==> ", e);
  }
};

const emailSender = (to: string | string[], subject: string, data: any, template: any, attachments?: any,) => {
  readHTMLFile(
    path.join(__dirname, `../../src/email_templates/${template}.html`),
    async function (err: any, html: any) {
      try {
        const compiledTemplate = handlebars.compile(html);
        const htmlToSend = compiledTemplate(data);
        await sendEmail(to, subject, htmlToSend, global.config.FROM_EMAIL, data, attachments);
      } catch (e) {
        console.log("error", e);
      }
    },
  );
};

export { emailSender };
