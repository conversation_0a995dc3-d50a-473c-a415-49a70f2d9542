import { StatusCodes } from "http-status-codes";
import { Response } from "express";
import { User, user_status } from "../models/User";
import { CheckList } from "../models/CheckList";
import { EMAIL_ADDRESS, FILE_UPLOAD_CONSTANT, NOTIFICATION_TYPE, NOTIFICATIONCONSTANT, REDIRECTION_TYPE } from "../helper/constant";
import { addFooterToPDF, createNotification, formatUserAgentData, regenerateEmploymentContractFuncation, restoreTrackCategoryOfUser, sendEmailNotification, getOrganizationLogo, getHash } from "../helper/common";
// import path from "path";
import { RightToWorkCheckList, status } from "../models/RightToWorkCheckList";
import { StarterForm } from "../models/StarterForm";
import { HrmcForm } from "../models/HrmcForm";
import { check_list_status, UserCheckList } from "../models/UserCheckList";
import { Op } from "sequelize";
import { Activity, activity_action, activity_type } from "../models/Activity";
import fs from "fs";
import {
  deleteFileFromBucket,
  s3,
} from "../helper/upload.service";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import {
  Item,
  item_external_location,
  item_IEC,
  item_status,
  item_type,
} from "../models/Item";

const userVerification = async (req: any, res: Response) => {
  try {
    const {
      verification_status,
      checklist_ids,
      last_reject_remark,
      user_id,
    }: any = req.body;
    const findUserDetail: any = await User.findOne({
      attributes: ['id', 'user_first_name', 'user_middle_name', 'user_last_name', 'user_email', 'organization_id', 'appToken', 'webAppToken'],
      where: {
        id: user_id,
        organization_id: req.user.organization_id,
        user_status: {
          [Op.in]: [
            user_status.COMPLETED,
            user_status.ONGOING,
            user_status.REJECTED,
            user_status.VERIFIED,
          ],
        },
      },
      raw: true,
    });
    if (!findUserDetail) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("FAIL_PLEASE_VERIFY_ALL_CHECKLIST"),
      });
    }
    const employeeName = [];

    if (findUserDetail?.user_first_name) {
      employeeName.push(findUserDetail.user_first_name);
    }
    if (findUserDetail?.user_middle_name) {
      employeeName.push(findUserDetail.user_middle_name);
    }
    if (findUserDetail?.user_last_name) {
      employeeName.push(findUserDetail.user_last_name);
    }

    const superEmployeeName = [];

    if (findUserDetail?.user_first_name) {
      superEmployeeName.push(req.user.user_first_name);
    }
    if (findUserDetail?.user_middle_name) {
      superEmployeeName.push(req.user.user_middle_name);
    }
    if (findUserDetail?.user_last_name) {
      superEmployeeName.push(req.user.user_last_name);
    }

    const templateData: any = {
      name: employeeName.join(" "),
      admin: superEmployeeName.join(" "),
      ORGANIZATION_LOGO: await getOrganizationLogo(req.user.organization_id),
      LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
      ADDRESS: EMAIL_ADDRESS.ADDRESS,
      PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
      EMAIL: EMAIL_ADDRESS.EMAIL,
      ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
      smtpConfig: "INFO",
    };
    if (verification_status == user_status.VERIFIED) {
      let documentItemId = "";

      if (req?.files && req.files.length > 0) {
        const bucketName = process.env.NODE_ENV || "development";
        // const documentFile = req.files[0];
        const fileName = `user_verification_document_${findUserDetail?.user_first_name}_${findUserDetail?.user_last_name}_${findUserDetail.id}_${Date.now()}.pdf`;
        const filePath =
          FILE_UPLOAD_CONSTANT.USER_VERIFICATION_DOC_PATH.destinationPath(
            req.user.organization_id,
            user_id,
            fileName,
          );

        // Add footer to PDF and get modified buffer
        const { size, fPath }: any = await addFooterToPDF(
          req.files[0],
          fileName,
          req.user.id,
        );

        const s3uploadBuffer = await fs.readFileSync(fPath);
        // Upload to S3
        await s3.send(
          new PutObjectCommand({
            Bucket: bucketName,
            Key: filePath,
            Body: s3uploadBuffer,
            ContentType: "application/pdf",
          }),
        );

        const fileHash: any = await getHash(s3uploadBuffer, {
          originalname: fileName,
          path: fPath,
          size: size,
        });

        // Create item record
        const saveItem: any = {
          item_type: item_type.PDF,
          item_name: fileName,
          item_hash: fileHash.hash,
          item_mime_type: "application/pdf",
          item_extension: ".pdf",
          item_size: size,
          item_IEC: item_IEC.B,
          item_status: item_status.ACTIVE,
          item_external_location: item_external_location.NO,
          item_location: filePath,
          item_organization_id: req.user.organization_id,
        };

        const item = await Item.create(saveItem);
        documentItemId = item.id.toString();
        await fs.unlinkSync(fPath)
      }

      await User.setHeaders(req).update(
        {
          user_status: user_status.VERIFIED,
          user_verification_doc: documentItemId,
          updated_by: user_id,
          confirm_by: req.user.id,
          confirm_by_date: new Date(),
        } as any,
        { where: { id: user_id } },
      );

      templateData.email = findUserDetail.user_email;
      (templateData.mail_type = "form_approved"),
        await sendEmailNotification(templateData)
      await createNotification([findUserDetail], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.ONBOARDING_FORM_REQUEST.content(superEmployeeName.join(' '), user_status.VERIFIED), NOTIFICATIONCONSTANT.ONBOARDING_FORM_REQUEST.heading, REDIRECTION_TYPE.ONBOARDING, findUserDetail.id, { user_id: findUserDetail.id })
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FORM_VERIFIED"),
      });
    } else if (verification_status == user_status.REJECTED) {
      if (checklist_ids.length > 0) {
        let updateUserStatus: any;

        for (const checklist_id of checklist_ids) {
          const findCheckList: any = await CheckList.findOne({
            where: { id: checklist_id },
          });

          if (findCheckList.prefix == "RTWC") {
            const getRightToWork = await RightToWorkCheckList.findOne({
              where: { user_id: user_id, checklist_id: findCheckList.id },
            });
            if (getRightToWork) {
              const rightToWorkRemoved = await RightToWorkCheckList.setHeaders(
                req,
              ).update(
                {
                  passport_front: null,
                  passport_back: null,
                  cv: null,
                  share_code: null,
                  brp_front: null,
                  brp_back: null,
                  p45: null,
                  ni_letter: null,
                  student_letter: null,
                  statements_dl_utility: null,
                  has_right_to_work_in_uk: null,
                  is_uk_citizen: null,
                  photoID: null,
                  is_confirm_upload: null,
                  status: status.INACTIVE,
                  form_name: null,
                } as any,
                { where: { checklist_id: findCheckList.id, user_id: user_id } },
              );

              if (rightToWorkRemoved.length > 0) {
                await UserCheckList.setHeaders(req).update(
                  { status: check_list_status.PENDING, is_last_rejected: true },
                  {
                    where: {
                      to_user_id: user_id,
                      from_user_id: user_id,
                      checklist_id: findCheckList.id,
                    },
                  },
                );

                // Get file items from the database and delete from S3
                const filesToDelete = {
                  passport_front: getRightToWork.passport_front,
                  passport_back: getRightToWork.passport_back,
                  cv: getRightToWork.cv,
                  share_code: getRightToWork.share_code,
                  brp_front: getRightToWork.brp_front,
                  brp_back: getRightToWork.brp_back,
                  p45: getRightToWork.p45,
                  ni_letter: getRightToWork.ni_letter,
                  student_letter: getRightToWork.student_letter,
                  statements_dl_utility: getRightToWork.statements_dl_utility,
                  photoID: getRightToWork.photoID,
                };

                // Delete items from S3 and from Item table
                for (const [itemId] of Object.entries(filesToDelete)) {
                  if (itemId) {
                    const item = await Item.findOne({
                      where: { id: itemId },
                      attributes: ["id", "item_location"],
                      raw: true,
                    });

                    if (item) {
                      await deleteFileFromBucket(
                        process.env.NODE_ENV || "development",
                        item.item_location,
                      );
                      await Item.update(
                        { item_status: item_status.DELETED },
                        { where: { id: itemId } },
                      );
                    }
                  }
                }
              }
            }
          } else if (findCheckList.prefix == "NS&HMRCF") {
            const getNewStarter: any = await StarterForm.findOne({
              where: { user_id: user_id, checklist_id: findCheckList.id },
            });
            let hrmcFormRemoved: any;
            let starterFormRemoved: any;
            if (getNewStarter) {
              starterFormRemoved = await StarterForm.setHeaders(req).update(
                {
                  medical_disability: 0,
                  medical_disability_detail: null,
                  kin1_name: null,
                  kin1_relation: null,
                  kin1_address: null,
                  kin1_mobile_number: null,
                  kin2_name: null,
                  kin2_relation: null,
                  kin2_address: null,
                  kin2_mobile_number: null,
                  professional1_name_contact: null,
                  professional1_role_description: null,
                  professional2_name_contact: null,
                  professional2_role_description: null,
                  passport_no: null,
                  issued_date: null,
                  permit_type: null,
                  permit_type_other: null,
                  validity: null,
                  bank_account_name: null,
                  bank_account_number: null,
                  bank_sort_code: null,
                  bank_society_name: null,
                  bank_address: null,
                  professional1_start_date: null,
                  professional1_end_date: null,
                  professional2_start_date: null,
                  professional2_end_date: null,
                  has_student_or_pg_loan: null,
                  has_p45_form: null,
                  hmrc_p45_form: null,
                  status: status.INACTIVE,
                } as any,
                { where: { user_id: user_id } },
              );
            }
            const getHrmcForm = await HrmcForm.findOne({
              where: { user_id: user_id, checklist_id: findCheckList.id },
            });
            if (getHrmcForm) {
              hrmcFormRemoved = await HrmcForm.setHeaders(req).update(
                {
                  insurance_number: null,
                  postgraduate_loan: null,
                  statement_apply: null,
                  is_current_information: null,
                  another_job: null,
                  private_pension: null,
                  payment_from: null,
                  load_guidance: null,
                  statementA: null,
                  statementB: null,
                  statementC: null,
                  status: status.INACTIVE,
                } as any,
                { where: { user_id: user_id } },
              );
            }
            if (hrmcFormRemoved?.length > 0 && starterFormRemoved?.length > 0) {
              await UserCheckList.setHeaders(req).update(
                { status: check_list_status.PENDING, is_last_rejected: true },
                {
                  where: {
                    to_user_id: user_id,
                    from_user_id: user_id,
                    checklist_id: findCheckList.id,
                  },
                },
              );

              // Delete P45 form if exists
              if (getNewStarter?.hmrc_p45_form) {
                const item = await Item.findOne({
                  where: { id: getNewStarter.hmrc_p45_form },
                  attributes: ["id", "item_location"],
                  raw: true,
                });

                if (item) {
                  await deleteFileFromBucket(
                    process.env.NODE_ENV || "development",
                    item.item_location,
                  );
                  await Item.update(
                    { item_status: item_status.DELETED },
                    { where: { id: getNewStarter.hmrc_p45_form } },
                  );
                }
              }
            }
          } else if (findCheckList.prefix == "HSI") {
            const getHealthSafetyForm: any = await restoreTrackCategoryOfUser(
              user_id,
              req.user.id,
            );
            if (getHealthSafetyForm) {
              await Activity.create({
                activity_table: "Health Safety",
                reference_id: getHealthSafetyForm.id,
                activity_type: activity_type.SUCCESS,
                activity_action: activity_action.DELETED,
                ip_address: req.headers?.["ip-address"],
                userAgent: `${req.headers?.["platform-type"]} : ${formatUserAgentData(req.headers?.["user-agent"], req.headers?.["platform-type"])}`,
                address: req.headers?.["address"],
                location: req.headers?.["location"],
                organization_id: findUserDetail.organization_id
                  ? findUserDetail.organization_id
                  : null,
                created_by: user_id,
                updated_by: user_id,
              } as any);
              await UserCheckList.setHeaders(req).update(
                { status: check_list_status.PENDING, is_last_rejected: true },
                {
                  where: {
                    to_user_id: user_id,
                    from_user_id: user_id,
                    checklist_id: checklist_id,
                  },
                },
              );
              return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_REJECT_FORM_VERIFICATION"),
              });
            }
          } else if (findCheckList.prefix == "EC") {
            await regenerateEmploymentContractFuncation(
              user_id,
              req,
              false,
              true,
            );
          }
          // Update user status and destroy non-matching checklist entries for this checklist_id
          updateUserStatus = await User.setHeaders(req).update(
            {
              user_status: user_status.REJECTED,
              last_reject_remark: last_reject_remark,
              updated_by: user_id,
              confirm_by: req.user.id,
              confirm_by_date: new Date(),
            } as any,
            { where: { id: user_id } },
          );
          await UserCheckList.destroy({
            where: {
              to_user_id: user_id,
              from_user_id: { [Op.not]: user_id },
              checklist_id: checklist_id,
            },
          });
        }

        // Send email after all processing is done
        if (updateUserStatus.length > 0) {
          templateData.reason = last_reject_remark;
          templateData.email = findUserDetail.user_email;
          (templateData.mail_type = "form_rejected"),
            await sendEmailNotification(templateData)
          await createNotification([findUserDetail], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.ONBOARDING_FORM_REQUEST.content(superEmployeeName.join(' '), user_status.REJECTED), NOTIFICATIONCONSTANT.ONBOARDING_FORM_REQUEST.heading, REDIRECTION_TYPE.ONBOARDING, findUserDetail.id, { user_id: findUserDetail.id })
          return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_REJECT_FORM_VERIFICATION"),
          });
        }
      } else {
        return res.status(StatusCodes.EXPECTATION_FAILED).json({
          status: false,
          message: res.__("PLEASE_SELECT_ANY_FORM"),
        });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

export default {
  userVerification,
};
