import { NextFunction, Request, Response } from "express";
import { getGeneralSetting } from "../helper/common";
const checkAppVersion = async (req: Request, res: Response, next: NextFunction) => {
    try {
        /**  App version check */
        const platformType = req.headers['platform-type'];
        const appVersion = req.headers['app-version'];
        const findGeneralSetting: any = await getGeneralSetting()
        const isVersionOutOfBounds = (version: string, minVersion: string, maxVersion: string) => {
            return (version && minVersion && compareVersions(version, "<", minVersion)) ||
                (version && maxVersion && compareVersions(version, ">", maxVersion));
        };

        const isValidPlatform = (platform: any, version: any) => {
            switch (platform) {
                case "android":
                    return isVersionOutOfBounds(version, findGeneralSetting?.MIN_ANDROID_VERSION, findGeneralSetting?.MAX_ANDROID_VERSION);
                case "ios":
                    return isVersionOutOfBounds(version, findGeneralSetting.MIN_IOS_VERSION, findGeneralSetting.MAX_IOS_VERSION);
                default:
                    return false;
            }
        }
        if (req.headers['platform-type'] && req.headers['app-version']) {
            // Set review version in config only when app is in review  otherwise set it to empty
            if ((findGeneralSetting?.REVIEW_ANDROID_VERSION && findGeneralSetting?.REVIEW_ANDROID_VERSION == req.headers['app-version'] && platformType == "android") || (findGeneralSetting?.REVIEW_IOS_VERSION && findGeneralSetting?.REVIEW_IOS_VERSION == req.headers['app-version'] && platformType == "ios")) {
                res.setHeader("App-Version", "OK");
            } else {
                if (isValidPlatform(platformType, appVersion)) {
                    res.setHeader("App-Version", "Upgrade-Required");
                } else if (platformType === "android" && compareVersions(appVersion, "<", findGeneralSetting.MAX_ANDROID_VERSION)) {
                    res.setHeader("App-Version", "Upgrade");
                } else if (platformType === "ios" && compareVersions(appVersion, "<", findGeneralSetting.MAX_IOS_VERSION)) {
                    res.setHeader("App-Version", "Upgrade");
                } else {
                    res.setHeader("App-Version", "OK");
                }
            }
        } else {
            res.setHeader("App-Version", "OK");
        }
        // Set maintenance mode header based on maintenance-mode header and platform type
        if (req.headers['platform-type'] && req.headers['maintenance-mode']) {
            let maintenanceValue = "false";
            if (findGeneralSetting?.MAINTENANCE_MODE == "true") {
                maintenanceValue = "true";
            }
            res.setHeader("Maintenance-Mode", maintenanceValue);
        } else {
            res.setHeader("Maintenance-Mode", "false");
        }
        // res.setHeader('STORE_LINK', findGeneralSetting?.STORE_LINK);
        next()
    } catch (e) {
        return res.status(301).send({ status: false, message: e })
    }
}

function compareVersions(v1: any, comparator: string, v2: string) {
    "use strict";
    if (v1 && v2 && v1 != "" && v2 != "") {
        // const comparatory = comparator as any == '=' ? '==' : comparator;
        if (['==', '===', '<', '<=', '>', '>=', '!=', '!=='].indexOf(comparator) == -1) {
            throw new Error('Invalid comparator. ' + comparator);
        }
        const v1parts = v1.split('.'), v2parts = v2.split('.');
        const maxLen = Math.max(v1parts.length, v2parts.length);
        let part1, part2;
        let cmp = 0;
        for (let i = 0; i < maxLen && !cmp; i++) {
            part1 = parseInt(v1parts[i], 10) || 0;
            part2 = parseInt(v2parts[i], 10) || 0;
            if (part1 < part2)
                cmp = 1;
            if (part1 > part2)
                cmp = -1;
        }
        return eval('0' + comparator + cmp);
    } else {
        return false
    }
}

export default checkAppVersion