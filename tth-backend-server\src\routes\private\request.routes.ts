import { Router } from "express";
import requestController, { createOrUpdateLeavePolicy, createOrUpdateLeaveType, deleteLeavePolicy, deleteLeaveType, getLeavePolicies, getLeaveTypes } from "../../controller/request.controller";
import requestValidator from "../../validators/request.validator";
import contractController from "../../controller/contract.controller";
const router: Router = Router();

router.post(
  "/apply-leave",
  requestValidator.applyLeave(),
  requestController.leaveApply,
);

router.get("/get-leave-list", requestController.getOwnLeaveList);

router.post(
  "/approve-reject-request",
  requestValidator.approveRejectRequest(),
  requestController.approveRejectRequest,
);


router.get("/get-staff-leave-list", requestController.getStaffLeaveList);

router.post('/leave-type/:id?', requestValidator.createLeaveType(), createOrUpdateLeaveType); // Create or Update
router.get('/leave-types', getLeaveTypes); // List
router.delete('/leave-type/:id', deleteLeaveType); // Delete

router.post('/leave-policy/:id?', requestValidator.createLeavePolicy(), createOrUpdateLeavePolicy); // Create or Update
router.get('/leave-policies', getLeavePolicies); // List
router.delete('/leave-policy/:id', deleteLeavePolicy); // Delete

router.get('/get-user-leave-policies/:user_id', contractController.getUserLeavePolicies); //get user leave policies List

router.get('/get-timewise-leave-list', requestController.getUserLeaveListTimeWise); //get user leave policies List

router.post('/cancel-leave-request', requestValidator.cancelLeaveRequest(), requestController.cancelLeaveRequest)

router.get('/get-user-leave-count/:user_id', requestController.LeaveRequestCount)

router.get('/get-calender-wise-leave', requestController.getCalenderWiseLeaveList)

router.get('/get-leave-by-id/:leave_id', requestController.getLeaveDetailById)

router.get('/get-leave-rules', requestController.getLeaveRuleList)

router.put('/modify-leave-rules', requestController.modifyLeaveRule)

router.get('/get-leave-reports', requestController.getLeaveReportList)

router.get('/get-leave-type-reports', requestController.getLeaveTypeReport)

router.get('/get-leave-consumption-reports', requestController.getLeaveConsumptionReportList)

router.get('/get-user-leave-balance/:id', requestController.getUserLeaveBalance)

router.post('/convert-leave-format', requestValidator.convertLeaveFormat(), requestController.convertLeaveDaysFormat)

export default router;
