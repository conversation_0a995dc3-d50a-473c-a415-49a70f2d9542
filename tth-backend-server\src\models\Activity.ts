"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
// import { User } from "./User";

interface activityAttributes {
  id: number;
  activity_table: string;
  activity_action: string;
  reference_id: number;
  activity_status: string;
  activity_type: string;
  previous_data: string;
  location: string;
  address: string;
  userAgent: string;
  new_data: string;
  ip_address: string;
  organization_id: string;
  created_by: number;
  updated_by: number;
}

export enum activity_action {
  CREATED = "created",
  UPDATED = "updated",
  DELETED = "deleted",
  LOGIN = 'login',
  LOGOUT = 'logout'
}

export enum activity_status {
  SENT = "sent",
  READ = "read",
  DELETED = "deleted",
}

export enum activity_type {
  SUCCESS = "success",
  FAILED = "failed",
}

export class Activity
  extends Model<activityAttributes, never>
  implements activityAttributes {
  id!: number;
  activity_table!: string;
  activity_action!: string;
  reference_id!: number;
  activity_status!: string;
  activity_type!: string;
  previous_data!: string;
  new_data!: string;
  ip_address!: string;
  location!: string;
  address!: string;
  userAgent!: string;
  organization_id!: string;
  created_by!: number;
  updated_by!: number;
}

Activity.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    activity_table: {
      type: DataTypes.STRING(50),
      allowNull: false,
    },
    activity_action: {
      type: DataTypes.ENUM,
      values: Object.values(activity_action),
      allowNull: true,
    },
    reference_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    activity_type: {
      type: DataTypes.ENUM,
      values: Object.values(activity_type),
      allowNull: true,
    },
    activity_status: {
      type: DataTypes.ENUM,
      values: Object.values(activity_status),
      allowNull: true,
    },
    previous_data: {
      type: DataTypes.TEXT,
    },
    new_data: {
      type: DataTypes.TEXT,
    },
    ip_address: {
      type: DataTypes.STRING,
    },
    location: {
      type: DataTypes.STRING
    },
    address: {
      type: DataTypes.STRING
    },
    userAgent: {
      type: DataTypes.TEXT
    },
    organization_id: {
      type: DataTypes.STRING
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_activities",
    modelName: "Activity",
  },
);

// Activity.belongsTo(User, { foreignKey: "created_by", as: "activitiesusers" });
// User.hasMany(Activity, { foreignKey: "created_by", as: "activities" });
