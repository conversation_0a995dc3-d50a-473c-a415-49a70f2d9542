"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { DocumentCategory } from "./DocumentCategory";
import { Item } from "./Item";

interface documentCategoryItemAttributes {
    category_id: number;
    item_id: number;
    document_category_item_status: string;
    document_category_item_type: string;
    document_category_item_link: string;
    order: number;
    created_by: number;
    updated_by: number;
}

export enum document_category_item_status {
    ACTIVE = "active",
    DRAFT = "draft",
    INACTIVE = "inactive",
    DELETED = "deleted",
}

export enum document_category_item_type {
    IMAGE = "image",
    VIDEO = "video",
    AUDIO = "audio",
    DOC = "doc",
    YOUTUBE = "youtube",
    PDF = "pdf"
}

export class DocumentCategoryItem
    extends Model<documentCategoryItemAttributes, never>
    implements documentCategoryItemAttributes {
    category_id!: number;
    item_id!: number;
    document_category_item_status!: string;
    document_category_item_type!: string;
    document_category_item_link!: string;
    order!: number;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

DocumentCategoryItem.init(
    {
        category_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        item_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        document_category_item_status: {
            type: DataTypes.ENUM,
            values: Object.values(document_category_item_status),
            allowNull: true,
        },
        document_category_item_type: {
            type: DataTypes.ENUM,
            values: Object.values(document_category_item_type),
            allowNull: true,
        },
        document_category_item_link: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        order: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_document_category_item",
        modelName: "DocumentCategoryItem",
    },
);

DocumentCategoryItem.removeAttribute("id");
DocumentCategory.hasMany(DocumentCategoryItem, { foreignKey: "category_id", as: "document_category" });
DocumentCategoryItem.belongsTo(DocumentCategory, { foreignKey: "category_id", as: "document_category_item" });
Item.hasMany(DocumentCategoryItem, { foreignKey: "item_id", as: "document_item" });
DocumentCategoryItem.belongsTo(Item, { foreignKey: "item_id", as: "document_item" });


DocumentCategoryItem.addHook("afterUpdate", async (documentCategoryItem: any) => {
    await addActivity("DocumentCategoryItem", "updated", documentCategoryItem);
});

DocumentCategoryItem.addHook(
    "afterCreate",
    async (documentCategoryItem: DocumentCategoryItem) => {
        await addActivity("DocumentCategoryItem", "created", documentCategoryItem);
    },
);

DocumentCategoryItem.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
});
