import { Router } from "express";
import departmentController from "../../controller/department.controller";
import departmentValidator from "../../validators/department.validator";
const router: Router = Router();

router.post(
  "/add",
  departmentValidator.createDepartment(),
  departmentController.addDepartment,
);
router.put(
  "/update/:department_id",
  departmentValidator.updateDepartment(),
  departmentController.updatedDepartment,
);
router.delete("/delete/:department_id", departmentController.deleteDepartment);
router.get("/list", departmentController.getAllDepartment);
router.get("/get-one/:department_id", departmentController.getDepartmentById);

export default router;
