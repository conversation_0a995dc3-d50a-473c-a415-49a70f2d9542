"use strict";
const { QueryTypes } = require("sequelize");

module.exports = {
  async up(queryInterface, Sequelize) {
    const getRightWorkFormOption = await queryInterface.sequelize.query(
      `SELECT * FROM nv_right_work_form_option`,
      { type: QueryTypes.SELECT },
    );

    if (getRightWorkFormOption.length === 0) {
      const insertData = [{
        right_to_checklist_data_id	: 1 ,
        description: `1. A passport (current or expired) showing the holder is a British citizen or a citizen of the UK and Colonies having the right of abode in the UK.`,
        order: 1,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
        
      },
      {
        right_to_checklist_data_id	: 1 ,
        description:`2. A passport or passport card (in either case, whether current or expired) showing that the holder is an Irish citizen.`,
        order: 2,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        right_to_checklist_data_id	: 1 ,
        description: `3. A document issued by the Bailiwick of Jersey, the Bailiwick of Guernsey or the Isle of Man,which has been verified as valid by the Home Office Employer Checking Service, showing that the holder has been granted unlimited leave to enter or remain under Appendix EU(J) to the Jersey Immigration Rules, Appendix EU to the Immigration (Bailiwick of Guernsey)Rules 2008 or Appendix EU to the Isle of Man Immigration Rules.`,
        order: 3,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },{
        right_to_checklist_data_id	: 1 ,
        description: `4. A current passport endorsed to show that the holder is exempt from immigration control, is allowed to stay indefinitely in the UK, has the right of abode in the UK, or has no time limit on their stay in the UK.`,
        order: 4,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        right_to_checklist_data_id	: 1 ,
        description: `5. A current Immigration Status Document issued by the Home Office to the holder with an endorsement indicating that the named person is allowed to stay indefinitely in the UK, or has no time limit on their stay in the UK, together with an official document giving the person’s permanent National Insurance number and their name issued by a government agency or a previous employer.`,
        order: 5,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        right_to_checklist_data_id	: 1 ,
        description: `6. A birth or adoption certificate (short or long) issued in the UK, together with an official document giving the person’s permanent National Insurance number and their name issued by a government agency or a previous employer.`,
        order: 6,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        right_to_checklist_data_id	: 1 ,
        description: `7. A birth or adoption certificate issued in the Channel Islands, the Isle of Man or Ireland,together with an official document giving the person’s permanent National Insurance number and their name issued by a government agency or a previous employer.`,
        order: 7,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        right_to_checklist_data_id	: 1 ,
        description: `8. A certificate of registration or naturalisation as a British citizen, together with an official document giving the person’s permanent National Insurance number and their name issued by a government agency or a previous employer.`,
        order: 8,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        right_to_checklist_data_id	: 2 ,
        description: `1. A current passport endorsed to show that the holder is allowed to stay in the UK and is currently allowed to do the type of work in question.`,
        order: 1,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        right_to_checklist_data_id	: 2 ,
        description: `2. A document issued by the Bailiwick of Jersey, the Bailiwick of Guernsey or the Isle of Man,which has been verified as valid by the Home Office Employer Checking Service, showing that the holder has been granted limited leave to enter or remain under Appendix EU(J) to the Jersey Immigration Rules, Appendix EU to the Immigration (Bailiwick of Guernsey) Rules 2008 or Appendix EU to the Isle of Man Immigration Rules.`,
        order: 2,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        right_to_checklist_data_id	: 2 ,
        description:`3. A current Immigration Status Document containing a photograph issued by the Home Office to the holder with a valid endorsement indicating that the named person may stay in the UK,and is allowed to do the type of work in question, together with an official document giving the person’s permanent National Insurance number and their name issued by a government agency or a previous employer.`,
        order: 3,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        right_to_checklist_data_id	: 3 ,
        description: `1. A document issued by the Home Office showing that the holder has made an application for leave to enter or remain under Appendix EU to the immigration rules (known as the EU Settlement Scheme) on or before 30 June 2021 together with a Positive Verification Notice from the Home Office Employer Checking Service.`,
        order: 1,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },{
        right_to_checklist_data_id	: 3 ,
        description:`2. A Certificate of Application (non-digital) issued by the Home Office showing that the holder has made an application for leave to enter or remain under Appendix EU to the immigration rules (known as the EU Settlement Scheme), on or after 1 July 2021, together with a Positive Verification Notice from the Home Office Employer Checking Service.`,
        order: 2,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },{
        right_to_checklist_data_id	: 3 ,
        description: `3. A document issued by the Bailiwick of Jersey, the Bailiwick of Guernsey or the Isle of Man showing that the holder has made an application for leave to enter or remain under Appendix EU(J) to the Jersey Immigration Rules or Appendix EU to the Immigration Rules (Bailiwick of Guernsey) Rules 2008, or Appendix EU to the Isle of Man Immigration Rules together with a Positive Verification Notice from the Home Office Employer Checking Service.`,
        order: 3,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        right_to_checklist_data_id	: 3 ,
        description: `4. An Application Registration Card issued by the Home Office stating that the holder is permitted to take the employment in question, together with a Positive Verification Notice from the Home Office Employer Checking Service.`,
        order: 4,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      },{
        right_to_checklist_data_id	: 3 ,
        description:`5. A Positive Verification Notice issued by the Home Office Employer Checking Service to the employer or prospective employer, which indicates that the named person may stay in the UK and is permitted to do the work in question.`,
        order: 5,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },{
        right_to_checklist_data_id	: 4 ,
        description: `1. Are photographs consistent across documents and with the person presenting themselves for work?`,
        order: 1,
        separator: true,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      } ,
      {
        right_to_checklist_data_id	: 4 ,
        description: `2. Are dates of birth correct and consistent across documents?`,
        order: 2,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        right_to_checklist_data_id	: 4 ,
        description:`3. Are expiry dates for time-limited permission to be in the UK in the future i.e. they have not passed (if applicable)?`,
        order: 3,
        separator: true,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        right_to_checklist_data_id	: 4 ,
        description: `4. Have you checked work restrictions to determine if the person is able to work for you and do the type of work you are offering? (For <span class="fw600">students</span> who have limited permission to work during termtime, you <span class="fw600">must</span> also obtain, copy and retain details of their academic term and vacation times covering the duration of their period of study in the UK for which they will be employed.)`,
        order: 4,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },{
        right_to_checklist_data_id	: 4 ,
        description: `5. Have you taken all reasonable steps to check that the document is genuine, has not been tampered with and belongs to the holder?`,
        order: 5,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },{
        right_to_checklist_data_id	: 4 ,
        description: `6. Have you checked the reasons for any different names across documents (e.g. marriage certificate, divorce decree,deed poll)? (Supporting documents should also be photocopied and a copy retained.)`,
        order: 6,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },    
      {
        right_to_checklist_data_id	: 5 ,
        description: `<span class="fw600">Passports:</span> any page with the document expiry date, the holder’s nationality, date of birth, signature, immigration permission, expiry date, biometric details, photograph and any page containing information indicating the holder has an entitlement to enter or remain in the UK (visa or entry stamp) and undertake the work in question (the front cover no longer has to be copied).`,
        order: 1,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        right_to_checklist_data_id	: 5 ,
        description: `<span class="fw600">All other documents:</span> the document in full, both sides of an immigration status document and an Application Registration Card.`,
        order: 2,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        right_to_checklist_data_id	: 6 ,
        description: `<span class="fw600">List A:</span> contains the range of documents you may accept for a person who has a continuous right to work in the UK (including British and Irish citizens). If you conduct the right to work checks correctly before employment begins, you will establish a <span class="fw600">continuous statutory excuse for the duration of that person’s employment with you.</span> You do <span class="fw600">not</span> have to conduct any follow-up checks on this individual.`,
        order: 1,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        right_to_checklist_data_id	: 6 ,
        description: `<span class="fw600">List B:</span> contains a range of documents you may accept for a person who has a temporary right to work in the UK. If you conduct the right to work checks correctly, you will establish a <span class="fw600">time-limited statutory excuse.</span> You will be required to <span class="fw600">conduct a follow-up check</span> in order to retain your statutory excuse.`,
        order: 2,
        separator: false,
        created_by: 1,
        updated_by: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      ]
      await queryInterface.bulkInsert("nv_right_work_form_option", insertData, {});
    }
  },

  async down(queryInterface, Sequelize) {
    // Add commands to revert seed here.
    // Example: await queryInterface.bulkDelete('People', null, {});
  },
};


