"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { User } from "./User";

interface userFilterAttributes {
  id: number;
  user_id: number;
  filter_name: string;
  filter_value: string;
  group_value: string;
  user_filter_status: string;
  user_filter_type: string;
  created_by: number;
  updated_by: number;
}

export enum user_filter_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export enum user_filter_type {
  DAY = "day",
  GENERAL = "general"
}


export class UserFilter
  extends Model<userFilterAttributes, never>
  implements userFilterAttributes {
  id!: number;
  user_id!: number;
  filter_name!: string;
  filter_value!: string;
  group_value!: string;
  user_filter_status!: string;
  user_filter_type!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

UserFilter.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    filter_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    filter_value: {
      type: DataTypes.TEXT('long'),
      allowNull: true
    },
    group_value: {
      type: DataTypes.TEXT('long'),
      allowNull: true
    },
    user_filter_status: {
      type: DataTypes.ENUM,
      values: Object.values(user_filter_status),
      defaultValue: user_filter_status.ACTIVE,
    },
    user_filter_type: {
      type: DataTypes.ENUM,
      values: Object.values(user_filter_type),
      allowNull: true
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_user_filter",
    modelName: "UserFilter",
  },
);

UserFilter.belongsTo(User, { foreignKey: "user_id", as: "filter" });
User.hasMany(UserFilter, { foreignKey: "user_id", as: "user_filter" });

// Define hooks for Card model
UserFilter.addHook("afterUpdate", async (userFilter: any) => {
  await addActivity("UserFilter", "updated", userFilter);
});

UserFilter.addHook("afterCreate", async (userFilter: UserFilter) => {
  await addActivity("UserFilter", "created", userFilter);
});

UserFilter.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
