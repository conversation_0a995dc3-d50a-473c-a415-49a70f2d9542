"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { User } from "./User";

interface sideLetterConfirmationAttributes {
    id: number;
    side_letter_title: string;
    side_letter_description: string;
    sender_user_id: number;
    recipient_user_id: number;
    side_letter_confirmation_status: string;
    side_letter_confirmation_date: Date;
    has_confirmation_required: boolean;
    created_by: number;
    updated_by: number;
}

export enum side_letter_confirmation_status {
    PENDING = "pending",
    COMPLETED = "completed",
    REJECTED = "rejected",
    DELETED = "deleted"
}

export class SideLetterConfirmation
    extends Model<sideLetterConfirmationAttributes>
    implements sideLetterConfirmationAttributes {
    id!: number;
    side_letter_title!: string;
    side_letter_description!: string;
    sender_user_id!: number;
    recipient_user_id!: number;
    side_letter_confirmation_status!: string;
    side_letter_confirmation_date!: Date;
    has_confirmation_required!: boolean;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

SideLetterConfirmation.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        side_letter_title: {
            type: DataTypes.TEXT('long'),
            allowNull: false,
        },
        side_letter_description: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        sender_user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        recipient_user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        side_letter_confirmation_status: {
            type: DataTypes.ENUM,
            values: Object.values(side_letter_confirmation_status),
            defaultValue: side_letter_confirmation_status.PENDING,
        },
        side_letter_confirmation_date: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        has_confirmation_required: {
            type: DataTypes.BOOLEAN,
            defaultValue: true,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_side_letters",
        modelName: "SideLetterConfirmation",
    },
);

// Define relationships

SideLetterConfirmation.belongsTo(User, { foreignKey: "sender_user_id", as: "sender_user" });
User.hasMany(SideLetterConfirmation, { foreignKey: "sender_user_id", as: "sender_side_letter" });

SideLetterConfirmation.belongsTo(User, { foreignKey: "recipient_user_id", as: "recipient_user" });
User.hasMany(SideLetterConfirmation, { foreignKey: "recipient_user_id", as: "recipient_side_letter_confirmations" });


// Define hooks for SideLetterConfirmation model
SideLetterConfirmation.addHook("afterUpdate", async (confirmation: any) => {
    await addActivity("SideLetterConfirmation", "updated", confirmation);
});

SideLetterConfirmation.addHook("afterCreate", async (confirmation: SideLetterConfirmation) => {
    await addActivity("SideLetterConfirmation", "created", confirmation);
});

SideLetterConfirmation.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
}); 