"use strict";
const { QueryTypes } = require("sequelize");
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const getHealthSafetyList = await queryInterface.sequelize.query(
      `SELECT * FROM nv_health_safety_list`,
      { type: QueryTypes.SELECT },
    );
    if (getHealthSafetyList.length == 0) {
      await queryInterface.bulkInsert(
        "nv_health_safety_list",
        [
          {
            type: "intro",
            health_safety_category_id : 1,
            field: "The company H&S policy, Organisation & Arrangements",
            status: "active",
            order: 1,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "intro",
            health_safety_category_id : 1,
            field: "Employer’s Responsiblilities",
            status: "active",
            order: 2,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "intro",
            health_safety_category_id : 1,
            field: "Employee’s Responsiblilities",
            status: "active",
            order: 3,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "intro",
            health_safety_category_id : 1,
            field: "A tour of the premises & Induction to key staff",
            status: "active",
            order: 4,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "intro",
            health_safety_category_id : 1,
            field: "Supervisory Arrangments",
            status: "active",
            order: 5,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "intro",
            health_safety_category_id : 1,
            field: "Significant risks & control measures",
            status: "active",
            order: 6,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "intro",
            health_safety_category_id : 1,
            field: "Who to report health & safety problems",
            status: "active",
            order: 7,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "intro",
            health_safety_category_id : 1,
            field:
              "Prohibitions, areas that are out of bounds, prohibited activities",
            status: "active",
            order: 8,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "fire",
            health_safety_category_id : 2,
            field: "Sound of alarm & time of test",
            status: "active",
            order: 9,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "fire",
            health_safety_category_id : 2,
            field: "How to raise the fire alarm",
            status: "active",
            order: 10,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "fire",
            health_safety_category_id : 2,
            field: "Specific duties, if any",
            status: "active",
            order: 11,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "fire",
            health_safety_category_id : 2,
            field: "How to evacualte the building",
            status: "active",
            order: 12,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "fire",
            health_safety_category_id : 2,
            field: "Exit locations",
            status: "active",
            order: 13,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "fire",
            health_safety_category_id : 2,
            field: "How exits work",
            status: "active",
            order: 14,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "fire",
            health_safety_category_id : 2,
            field: "Assembly Points",
            status: "active",
            order: 15,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "fire",
            health_safety_category_id : 2,
            field: "Policy of fighting fires",
            status: "active",
            order: 16,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "fire",
            health_safety_category_id : 2,
            field: "Purpose of fire doors",
            status: "active",
            order: 17,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "fire",
            health_safety_category_id : 2,
            field:
              "Fire extinguishers (Type, locations & types of fire they can be used on)",
            status: "active",
            order: 18,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "firstaid",
            health_safety_category_id : 3,
            field: "First aid kit locations",
            status: "active",
            order: 19,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "firstaid",
            health_safety_category_id : 3,
            field: "Accident book location",
            status: "active",
            order: 20,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "firstaid",
            health_safety_category_id : 3,
            field: "Reporting accidents & near misses",
            status: "active",
            order: 21,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "firstaid",
            health_safety_category_id : 3,
            field: "How to obtain a first alder",
            status: "active",
            order: 22,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "siteissue",
            health_safety_category_id : 4,
            field: "Silps, Trips & Falls",
            status: "active",
            order: 23,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "siteissue",
            health_safety_category_id : 4,
            field: "Lifts (When to use/when not to use)",
            status: "active",
            order: 24,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "siteissue",
            health_safety_category_id : 4,
            field: "Manual handling/lifting & Carrying",
            status: "active",
            order: 25,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "siteissue",
            health_safety_category_id : 4,
            field: "Computers & workstations",
            status: "active",
            order: 26,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "siteissue",
            health_safety_category_id : 4,
            field: "Safe use of epquipments/machinery",
            status: "active",
            order: 27,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            type: "siteissue",
            health_safety_category_id : 4,
            field: "Personal protective equipment",
            status: "active",
            order: 28,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        {},
      );
    }
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
  },
};
