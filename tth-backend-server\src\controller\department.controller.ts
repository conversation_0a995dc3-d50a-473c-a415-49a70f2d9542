import { Request, Response } from "express";
import { Op, Sequelize } from "sequelize";
import { Department, department_status } from "../models/Department";
import { isRecordReferencedInAnyTable, permittedForAdmin, permittedForAdminEnhanced, validateModulePermission } from "../helper/common";
import { StatusCodes } from "http-status-codes";
import { getPaginatedItems, getPagination } from "../helper/utils";
import { ROLE_CONSTANT, ROLE_PERMISSIONS } from "../helper/constant";
import { User } from "../models/User";
import { Role } from "../models/Role";
import { EmpContractTemplate } from "../models/EmployeeContractTemplate";
import { UserMeta } from "../models/UserMeta";
import {
  EmpContractCategory,
  status,
} from "../models/EmployeeContractCategory";

/**
 * Add department to a branch.
 * @param req
 * @param res
 * @returns
 */

const addDepartment = async (req: Request, res: Response) => {
  try {
    const {
      department_name = "",
      department_remark,
      departmentStatus,
    } = req.body;
    // Try new MORole-based permission system first, then fallback to old system
    const checkModulePermission = await validateModulePermission(
      req.user,
      req.user.organization_id,
      'department', // Department module slug
      ROLE_PERMISSIONS.CREATE
    );

    // Enhanced admin permission check (combines both old and new systems)
    // const checkAdminPermission = await permittedForAdminEnhanced(
    //   req.user?.id,
    //   req.user.organization_id,
    //   [
    //     ROLE_CONSTANT.SUPER_ADMIN,
    //     ROLE_CONSTANT.ADMIN,
    //     ROLE_CONSTANT.DIRECTOR,
    //     ROLE_CONSTANT.HR,
    //     ROLE_CONSTANT.BRANCH_MANAGER,
    //     ROLE_CONSTANT.HOTEL_MANAGER,
    //   ]
    // );

    // User has permission if either check passes
    const hasPermission = checkModulePermission; // || checkAdminPermission;

    if (!hasPermission)
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });

    const findDepartmentByName = await Department.findOne({
      where: {
        department_name,
        department_status: { [Op.not]: department_status.DELETED },
        organization_id: req.user.organization_id,
      }, raw: true
    });
    if (findDepartmentByName) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("DEPARTMENT_NAME_EXIST") });
    } else {
      const createDepartment = await Department.setHeaders(req).create({
        department_name: department_name,
        department_remark,
        department_status: departmentStatus,
        organization_id: req.user.organization_id,
        created_by: req.user.id,
        updated_by: req.user.id,
      } as any);
      if (createDepartment) {
        return res.status(StatusCodes.CREATED).json({
          status: true,
          message: res.__("DEPARTMENT_CREATION_SUCCESSED"),
          data: createDepartment,
        });
      } else {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("DEPARTMENT_CREATION_FAILED"),
        });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * Update department details
 * @param req
 * @param res
 * @returns
 */

const updatedDepartment = async (req: Request, res: Response) => {
  try {
    const checkPermission = await 
    validateModulePermission(
      req.user,
      req.user.organization_id,
      'department', // Department module slug
      ROLE_PERMISSIONS.EDIT
    );
    if (!checkPermission)
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    const {
      department_name = "",
      department_remark = "",
      departmentStatus,
    } = req.body;
    const { department_id }: any = req.params;

    /** check department exist or not */
    const checkDepartmentExist = await Department.findOne({
      attributes: ['id'],
      where: {
        id: department_id,
        department_status: { [Op.not]: department_status.DELETED },
        organization_id: req.user.organization_id
      }, raw: true
    });
    if (!checkDepartmentExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("DEPARTMENT_NOT_FOUND") });
    }
    const updateObj: any = {
      department_name: department_name,
      department_remark: department_remark,
      updated_by: req.user.id,
      department_status: departmentStatus,
    };

    const updateDepartment = await Department.setHeaders(req).update(
      updateObj,
      {
        where: { id: checkDepartmentExist.id },
      },
    );
    if (updateDepartment.length > 0) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("DEPARTMENT_UPDATION_SUCCESSED"),
      });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("DEPARTMENT_UPDATION_FAILED") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * Get all department List
 * @param req
 * @param res
 * @returns
 */

const getAllDepartment = async (req: Request, res: Response) => {
  try {
    const { page, size, search, departmentStatus }: any = req.query;
    let { limit, offset }: any = getPagination(page, size);
    limit = Number(limit);
    offset = Number(offset);
    if (departmentStatus != department_status.ACTIVE) {
      // Try new MORole-based permission system first, then fallback to old system
      const checkModulePermission = await validateModulePermission(
        req.user,
        req.user.organization_id,
        'department', // Department module slug
        ROLE_PERMISSIONS.VIEW
      );

      // Enhanced admin permission check (combines both old and new systems)
      // const checkAdminPermission = await permittedForAdminEnhanced(
      //   req.user?.id,
      //   req.user.organization_id,
      //   [
      //     ROLE_CONSTANT.SUPER_ADMIN,
      //     ROLE_CONSTANT.ADMIN,
      //     ROLE_CONSTANT.DIRECTOR,
      //     ROLE_CONSTANT.HR,
      //     ROLE_CONSTANT.BRANCH_MANAGER,
      //     ROLE_CONSTANT.HOTEL_MANAGER,
      //   ]
      // );

      // User has permission if either check passes
      const hasPermission = checkModulePermission; // || checkAdminPermission;

      if (!hasPermission)
        return res
          .status(StatusCodes.FORBIDDEN)
          .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }
    /** check user exist or not else throw error */
    const findUser = await User.findOne({ where: { id: req.user.id, organization_id: req.user.organization_id }, attributes: ['id', 'department_id', 'web_user_active_role_id', 'user_active_role_id'], raw: true })

    if (!findUser) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const findCurrentUserRole: any = await Role.findOne({
      where: {
        id:
          req.headers["platform-type"] == "web"
            ? findUser?.web_user_active_role_id
            : findUser?.user_active_role_id,
      },
      raw: true,
    });
    const whereObj: any = {
      department_status: { [Op.not]: department_status.DELETED },
      organization_id: req.user.organization_id,
    };

    if (search) {
      whereObj.department_name = { [Op.like]: `%${search}%` };
    }
    const departmentListObj: any = { where: whereObj, raw: true, nest: true };
    if (page && size) {
      departmentListObj.limit = limit;
      departmentListObj.offset = offset;
    }
    if (departmentStatus) {
      whereObj.department_status = departmentStatus;
    }
    if (
      findCurrentUserRole?.role_name == ROLE_CONSTANT.BRANCH_MANAGER &&
      findUser?.department_id &&
      findCurrentUserRole?.role_name == ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER
    ) {
      whereObj.id = findUser?.department_id;
    }
    const getDepartmentList: any = await Department.findAll(departmentListObj);
    const count = await Department.count({ where: whereObj });
    const { total_pages } = getPaginatedItems(size, page, count || 0);
    return res.status(StatusCodes.OK).json({
      status: true,
      data: getDepartmentList,
      message: res.__("SUCCESS_FETCHED"),
      count: count,
      page: parseInt(page),
      size: parseInt(size),
      total_pages,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * Delete department by id
 * @param req
 * @param res
 * @returns
 */

const deleteDepartment = async (req: Request, res: Response) => {
  try {
    const { department_id } = req.params;
    // Try new MORole-based permission system first, then fallback to old system
    const checkModulePermission = await validateModulePermission(
      req.user,
      req.user.organization_id,
      'department', // Department module slug
      ROLE_PERMISSIONS.DELETE
    );

    // Enhanced admin permission check (combines both old and new systems)
    // const checkAdminPermission = await permittedForAdminEnhanced(
    //   req.user?.id,
    //   req.user.organization_id,
    //   [
    //     ROLE_CONSTANT.SUPER_ADMIN,
    //     ROLE_CONSTANT.ADMIN,
    //     ROLE_CONSTANT.DIRECTOR,
    //     ROLE_CONSTANT.HR,
    //     ROLE_CONSTANT.BRANCH_MANAGER,
    //     ROLE_CONSTANT.HOTEL_MANAGER,
    //   ]
    // );

    // User has permission if either check passes
    const hasPermission = checkModulePermission; // || checkAdminPermission;

    if (!hasPermission)
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    // check if the user is already in use or not
    const findDepartmentList = await Department.findOne({
      where: {
        id: department_id,
        department_status: { [Op.not]: department_status.DELETED },
        organization_id: req.user.organization_id
      }, attributes: ['id'], raw: true
    });
    if (!findDepartmentList) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("DEPARTMENT_NOT_FOUND") });
    }

    const isReferenced = await isRecordReferencedInAnyTable(Department.tableName, department_id);

    if (isReferenced) {
      return res.status(400).json({ message: res.__("ERROR_RECORD_IN_USE_TABLES") });
    }

    const findDepartmentCategory = await EmpContractCategory.findAll({ attributes: ['id'], where: { department_id: findDepartmentList.id, status: status.ACTIVE, organization_id: req.user.organization_id }, raw: true })
    if (findDepartmentCategory.length > 0) {
      const findTemplates = await EmpContractTemplate.findAll({ attributes: ['id'], where: { category_id: { [Op.in]: findDepartmentCategory.map(category => category.id) } }, raw: true })
      if (findTemplates.length > 0) {
        const findTemplatesIds = findTemplates.map((template) => template.id);
        const findIsUsed = await UserMeta.findAll({
          where: {
            [Op.or]: [
              { general_template: { [Op.in]: findTemplatesIds } },
              {
                department_template: {
                  [Op.in]: findTemplatesIds,
                },
              },
              Sequelize.literal(
                `(${findTemplatesIds
                  .map((id) => `FIND_IN_SET(${id}, additional_template) > 0`)
                  .join(" OR ")})`,
              ),
            ],
          },
        });
        if (findIsUsed.length > 0) {
          const plural = findIsUsed.length == 1 ? "" : "s";
          return res
            .status(StatusCodes.BAD_REQUEST)
            .send({
              status: false,
              message: res.__("CANNOT_DELETE_DEPARTMENT_USE_IN_CONTRACT", {
                userLength: findIsUsed.length,
                plural,
              }),
            });
        }
      }
    }
    const deleteDepartment = await Department.setHeaders(req).update(
      { department_status: department_status.DELETED },
      { where: { id: findDepartmentList.id } },
    );
    if (deleteDepartment.length > 0) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("DEPARTMENT_DELETATION_SUCCESSED"),
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("DEPARTMENT_DELETATION_FAILED"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * Get department by id
 * @param req
 * @param res
 * @returns
 */

const getDepartmentById = async (req: Request, res: Response) => {
  try {
    const { department_id } = req.params;
    // Try new MORole-based permission system first, then fallback to old system
    const checkModulePermission = await validateModulePermission(
      req.user,
      req.user.organization_id,
      'department', // Department module slug
      ROLE_PERMISSIONS.VIEW
    );

    // Enhanced admin permission check (combines both old and new systems)
    // const checkAdminPermission = await permittedForAdminEnhanced(
    //   req.user?.id,
    //   req.user.organization_id,
    //   [
    //     ROLE_CONSTANT.SUPER_ADMIN,
    //     ROLE_CONSTANT.ADMIN,
    //     ROLE_CONSTANT.DIRECTOR,
    //     ROLE_CONSTANT.HR,
    //     ROLE_CONSTANT.BRANCH_MANAGER,
    //     ROLE_CONSTANT.HOTEL_MANAGER,
    //   ]
    // );

    // User has permission if either check passes
    const hasPermission = checkModulePermission; // || checkAdminPermission;

    if (!hasPermission) {
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }
    const getDepartmentById = await Department.findOne({
      where: {
        id: department_id,
        department_status: { [Op.not]: department_status.DELETED },
        organization_id: req.user.organization_id
      },
      raw: true,
      nest: true,
    });
    if (getDepartmentById) {
      return res.status(StatusCodes.OK).json({
        status: true,
        data: getDepartmentById,
        message: res.__("SUCCESS_FETCHED"),
      });
    } else {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("DEPARTMENT_NOT_FOUND"),
        data: [],
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};
export default {
  addDepartment,
  updatedDepartment,
  getAllDepartment,
  deleteDepartment,
  getDepartmentById,
};
