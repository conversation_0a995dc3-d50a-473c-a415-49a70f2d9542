"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { DsrDetail } from "./DsrDetail";
import { DocumentCategoryBranch } from "./DocumentCategoryBranch";
import { HealthSafetyCategoryItem } from "./HealthSafetyCategoryItem";
import { WsrDetail } from "./WsrDetail";
import { ExpenseDetail } from "./ExpenseDetail";
import { Forecast } from "./Forecast";
import { UserBranch } from "./UserBranch";

interface branchAttributes {
  id: number;
  branch_name: string;
  branch_status: string;
  branch_remark: string;
  branch_sign: string;
  branch_employer_name: string;
  branch_color: string;
  branch_heading_employer_name: string;
  branch_heading_name: string;
  branch_heading_work_place: string;
  branch_work_place: string;
  registration_number: string
  organization_id: string
  text_color: string;
  created_by: number;
  updated_by: number;
}

/** Role enum  for status*/
export enum branch_status {
  ACTIVE = "active",
  DRAFT = "draft",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export class Branch
  extends Model<branchAttributes, never>
  implements branchAttributes {
  id!: number;
  branch_name!: string;
  branch_status!: string;
  branch_remark!: string;
  branch_sign!: string;
  branch_employer_name!: string;
  branch_color!: string;
  branch_heading_employer_name!: string;
  branch_heading_name!: string;
  branch_heading_work_place!: string;
  branch_work_place!: string;
  registration_number!: string
  organization_id!: string
  text_color!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

Branch.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    branch_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    branch_remark: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
    },
    branch_status: {
      type: DataTypes.ENUM,
      values: Object.values(branch_status),
      defaultValue: branch_status.ACTIVE,
      allowNull: false,
    },
    branch_sign: {
      type: DataTypes.STRING,
    },
    branch_employer_name: {
      type: DataTypes.STRING,
    },
    branch_color: {
      type: DataTypes.STRING,
    },
    branch_heading_employer_name: {
      type: DataTypes.STRING,
    },
    branch_heading_name: {
      type: DataTypes.STRING,
    },
    branch_heading_work_place: {
      type: DataTypes.STRING,
    },
    branch_work_place: {
      type: DataTypes.STRING,
    },
    registration_number: {
      type: DataTypes.STRING,
      allowNull: true
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    text_color: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_branches",
    modelName: "Branch",
  },
);

DsrDetail.belongsTo(Branch, { foreignKey: "branch_id", as: "dsr_branch" });
Branch.hasMany(DsrDetail, { foreignKey: "branch_id", as: "branch" });
Branch.hasMany(DocumentCategoryBranch, { foreignKey: "branch_id", as: "branch_wise" });
DocumentCategoryBranch.belongsTo(Branch, { foreignKey: "branch_id", as: "category_branches" });
WsrDetail.belongsTo(Branch, { foreignKey: "branch_id", as: "wsr_branch" });
Branch.hasMany(WsrDetail, { foreignKey: "branch_id", as: "branch_wsr" });

ExpenseDetail.belongsTo(Branch, { foreignKey: "branch_id", as: "expense_branch" });
Branch.hasMany(ExpenseDetail, { foreignKey: "branch_id", as: "branch_expense" });

Branch.hasMany(HealthSafetyCategoryItem, {
  foreignKey: "branch_id",
  as: "document_category_branch",
});
HealthSafetyCategoryItem.belongsTo(Branch, {
  foreignKey: "branch_id",
  as: "health_safety_category_item_branch",
});

Forecast.belongsTo(Branch, { foreignKey: "branch_id", as: "forecast_branch" });
Branch.hasMany(Forecast, { foreignKey: "branch_id", as: "branch_forecast" });
UserBranch.belongsTo(Branch, { foreignKey: "branch_id", as: "branch" });
Branch.hasMany(UserBranch, { foreignKey: "branch_id", as: "user_branch" });

// Define hooks for Branch model
Branch.addHook("afterUpdate", async (branch: any) => {
  await addActivity("Branch", "updated", branch);
});

Branch.addHook("afterCreate", async (branch: Branch) => {
  await addActivity("Branch", "created", branch);
});




Branch.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

