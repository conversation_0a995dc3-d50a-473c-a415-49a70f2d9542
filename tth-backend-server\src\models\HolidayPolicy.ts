"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { HolidayType } from "./HolidayType";

interface holidayPolicyAttributes {
  id: number;
  holiday_policy_name: string;
  holiday_policy_colour: string;
  holiday_policy_description: string;
  holiday_policy_start_date: string;
  holiday_policy_end_date: string;
  holiday_policy_status: string;
  has_leave_reprocess: boolean;
  holiday_type_id: number;
  created_by: number;
  updated_by: number;
}

export enum holiday_policy_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DELETED = "deleted",
}

export class HolidayPolicy
  extends Model<holidayPolicyAttributes, never>
  implements holidayPolicyAttributes {
  id!: number;
  holiday_policy_name!: string;
  holiday_policy_colour!: string;
  holiday_policy_description!: string;
  holiday_policy_start_date!: string;
  holiday_policy_end_date!: string;
  holiday_policy_status!: string;
  has_leave_reprocess!: boolean;
  holiday_type_id!: number;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

HolidayPolicy.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    holiday_policy_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    holiday_policy_colour: {
      type: DataTypes.STRING,
      allowNull: true
    },
    holiday_policy_description: {
      type: DataTypes.TEXT('long'),
      allowNull: true
    },
    holiday_policy_start_date: {
      type: DataTypes.DATE,
      allowNull: false
    },
    holiday_policy_end_date: {
      type: DataTypes.DATE,
      allowNull: false
    },
    holiday_policy_status: {
      type: DataTypes.ENUM,
      values: Object.values(holiday_policy_status),
      defaultValue: holiday_policy_status.ACTIVE,
    },
    has_leave_reprocess: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    holiday_type_id: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_holiday_policy",
    modelName: "HolidayPolicy",
  },
);


HolidayPolicy.belongsTo(HolidayType, { foreignKey: "holiday_type_id", as: "holiday_policy" });
HolidayType.hasMany(HolidayPolicy, { foreignKey: "holiday_type_id", as: "holiday_type" });

// Define hooks for Card model
HolidayPolicy.addHook("afterUpdate", async (holidayPolicy: any) => {
  await addActivity("HolidayPolicy", "updated", holidayPolicy);
});

HolidayPolicy.addHook("afterCreate", async (holidayPolicy: HolidayPolicy) => {
  await addActivity("HolidayPolicy", "created", holidayPolicy);
});

HolidayPolicy.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
