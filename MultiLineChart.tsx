import React, { useState, useEffect } from 'react';
import {
    <PERSON><PERSON>hart,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    <PERSON>lt<PERSON>,
    Legend,
    ResponsiveContainer
} from 'recharts';

interface ChartData {
    month: string;
    dsr: number;
    wsr: number;
    expense: number;
}

interface MultiLineChartProps {
    year?: number;
    branchId?: string;
}

const MultiLineChart: React.FC<MultiLineChartProps> = ({ year, branchId }) => {
    const [chartData, setChartData] = useState<ChartData[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        fetchChartData();
    }, [year, branchId]);

    const fetchChartData = async () => {
        try {
            setLoading(true);
            setError(null);

            // Build query parameters
            const params = new URLSearchParams();
            if (year) params.append('year', year.toString());
            if (branchId) params.append('branch_id', branchId);

            const response = await fetch(`/api/report/multi-line-chart?${params}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    // Add your authentication headers here
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                },
            });

            if (!response.ok) {
                throw new Error('Failed to fetch chart data');
            }

            const result = await response.json();

            if (result.status) {
                setChartData(result.data);
            } else {
                setError(result.message || 'Failed to fetch data');
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred');
        } finally {
            setLoading(false);
        }
    };

    // Custom tooltip formatter
    const formatTooltip = (value: any, name: string) => {
        return [`$${value?.toLocaleString()}`, name.toUpperCase()];
    };

    // Custom legend formatter
    const formatLegend = (value: string) => {
        return value.toUpperCase();
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="text-lg">Loading chart data...</div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="text-red-500">Error: {error}</div>
            </div>
        );
    }

    return (
        <div className="w-full h-96 p-4 bg-white rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">
                DSR, WSR & Expense Overview - {year || new Date().getFullYear()}
            </h2>

            <ResponsiveContainer width="100%" height="100%">
                <LineChart
                    data={chartData}
                    margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 20,
                    }}
                >
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />

                    <XAxis
                        dataKey="month"
                        stroke="#666"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                    />

                    <YAxis
                        stroke="#666"
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                        tickFormatter={(value) => `$${value.toLocaleString()}`}
                    />

                    <Tooltip
                        formatter={formatTooltip}
                        labelStyle={{ color: '#333' }}
                        contentStyle={{
                            backgroundColor: 'white',
                            border: '1px solid #ccc',
                            borderRadius: '4px',
                            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                        }}
                    />

                    <Legend
                        formatter={formatLegend}
                        wrapperStyle={{ paddingTop: '10px' }}
                    />

                    {/* DSR Line */}
                    <Line
                        type="monotone"
                        dataKey="dsr"
                        stroke="#3B82F6"
                        strokeWidth={3}
                        dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, stroke: '#3B82F6', strokeWidth: 2 }}
                        name="DSR"
                    />

                    {/* WSR Line */}
                    <Line
                        type="monotone"
                        dataKey="wsr"
                        stroke="#10B981"
                        strokeWidth={3}
                        dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, stroke: '#10B981', strokeWidth: 2 }}
                        name="WSR"
                    />

                    {/* Expense Line */}
                    <Line
                        type="monotone"
                        dataKey="expense"
                        stroke="#EF4444"
                        strokeWidth={3}
                        dot={{ fill: '#EF4444', strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, stroke: '#EF4444', strokeWidth: 2 }}
                        name="Expense"
                    />
                </LineChart>
            </ResponsiveContainer>

            {/* Summary Stats */}
            <div className="grid grid-cols-3 gap-4 mt-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                        ${chartData.reduce((sum, item) => sum + item.dsr, 0).toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">Total DSR</div>
                </div>

                <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                        ${chartData.reduce((sum, item) => sum + item.wsr, 0).toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">Total WSR</div>
                </div>

                <div className="text-center p-3 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">
                        ${chartData.reduce((sum, item) => sum + item.expense, 0).toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">Total Expense</div>
                </div>
            </div>
        </div>
    );
};

export default MultiLineChart; 