import passwordHash from "pbkdf2-password-hash";
import moment from "moment";
import { NextFunction, Request, Response } from "express";
import cryptoJs from "crypto";
import { Session, sessions } from "./session.service";
import multer from "multer";
import path from "path";
import fs from "fs";

const generateOtp = async (n: number) => {
  const val =
    Math.floor(Math.random() * (9 * Math.pow(10, n - 1))) + Math.pow(10, n - 1);
  return val;
};

const generateSession = async (
  req: Request,
  res: Response,
  next?: NextFunction,
) => {
  const sessionToken = cryptoJs.randomUUID().toString();

  const expiresAt = moment()
    .add(global.config.SESSION_TIME, "seconds")
    .toDate();

  // create a session containing information about the user and expiry time
  const session = new Session(expiresAt);
  // add the session information to the sessions map
  sessions[sessionToken] = session;

  // In the response, set a cookie on the client with the name "session_cookie"
  // and the value as the UUID we generated. We also set the expiry time
  res.cookie("secure_session", sessionToken);
  if (next) {
    next();
  } else {
    return;
  }
};

const logOutSessionHandle = async (req: Request, res: Response) => {
  if (!req.cookies) {
    res.status(401).end();
    return;
  }

  const sessionToken = req.cookies["secure_session"];
  if (!sessionToken) {
    res.status(401).end();
    return;
  }

  delete sessions[sessionToken];

  res.cookie("secure_session", "", { expires: new Date() });
};

const refreshSessionHandle = async (req: Request, res: Response) => {
  if (!req.cookies) {
    res.status(401).end();
    return;
  }

  const sessionToken = req.cookies["secure_session"];
  if (!sessionToken) {
    res.status(401).end();
    return;
  }

  const userSession: Session | undefined = sessions[sessionToken];
  if (!userSession) {
    res.status(401).end();
    return;
  }
  if (userSession.isExpired()) {
    delete sessions[sessionToken];
    res.status(401).end();
    return;
  }

  await generateSession(req, res);

  delete sessions[sessionToken];

  res.end();
};

const encrypt = async (password: string) => {
  return await passwordHash.hash(password, {
    iterations: 100,
    digest: "sha1",
    keylen: 16,
    saltlen: 16,
  });
};

/** validate phone number */
const validatePhoneNumber = async (phone: string) => {
  const phoneRegax =
    // eslint-disable-next-line no-useless-escape
    /^[\+]?([0-9][\s]?|[0-9]?)([(][0-9]{3}[)][\s]?|[0-9]{3}[-\s\.]?)[0-9]{3}[-\s\.]?[0-9]{4,6}$/im;
  const match = phoneRegax.test(phone);
  if (match) {
    return true;
  } else {
    return false;
  }
};

const otpExpTime = async () => {
  const time = moment(new Date())
    .add(global.config.OTP_EXPIRE_TIME, "seconds")
    .format("YYYY-MM-DD HH:mm:ss");
  return time;
};

const comparePassword = async (
  plainPassword: string,
  hashedPassword: string,
) => {
  return await passwordHash.compare(plainPassword, hashedPassword);
};

const getPagination = (page: number, size: number) => {
  const limit = size;
  const Page = page || 1;
  const offset = (Page - 1) * limit;
  return { limit, offset };
};

function getPaginatedItems(
  pageSize: number,
  pageNumber: number,
  total: number,
) {
  return {
    pageNumber: Number(pageNumber),
    per_page: Number(pageSize),
    total: total,
    total_pages: Math.ceil(Number(total) / Number(pageSize)),
  };
}

const uploadMulter = (destination_path: string) => {
  const storage = multer.diskStorage({
    destination: (req: any, file: any, cb: any) => {
      const onbordingPath = path.resolve(__dirname, "../uploads/", destination_path);
      try {
        if (!fs.existsSync(onbordingPath)) {
          fs.mkdirSync(onbordingPath, { recursive: true });
        }
      } catch (error) {
        console.log("error", error);
      }

      cb(null, onbordingPath);
    },
    filename: (req: any, file: any, cb: any) => {
      cb(null, Date.now() + "-" + file.originalname); // File naming
    },
  });

  // Initialize Multer with the storage configuration
  return multer({ storage: storage });
}

const getStartDate = (policyType: string, count: number, date: string) => {
  const appliedDate = moment(date); // Use the applied leave's date

  switch (policyType) {
    case "days":
      return { date: appliedDate.subtract(count, "days").format("YYYY-MM-DD"), type: "days" };
    case "weekly":
      return { date: appliedDate.startOf("week").format("YYYY-MM-DD"), type: "week" };
    case "monthly":
      return { date: appliedDate.startOf("month").format("YYYY-MM-DD"), type: "month" };
    case "quarterly":
      return {
        date: appliedDate.startOf("quarter").format("YYYY-MM-DD"), type: "quarter"
      };
    case "yearly":
      return { date: appliedDate.startOf("year").format("YYYY-MM-DD"), type: "year" };
    default:
      throw new Error("Invalid leave policy type");
  }
};


function formatNumber(number: number) {
  return number ? Number(Number(number).toFixed(2)) : 0;
}

function formatPercentage(number: number) {
  if (Number(number) > 100) {
    return formatNumber(100)
  } else {
    return formatNumber(number)
  }
}

export {
  generateOtp,
  validatePhoneNumber,
  encrypt,
  otpExpTime,
  comparePassword,
  getPagination,
  getPaginatedItems,
  generateSession,
  logOutSessionHandle,
  refreshSessionHandle,
  uploadMulter,
  getStartDate,
  formatNumber,
  formatPercentage
};
