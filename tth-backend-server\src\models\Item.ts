"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

interface itemAttributes {
  id: number;
  item_type: string;
  item_name: string;
  item_hash: string;
  item_mime_type: string;
  item_extension: string;
  item_size: number;
  item_IEC: string;
  item_location: string;
  item_external_location: string;
  item_organization_id: string;
  item_category: string;
  item_status: string;
  created_by: number;
  updated_by: number;
}

export enum item_status {
  ACTIVE = "active",
  DELETED = "deleted",
}

export enum item_type {
  VIDEO = "video",
  AUDIO = "audio",
  IMAGE = "image",
  LINK = "link",
  TEXT = "text",
  BLOB = "blob",
  PDF = "pdf",
}

export enum item_external_location {
  YES = "yes",
  NO = "no",
}

export enum item_category {
  PROFILE = "profile",
  ITEM = "item",
  DOCUMENT = "document",
  SIGNATURE = "signature",
  OTHER = "other",
  RECIPE_IMAGE = "recipe_image",
  RECIPE_VIDEO = "recipe_video",
  RECIPE_DOCUMENT = "recipe_document",
  INGREDIENT_IMAGE = "ingredient_image",
  CATEGORY_ICON = "category_icon",
  ATTRIBUTE_ICON = "attribute_icon",
  UNIT_ICON = "unit_icon",
}

export enum item_IEC {
  B = "B",
  KB = "KB",
  MB = "MB",
  GB = "GB",
  TB = "TB",
  PB = "PB",
  EB = "EB",
  ZB = "ZB",
  YB = "YB",
  IB = "iB",
  KIB = "KiB",
  MIB = "MiB",
  GIB = "GiB",
  TIB = "TiB",
  PIB = "PiB",
  EIB = "EiB",
  ZIB = "ZiB",
  YIB = "YiB",
}

export class Item
  extends Model<itemAttributes, never>
  implements itemAttributes {
  id!: number;
  item_type!: string;
  item_name!: string;
  item_hash!: string;
  item_mime_type!: string;
  item_extension!: string;
  item_size!: number;
  item_IEC!: string;
  item_location!: string;
  item_external_location!: string;
  item_organization_id!: string;
  item_category!: string;
  item_status!: string;
  created_by!: number;
  updated_by!: number;
}

Item.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    item_type: {
      type: DataTypes.ENUM,
      values: Object.values(item_type),
      allowNull: false,
    },
    item_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    item_hash: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    item_mime_type: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    item_extension: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    item_size: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    item_IEC: {
      type: DataTypes.ENUM,
      values: Object.values(item_IEC),
      allowNull: true,
    },
    item_location: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    item_external_location: {
      type: DataTypes.ENUM,
      values: Object.values(item_external_location),
      defaultValue: item_external_location.NO,
    },
    item_organization_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    item_category: {
      type: DataTypes.ENUM,
      values: Object.values(item_category),
      defaultValue: item_category.ITEM,
    },
    item_status: {
      type: DataTypes.ENUM,
      values: Object.values(item_status),
      defaultValue: item_status.ACTIVE,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "nv_items",
    modelName: "Item",
    timestamps: true,
  },
);
